# SEO-Friendly Event URLs

This document describes the implementation of SEO-friendly URLs for event detail pages.

## URL Structure

### New Format (SEO-Friendly)
```
/events/[category]/[event-title]/[id]
```

**Examples:**
- `/events/conferences/annual-developer-conference/cmaqv9i6c000grbxwii1gsv1v`
- `/events/music/summer-music-festival/xyz123abc`
- `/events/business/networking-night/def456ghi`

### Legacy Format (Backward Compatibility)
```
/events/[id]
```

**Examples:**
- `/events/cmaqv9i6c000grbxwii1gsv1v`
- `/events/xyz123abc`

## Implementation Details

### URL Generation
The new URL structure is generated using the `generateEventUrl()` function:

```typescript
import { generateEventUrl } from '@/lib/utils/events';

const event = {
  id: 'cmaqv9i6c000grbxwii1gsv1v',
  title: 'Annual Developer Conference',
  category: 'CONFERENCES_AND_WORKSHOPS'
};

const url = generateEventUrl(event);
// Returns: /events/conferences/annual-developer-conference/cmaqv9i6c000grbxwii1gsv1v
```

### Category Slug Mapping
Event categories are mapped to SEO-friendly slugs:

| Category | Slug |
|----------|------|
| CONFERENCES_AND_WORKSHOPS | conferences |
| MUSIC | music |
| BUSINESS | business |
| TECHNOLOGY | technology |
| SPORTS_AND_FITNESS | sports |
| ... | ... |

### Title Slug Generation
Event titles are converted to URL-friendly slugs:
- Converted to lowercase
- Spaces and special characters replaced with hyphens
- Leading/trailing hyphens removed

**Examples:**
- "Annual Developer Conference" → "annual-developer-conference"
- "Music & Arts Festival!" → "music-arts-festival"
- "Tech Summit 2024" → "tech-summit-2024"

## Routing

### New Route Structure
```
src/app/events/[category]/[title]/[id]/page.tsx
```

This route:
1. Validates the URL structure
2. Fetches the event data
3. Verifies category and title match the event
4. Redirects to canonical URL if needed
5. Renders the event details

### Legacy Route (Redirect)
```
src/app/events/[id]/page.tsx
```

This route:
1. Fetches event data by ID
2. Generates the new SEO-friendly URL
3. Redirects to the new URL format

## Backward Compatibility

### Automatic Redirects
- All old URLs (`/events/[id]`) automatically redirect to new format
- 301 redirects preserve SEO value
- No broken links for existing bookmarks or external links

### API Support
- New API endpoint: `/api/events/by-url` supports both URL formats
- Existing APIs continue to work unchanged
- URL parsing handles both old and new formats

## SEO Benefits

### Improved URL Structure
- **Descriptive URLs**: URLs now contain meaningful information
- **Category Organization**: Events grouped by category in URL structure
- **Keyword Rich**: Event titles in URLs improve search relevance

### Technical SEO
- **Canonical URLs**: Proper canonical tags prevent duplicate content
- **Structured Data**: Enhanced metadata for search engines
- **Social Sharing**: Better preview cards with descriptive URLs

### Examples of SEO Improvements

**Before:**
```
https://example.com/events/cmaqv9i6c000grbxwii1gsv1v
```

**After:**
```
https://example.com/events/conferences/annual-developer-conference/cmaqv9i6c000grbxwii1gsv1v
```

## Usage in Components

### EventCard Component
```typescript
import { generateEventUrl } from '@/lib/utils/events';

<Link href={generateEventUrl({ id: event.id, title: event.title, category: event.category })}>
  View Event
</Link>
```

### EventLink Component
```typescript
import EventLink from '@/components/ui/EventLink';

<EventLink event={event}>
  <Button>View Event Details</Button>
</EventLink>
```

## Testing

Run the test suite to verify URL generation:

```bash
npm test src/lib/utils/__tests__/events.test.ts
```

## Migration Notes

### For Developers
1. Update all event links to use `generateEventUrl()`
2. Use `EventLink` component for consistent URL generation
3. Test both old and new URL formats

### For Content
1. Old URLs continue to work (redirect automatically)
2. New URLs are automatically generated for all events
3. Social media sharing uses new URL format

### For SEO
1. Submit new sitemap with updated URLs
2. Update internal links to use new format
3. Monitor search console for crawl errors

## Configuration

### Category Mapping
Update category slugs in `src/lib/utils/events.ts`:

```typescript
export const categorySlugMap: Record<string, string> = {
  'NEW_CATEGORY': 'new-category-slug',
  // ... existing mappings
};
```

### URL Patterns
Modify URL generation logic in `generateEventUrl()` function if needed.

## Troubleshooting

### Common Issues
1. **404 Errors**: Ensure event is published and ID is correct
2. **Redirect Loops**: Check canonical URL generation
3. **Category Mismatches**: Verify category slug mapping

### Debug Tools
- Use `parseEventUrl()` to test URL parsing
- Check browser network tab for redirect chains
- Verify canonical tags in page source
