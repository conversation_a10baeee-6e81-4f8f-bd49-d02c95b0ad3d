# Admin User Seeding

This document explains how to set up and use the admin user seeding functionality in the application.

## Overview

The application includes functionality to automatically create a default admin user in the database if one doesn't exist. This ensures that there's always an administrative account available when the application is first deployed or when the database is reset.

## Default Admin Credentials

The default admin credentials are defined in the `.env` file:

```
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=Admin@123456
DEFAULT_ADMIN_NAME=System Administrator
```

You should change these values in your production environment for security reasons.

## How Admin Seeding Works

The admin seeding can happen in three ways:

1. **Automatic seeding during application startup**: The application checks for an admin user when it starts and creates one if needed.

2. **Manual seeding using the Prisma seed command**: You can run `npm run prisma:seed` to seed the database, which includes creating the admin user.

3. **Manual seeding using the dedicated admin seed script**: You can run `npm run seed:admin` to specifically create the admin user.

## When to Use Each Method

- **Automatic seeding**: This happens by default and requires no action. It's useful for ensuring an admin user exists in production environments.

- **Prisma seed command**: Use this when you're setting up a new database and want to seed it with all initial data, including the admin user.

- **Admin seed script**: Use this when you specifically want to create or verify the admin user without affecting other data.

## Customizing Admin Credentials

To customize the admin credentials:

1. Edit the `.env` file and update the following variables:
   ```
   DEFAULT_ADMIN_EMAIL=<EMAIL>
   DEFAULT_ADMIN_PASSWORD=your-secure-password
   DEFAULT_ADMIN_NAME=Your Name
   ```

2. Restart the application or run one of the seeding commands.

## Security Considerations

- Change the default admin credentials in production environments.
- Use a strong password for the admin account.
- Consider using environment-specific credentials for different deployment environments.
- The admin password is securely hashed before being stored in the database.

## Troubleshooting

If you encounter issues with admin seeding:

1. Check the server logs for any error messages.
2. Verify that the `.env` file contains the correct admin credentials.
3. Ensure that the database connection is working properly.
4. Try running the manual seeding command: `npm run seed:admin`
