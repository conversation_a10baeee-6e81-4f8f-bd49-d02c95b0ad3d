# API Pricing System

This document provides information about the API pricing system, including how to set up and customize the pricing tiers.

## Overview

The API pricing system provides a way to display and manage pricing tiers for the API. It includes:

- A visual pricing component with monthly/yearly billing options
- Database storage for pricing tiers
- Seeding scripts to populate the database with default pricing tiers
- Utility functions to fetch and format pricing information

## API Tiers

The system defines four pricing tiers:

1. **Free** - Basic access for developers
2. **Basic** - For small applications
3. **Professional** - For growing businesses (popular choice)
4. **Enterprise** - For large organizations

Each tier has different rate limits and features.

## Setting Up API Tiers

To set up the API tiers in the database, run the following command:

```bash
npm run seed:api-tiers
```

This will create or update the API tiers in the `SystemSetting` table with the key `api_tiers`.

## Using the API Pricing Component

The `EnhancedApiPricing` component can be used to display the pricing tiers in a visually appealing way. To use it:

```jsx
import EnhancedApiPricing from '@/components/api/EnhancedApiPricing';

export default function PricingPage() {
  return (
    <div>
      <h1>API Pricing</h1>
      <EnhancedApiPricing />
    </div>
  );
}
```

## API Endpoints

The following API endpoints are available for managing API tiers:

- `GET /api/api-tiers` - Get all API tiers
- `POST /api/seed/api-tiers` - Seed API tiers (admin only)

## Customizing API Tiers

To customize the API tiers, you can modify the `apiTiersWithPricing` object in the `scripts/seed-api-tiers.js` file and then run the seeding script.

You can customize:

- Pricing for monthly and yearly billing
- Features for each tier
- Descriptions
- Visual elements (icons, gradients, etc.)
- Button text and variants

## Utility Functions

The `api-tier-utils.ts` file provides utility functions for working with API tiers:

- `getApiTiers()` - Fetch API tiers from the database
- `getApiTierPrice(tier, billingPeriod)` - Get the price for a specific tier and billing period
- `formatApiTierPrice(price)` - Format a price for display

## Database Schema

API tiers are stored in the `SystemSetting` table with the key `api_tiers`. The value is a JSON object containing all tier information.

## Integration with API Keys

When creating API keys, you can assign them to specific tiers to enforce rate limits and feature access. The API key validation system checks the tier of each key to determine the appropriate rate limits.
