# Turbopack Build Optimization Guide

## Quick Commands

```bash
# Fast Turbopack build
npm run build:turbo

# Regular build (for comparison)
npm run build

# Clean build with Turbopack
npm run clean:simple && npm run build:turbo
```

## Performance Tips

### 1. **Use Turbopack for Development**
Your dev script already uses Turbopack:
```bash
npm run dev  # Uses --turbopack flag
```

### 2. **Build Optimization Strategies**

#### For Faster Builds:
- Use `npm run build:turbo` instead of `npm run build`
- Clear cache before builds: `npm run clean:simple`
- Use fewer TypeScript strict checks during development

#### For Production:
- Use regular webpack build for production: `npm run build`
- Turbopack is still experimental for production builds

### 3. **Memory Optimization**
If builds are slow due to memory:
```bash
# Increase Node.js memory limit
NODE_OPTIONS="--max_old_space_size=8192" npm run build:turbo
```

### 4. **Parallel Processing**
Enable parallel processing in your environment:
```bash
# Windows PowerShell
$env:UV_THREADPOOL_SIZE=16; npm run build:turbo

# Command Prompt
set UV_THREADPOOL_SIZE=16 && npm run build:turbo
```

### 5. **Build Analysis**
Compare build times:
```bash
# Time the builds
Measure-Command { npm run build }
Measure-Command { npm run build:turbo }
```

## Current Status

✅ **Turbopack Dev**: Enabled and working
🔄 **Turbopack Build**: Experimental, use for testing
⚠️ **Production**: Use webpack build for now

## Troubleshooting

### If Turbopack build fails:
1. Clear cache: `npm run clean:simple`
2. Try regular build: `npm run build`
3. Check for experimental features in next.config.js

### If builds are too slow:
1. Increase memory limit
2. Close other applications
3. Use SSD storage
4. Enable parallel processing

## Next Steps

When Turbopack becomes stable (Next.js 16):
- Switch production builds to Turbopack
- Enable persistent caching
- Use advanced optimization features
