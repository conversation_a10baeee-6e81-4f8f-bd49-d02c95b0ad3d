import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Cleanup script for Elite Communication System data
 * This script removes all Elite Communication related data for testing purposes
 */
async function main() {
  console.log('Starting Elite Communication System cleanup...');

  try {
    // Elite Communication user emails to identify and clean up
    const eliteUserEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    // Find Elite Communication users
    const eliteUsers = await prisma.user.findMany({
      where: {
        email: {
          in: eliteUserEmails
        }
      }
    });

    if (eliteUsers.length === 0) {
      console.log('No Elite Communication users found. Nothing to clean up.');
      return;
    }

    console.log(`Found ${eliteUsers.length} Elite Communication users to clean up.`);

    const userIds = eliteUsers.map(user => user.id);

    // Find events created by Elite organizer
    const eliteEvents = await prisma.event.findMany({
      where: {
        OR: [
          { userId: { in: userIds } },
          { title: { in: ['Tech Innovation Summit 2024', 'Global Finance & Fintech Conference', 'Healthcare Innovation Symposium'] } }
        ]
      }
    });

    const eventIds = eliteEvents.map(event => event.id);

    console.log(`Found ${eliteEvents.length} Elite Communication events to clean up.`);

    // Clean up in the correct order to respect foreign key constraints

    // 1. Delete messages for events
    if (eventIds.length > 0) {
      const deletedMessages = await prisma.message.deleteMany({
        where: {
          eventId: { in: eventIds }
        }
      });
      console.log(`Deleted ${deletedMessages.count} messages for events.`);
    }

    // 2. Delete direct messages
    let profileIds: string[] = [];
    if (userIds.length > 0) {
      const attendeeProfiles = await prisma.attendeeProfile.findMany({
        where: {
          userId: { in: userIds }
        }
      });

      profileIds = attendeeProfiles.map(profile => profile.id);

      if (profileIds.length > 0) {
        const deletedDirectMessages = await prisma.message.deleteMany({
          where: {
            OR: [
              { senderId: { in: profileIds } },
              { receiverId: { in: profileIds } }
            ]
          }
        });
        console.log(`Deleted ${deletedDirectMessages.count} direct messages.`);
      }
    }

    // 3. Delete meeting requests
    if (profileIds.length > 0) {
      const deletedMeetingRequests = await prisma.meetingRequest.deleteMany({
        where: {
          OR: [
            { senderId: { in: profileIds } },
            { receiverId: { in: profileIds } }
          ]
        }
      });
      console.log(`Deleted ${deletedMeetingRequests.count} meeting requests.`);
    }

    // 4. Delete chat room members
    if (userIds.length > 0) {
      const deletedChatMembers = await prisma.chatRoomMember.deleteMany({
        where: {
          userId: { in: userIds }
        }
      });
      console.log(`Deleted ${deletedChatMembers.count} chat room memberships.`);
    }

    // 5. Delete chat rooms
    if (eventIds.length > 0) {
      const deletedChatRooms = await prisma.chatRoom.deleteMany({
        where: {
          eventId: { in: eventIds }
        }
      });
      console.log(`Deleted ${deletedChatRooms.count} chat rooms.`);
    }

    // 6. Delete attendee profiles
    if (userIds.length > 0) {
      const deletedProfiles = await prisma.attendeeProfile.deleteMany({
        where: {
          userId: { in: userIds }
        }
      });
      console.log(`Deleted ${deletedProfiles.count} attendee profiles.`);
    }

    // 7. Delete Elite Communication subscriptions
    if (userIds.length > 0) {
      const deletedSubscriptions = await prisma.eliteCommunication.deleteMany({
        where: {
          userId: { in: userIds }
        }
      });
      console.log(`Deleted ${deletedSubscriptions.count} Elite Communication subscriptions.`);
    }

    // 8. Delete events
    if (eventIds.length > 0) {
      const deletedEvents = await prisma.event.deleteMany({
        where: {
          id: { in: eventIds }
        }
      });
      console.log(`Deleted ${deletedEvents.count} events.`);
    }

    // 9. Delete users
    const deletedUsers = await prisma.user.deleteMany({
      where: {
        email: { in: eliteUserEmails }
      }
    });
    console.log(`Deleted ${deletedUsers.count} users.`);

    console.log('Elite Communication System cleanup completed successfully!');

  } catch (error) {
    console.error('Error during Elite Communication cleanup:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Error during Elite Communication cleanup:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
