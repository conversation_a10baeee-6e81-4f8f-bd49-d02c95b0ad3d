-- CreateTable
CREATE TABLE "PromotionStrategy" (
    "id" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "budget" DOUBLE PRECISION NOT NULL,
    "targetAudience" TEXT,
    "channels" TEXT[],
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "goals" JSONB,
    "isAutomated" BOOLEAN NOT NULL DEFAULT false,
    "status" TEXT NOT NULL DEFAULT 'DRAFT',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PromotionStrategy_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromotionRecommendation" (
    "id" TEXT NOT NULL,
    "strategyId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "budget" DOUBLE PRECISION NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "channels" TEXT[],
    "targetAudience" TEXT,
    "reasoning" TEXT,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "notes" TEXT,
    "implementedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PromotionRecommendation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PromotionStrategyPromotion" (
    "id" TEXT NOT NULL,
    "strategyId" TEXT NOT NULL,
    "promotionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PromotionStrategyPromotion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReferralLink" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "referrerId" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3),
    "views" INTEGER NOT NULL DEFAULT 0,
    "clicks" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReferralLink_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Referral" (
    "id" TEXT NOT NULL,
    "referrerId" TEXT NOT NULL,
    "refereeId" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "referralLinkId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Referral_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AudienceSegment" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" TEXT NOT NULL DEFAULT 'custom',
    "criteria" JSONB,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AudienceSegment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CrossPromotion" (
    "id" TEXT NOT NULL,
    "sourceEventId" TEXT NOT NULL,
    "targetEventId" TEXT NOT NULL,
    "promotionType" TEXT NOT NULL DEFAULT 'MUTUAL',
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "message" TEXT,
    "responseMessage" TEXT,
    "createdBy" TEXT NOT NULL,
    "respondedBy" TEXT,
    "respondedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CrossPromotion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MatchingFeedback" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "matchedUserId" TEXT NOT NULL,
    "rating" INTEGER NOT NULL,
    "feedback" TEXT,
    "actionTaken" TEXT,
    "connectionMade" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MatchingFeedback_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MatchingAnalytics" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "eventId" TEXT NOT NULL,
    "totalMatches" INTEGER NOT NULL DEFAULT 0,
    "successfulConnections" INTEGER NOT NULL DEFAULT 0,
    "averageRating" DOUBLE PRECISION,
    "lastMatchDate" TIMESTAMP(3),
    "profileViews" INTEGER NOT NULL DEFAULT 0,
    "messagesReceived" INTEGER NOT NULL DEFAULT 0,
    "messagesSent" INTEGER NOT NULL DEFAULT 0,
    "meetingsScheduled" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MatchingAnalytics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_AudienceSegmentUsers" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_AudienceSegmentUsers_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "PromotionStrategy_eventId_idx" ON "PromotionStrategy"("eventId");

-- CreateIndex
CREATE INDEX "PromotionStrategy_userId_idx" ON "PromotionStrategy"("userId");

-- CreateIndex
CREATE INDEX "PromotionStrategy_status_idx" ON "PromotionStrategy"("status");

-- CreateIndex
CREATE INDEX "PromotionRecommendation_strategyId_idx" ON "PromotionRecommendation"("strategyId");

-- CreateIndex
CREATE INDEX "PromotionRecommendation_status_idx" ON "PromotionRecommendation"("status");

-- CreateIndex
CREATE INDEX "PromotionStrategyPromotion_strategyId_idx" ON "PromotionStrategyPromotion"("strategyId");

-- CreateIndex
CREATE INDEX "PromotionStrategyPromotion_promotionId_idx" ON "PromotionStrategyPromotion"("promotionId");

-- CreateIndex
CREATE UNIQUE INDEX "PromotionStrategyPromotion_strategyId_promotionId_key" ON "PromotionStrategyPromotion"("strategyId", "promotionId");

-- CreateIndex
CREATE UNIQUE INDEX "ReferralLink_code_key" ON "ReferralLink"("code");

-- CreateIndex
CREATE INDEX "ReferralLink_eventId_idx" ON "ReferralLink"("eventId");

-- CreateIndex
CREATE INDEX "ReferralLink_referrerId_idx" ON "ReferralLink"("referrerId");

-- CreateIndex
CREATE INDEX "ReferralLink_code_idx" ON "ReferralLink"("code");

-- CreateIndex
CREATE INDEX "Referral_referrerId_idx" ON "Referral"("referrerId");

-- CreateIndex
CREATE INDEX "Referral_refereeId_idx" ON "Referral"("refereeId");

-- CreateIndex
CREATE INDEX "Referral_eventId_idx" ON "Referral"("eventId");

-- CreateIndex
CREATE INDEX "Referral_referralLinkId_idx" ON "Referral"("referralLinkId");

-- CreateIndex
CREATE UNIQUE INDEX "Referral_referrerId_refereeId_eventId_key" ON "Referral"("referrerId", "refereeId", "eventId");

-- CreateIndex
CREATE INDEX "AudienceSegment_userId_idx" ON "AudienceSegment"("userId");

-- CreateIndex
CREATE INDEX "AudienceSegment_type_idx" ON "AudienceSegment"("type");

-- CreateIndex
CREATE INDEX "AudienceSegment_createdAt_idx" ON "AudienceSegment"("createdAt");

-- CreateIndex
CREATE INDEX "CrossPromotion_sourceEventId_idx" ON "CrossPromotion"("sourceEventId");

-- CreateIndex
CREATE INDEX "CrossPromotion_targetEventId_idx" ON "CrossPromotion"("targetEventId");

-- CreateIndex
CREATE INDEX "CrossPromotion_status_idx" ON "CrossPromotion"("status");

-- CreateIndex
CREATE INDEX "CrossPromotion_createdBy_idx" ON "CrossPromotion"("createdBy");

-- CreateIndex
CREATE INDEX "CrossPromotion_createdAt_idx" ON "CrossPromotion"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "CrossPromotion_sourceEventId_targetEventId_key" ON "CrossPromotion"("sourceEventId", "targetEventId");

-- CreateIndex
CREATE INDEX "MatchingFeedback_userId_idx" ON "MatchingFeedback"("userId");

-- CreateIndex
CREATE INDEX "MatchingFeedback_eventId_idx" ON "MatchingFeedback"("eventId");

-- CreateIndex
CREATE INDEX "MatchingFeedback_matchedUserId_idx" ON "MatchingFeedback"("matchedUserId");

-- CreateIndex
CREATE INDEX "MatchingFeedback_rating_idx" ON "MatchingFeedback"("rating");

-- CreateIndex
CREATE INDEX "MatchingFeedback_createdAt_idx" ON "MatchingFeedback"("createdAt");

-- CreateIndex
CREATE INDEX "MatchingAnalytics_userId_idx" ON "MatchingAnalytics"("userId");

-- CreateIndex
CREATE INDEX "MatchingAnalytics_eventId_idx" ON "MatchingAnalytics"("eventId");

-- CreateIndex
CREATE INDEX "MatchingAnalytics_totalMatches_idx" ON "MatchingAnalytics"("totalMatches");

-- CreateIndex
CREATE INDEX "MatchingAnalytics_successfulConnections_idx" ON "MatchingAnalytics"("successfulConnections");

-- CreateIndex
CREATE UNIQUE INDEX "MatchingAnalytics_userId_eventId_key" ON "MatchingAnalytics"("userId", "eventId");

-- CreateIndex
CREATE INDEX "_AudienceSegmentUsers_B_index" ON "_AudienceSegmentUsers"("B");

-- AddForeignKey
ALTER TABLE "PromotionStrategy" ADD CONSTRAINT "PromotionStrategy_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromotionStrategy" ADD CONSTRAINT "PromotionStrategy_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromotionRecommendation" ADD CONSTRAINT "PromotionRecommendation_strategyId_fkey" FOREIGN KEY ("strategyId") REFERENCES "PromotionStrategy"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromotionStrategyPromotion" ADD CONSTRAINT "PromotionStrategyPromotion_strategyId_fkey" FOREIGN KEY ("strategyId") REFERENCES "PromotionStrategy"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PromotionStrategyPromotion" ADD CONSTRAINT "PromotionStrategyPromotion_promotionId_fkey" FOREIGN KEY ("promotionId") REFERENCES "Promotion"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReferralLink" ADD CONSTRAINT "ReferralLink_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReferralLink" ADD CONSTRAINT "ReferralLink_referrerId_fkey" FOREIGN KEY ("referrerId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Referral" ADD CONSTRAINT "Referral_referrerId_fkey" FOREIGN KEY ("referrerId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Referral" ADD CONSTRAINT "Referral_refereeId_fkey" FOREIGN KEY ("refereeId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Referral" ADD CONSTRAINT "Referral_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Referral" ADD CONSTRAINT "Referral_referralLinkId_fkey" FOREIGN KEY ("referralLinkId") REFERENCES "ReferralLink"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AudienceSegment" ADD CONSTRAINT "AudienceSegment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CrossPromotion" ADD CONSTRAINT "CrossPromotion_sourceEventId_fkey" FOREIGN KEY ("sourceEventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CrossPromotion" ADD CONSTRAINT "CrossPromotion_targetEventId_fkey" FOREIGN KEY ("targetEventId") REFERENCES "Event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CrossPromotion" ADD CONSTRAINT "CrossPromotion_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CrossPromotion" ADD CONSTRAINT "CrossPromotion_respondedBy_fkey" FOREIGN KEY ("respondedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MatchingFeedback" ADD CONSTRAINT "MatchingFeedback_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MatchingFeedback" ADD CONSTRAINT "MatchingFeedback_matchedUserId_fkey" FOREIGN KEY ("matchedUserId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MatchingFeedback" ADD CONSTRAINT "MatchingFeedback_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MatchingAnalytics" ADD CONSTRAINT "MatchingAnalytics_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MatchingAnalytics" ADD CONSTRAINT "MatchingAnalytics_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "Event"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AudienceSegmentUsers" ADD CONSTRAINT "_AudienceSegmentUsers_A_fkey" FOREIGN KEY ("A") REFERENCES "AudienceSegment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AudienceSegmentUsers" ADD CONSTRAINT "_AudienceSegmentUsers_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
