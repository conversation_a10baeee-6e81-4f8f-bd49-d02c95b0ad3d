const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedNFCPricing() {
  console.log('Seeding NFC product pricing...');
  
  // Define the initial pricing for NFC products
  const nfcProducts = [
    {
      deviceType: 'CARD',
      name: 'NFC Smart Card',
      description: 'Credit card sized NFC device for contactless payments at events',
      price: 20.00,
      currency: 'USD',
      imageUrl: '/images/nfc-card.jpg'
    },
    {
      deviceType: 'FABRIC_WRISTBAND',
      name: 'NFC Fabric Wristband',
      description: 'Comfortable fabric wristband with embedded NFC chip for hands-free payments',
      price: 60.00,
      currency: 'USD',
      imageUrl: '/images/nfc-fabric-wristband.jpg'
    },
    {
      deviceType: 'PAPER_WRISTBAND',
      name: 'NFC Paper Wristband',
      description: 'Disposable paper wristband with NFC chip, ideal for single-day events',
      price: 64.00,
      currency: 'USD',
      imageUrl: '/images/nfc-paper-wristband.jpg'
    },
    {
      deviceType: 'SILICONE_WRISTBAND',
      name: 'NFC Silicone Wristband',
      description: 'Durable silicone wristband with NFC chip, waterproof and long-lasting',
      price: 94.00,
      currency: 'USD',
      imageUrl: '/images/nfc-silicone-wristband.jpg'
    },
    {
      deviceType: 'TAG',
      name: 'NFC Tag',
      description: 'Small, versatile NFC tag that can be attached to keychains or lanyards',
      price: 15.00,
      currency: 'USD',
      imageUrl: '/images/nfc-tag.jpg'
    }
  ];
  
  // Upsert each product (create if not exists, update if exists)
  for (const product of nfcProducts) {
    try {
      await prisma.nFCProductPricing.upsert({
        where: {
          deviceType_currency: {
            deviceType: product.deviceType,
            currency: product.currency
          }
        },
        update: {
          name: product.name,
          description: product.description,
          price: product.price,
          imageUrl: product.imageUrl,
          isActive: true
        },
        create: product
      });
      
      console.log(`Upserted pricing for ${product.name}`);
    } catch (error) {
      console.error(`Error upserting ${product.name}:`, error);
    }
  }
  
  console.log('NFC product pricing seeded successfully!');
}

// Execute the seed function
seedNFCPricing()
  .catch((e) => {
    console.error('Error seeding NFC pricing:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
