import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Find all orders with missing customer information
  const ordersWithMissingInfo = await prisma.order.findMany({
    where: {
      OR: [
        { customerName: { equals: '' } },
        { customerEmail: { equals: '' } },
        { customerPhone: { equals: '' } }
      ]
    },
    include: {
      user: true
    }
  });

  console.log(`Found ${ordersWithMissingInfo.length} orders with missing customer information`);

  // Update each order with default values
  for (const order of ordersWithMissingInfo) {
    const updatedOrder = await prisma.order.update({
      where: { id: order.id },
      data: {
        customerName: order.customerName || order.user?.name || 'Unknown Customer',
        customerEmail: order.customerEmail || order.user?.email || `unknown-${order.id}@example.com`,
        customerPhone: order.customerPhone || 'Unknown'
      }
    });

    console.log(`Updated order ${updatedOrder.id} with name: ${updatedOrder.customerName}, email: ${updatedOrder.customerEmail}, phone: ${updatedOrder.customerPhone}`);
  }

  console.log('All orders updated successfully');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
