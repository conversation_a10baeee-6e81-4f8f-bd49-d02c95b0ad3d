import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Verification script for Elite Communication System data
 * This script checks that all Elite Communication data was created correctly
 */
async function main() {
  console.log('Verifying Elite Communication System data...');

  try {
    // Check users
    const eliteUsers = await prisma.user.findMany({
      where: {
        email: {
          contains: '@'
        },
        OR: [
          { email: { contains: 'techcorp.com' } },
          { email: { contains: 'financeplus.com' } },
          { email: { contains: 'healthtech.com' } },
          { email: { contains: 'marketingpro.com' } },
          { email: { contains: 'consultant.com' } },
          { email: { contains: 'retailtech.com' } },
          { email: { contains: 'dataanalytics.com' } },
          { email: { contains: 'manufacturing.com' } },
          { email: { contains: 'nonprofit.org' } },
          { email: { contains: 'realestate.com' } },
          { email: { contains: 'cybersecurity.com' } },
          { email: { contains: 'logistics.com' } },
          { email: { contains: 'design.com' } },
          { email: { contains: 'energy.com' } },
          { email: { contains: 'education.com' } }
        ]
      },
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true
      }
    });

    console.log(`✓ Found ${eliteUsers.length} Elite Communication users`);

    // Check events
    const eliteEvents = await prisma.event.findMany({
      where: {
        title: {
          in: ['Tech Innovation Summit 2024', 'Global Finance & Fintech Conference', 'Healthcare Innovation Symposium']
        }
      },
      select: {
        id: true,
        title: true,
        location: true,
        startDate: true,
        endDate: true
      }
    });

    console.log(`✓ Found ${eliteEvents.length} Elite Communication events`);
    eliteEvents.forEach(event => {
      console.log(`  - ${event.title} at ${event.location}`);
    });

    // Check Elite Communication subscriptions
    const subscriptions = await prisma.eliteCommunication.findMany({
      where: {
        userId: { in: eliteUsers.map(u => u.id) }
      },
      include: {
        user: { select: { name: true, email: true } },
        event: { select: { title: true } }
      }
    });

    console.log(`✓ Found ${subscriptions.length} Elite Communication subscriptions`);

    // Group by tier
    const tierCounts = subscriptions.reduce((acc, sub) => {
      acc[sub.tier] = (acc[sub.tier] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log('  Subscription tiers:');
    Object.entries(tierCounts).forEach(([tier, count]) => {
      console.log(`    - ${tier}: ${count} subscriptions`);
    });

    // Check attendee profiles
    const profiles = await prisma.attendeeProfile.findMany({
      where: {
        userId: { in: eliteUsers.map(u => u.id) }
      },
      include: {
        user: { select: { name: true, email: true } },
        event: { select: { title: true } }
      }
    });

    console.log(`✓ Found ${profiles.length} attendee profiles`);

    // Check privacy levels
    const privacyLevels = profiles.reduce((acc, profile) => {
      acc[profile.privacyLevel] = (acc[profile.privacyLevel] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log('  Privacy levels:');
    Object.entries(privacyLevels).forEach(([level, count]) => {
      console.log(`    - ${level}: ${count} profiles`);
    });

    // Check chat rooms
    const chatRooms = await prisma.chatRoom.findMany({
      where: {
        eventId: { in: eliteEvents.map(e => e.id) }
      },
      include: {
        event: { select: { title: true } },
        _count: { select: { members: true } }
      }
    });

    console.log(`✓ Found ${chatRooms.length} chat rooms`);
    chatRooms.forEach(room => {
      console.log(`  - ${room.name} (${room.roomType}) - ${room._count.members} members`);
    });

    // Check messages
    const messages = await prisma.message.findMany({
      where: {
        eventId: { in: eliteEvents.map(e => e.id) }
      },
      include: {
        sender: {
          include: {
            user: { select: { name: true } }
          }
        },
        receiver: {
          include: {
            user: { select: { name: true } }
          }
        }
      }
    });

    console.log(`✓ Found ${messages.length} messages`);

    // All messages are direct messages since there's no chatRoomId field
    const directMessages = messages;

    console.log(`  - ${directMessages.length} direct messages`);

    // Check meeting requests
    const meetingRequests = await prisma.meetingRequest.findMany({
      where: {
        eventId: { in: eliteEvents.map(e => e.id) }
      },
      include: {
        sender: {
          include: {
            user: { select: { name: true } }
          }
        },
        receiver: {
          include: {
            user: { select: { name: true } }
          }
        },
        event: { select: { title: true } }
      }
    });

    console.log(`✓ Found ${meetingRequests.length} meeting requests`);

    const statusCounts = meetingRequests.reduce((acc, req) => {
      acc[req.status] = (acc[req.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log('  Meeting request statuses:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`    - ${status}: ${count} requests`);
    });

    // Sample data verification
    console.log('\n📊 Sample Data Summary:');
    console.log(`Users: ${eliteUsers.length}`);
    console.log(`Events: ${eliteEvents.length}`);
    console.log(`Subscriptions: ${subscriptions.length}`);
    console.log(`Attendee Profiles: ${profiles.length}`);
    console.log(`Chat Rooms: ${chatRooms.length}`);
    console.log(`Messages: ${messages.length}`);
    console.log(`Meeting Requests: ${meetingRequests.length}`);

    // Show some sample interactions
    if (directMessages.length > 0) {
      console.log('\n💬 Sample Direct Messages:');
      directMessages.slice(0, 3).forEach(msg => {
        console.log(`  "${msg.content.substring(0, 50)}..." - ${msg.sender?.user?.name || 'Unknown'} to ${msg.receiver?.user?.name || 'Unknown'}`);
      });
    }

    if (meetingRequests.length > 0) {
      console.log('\n🤝 Sample Meeting Requests:');
      meetingRequests.slice(0, 3).forEach(req => {
        console.log(`  "${req.title}" - ${req.sender.user.name} to ${req.receiver.user.name} (${req.status})`);
      });
    }

    console.log('\n✅ Elite Communication System verification completed successfully!');

  } catch (error) {
    console.error('❌ Error during verification:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Error during Elite Communication verification:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
