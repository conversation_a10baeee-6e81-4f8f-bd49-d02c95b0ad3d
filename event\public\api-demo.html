<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event API Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .events-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .event-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .event-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        .event-details {
            padding: 15px;
        }
        .event-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .event-date, .event-location {
            color: #666;
            margin-bottom: 5px;
        }
        .event-description {
            margin-top: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .code-block {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-family: monospace;
        }
        .response-container {
            margin-top: 20px;
        }
        .tab-container {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
        }
        .tab.active {
            border: 1px solid #ddd;
            border-bottom: 1px solid white;
            border-radius: 4px 4px 0 0;
            margin-bottom: -1px;
            background-color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>Event API Demo</h1>
    <p>This page demonstrates how to use the Event API from an external application.</p>

    <div class="tab-container">
        <div class="tab active" onclick="openTab(event, 'public-api')">Public API</div>
        <div class="tab" onclick="openTab(event, 'authenticated-api')">Authenticated API</div>
        <div class="tab" onclick="openTab(event, 'documentation')">Documentation</div>
    </div>

    <div id="public-api" class="tab-content active">
        <h2>Public API</h2>
        <p>These endpoints don't require authentication and can be accessed by anyone.</p>

        <div class="container">
            <div class="card">
                <h3>Get Published Events</h3>
                <div class="form-group">
                    <label for="category">Category (optional)</label>
                    <select id="category">
                        <option value="">All Categories</option>
                        <option value="MUSIC">Music</option>
                        <option value="BUSINESS">Business</option>
                        <option value="TECHNOLOGY">Technology</option>
                        <option value="SPORTS_AND_FITNESS">Sports & Fitness</option>
                    </select>
                </div>
                <button onclick="getPublishedEvents()">Get Events</button>
                <div class="response-container">
                    <h4>Events:</h4>
                    <div id="events-output" class="events-container"></div>
                </div>
            </div>

            <div class="card">
                <h3>Get Event Details</h3>
                <div class="form-group">
                    <label for="event-id">Event ID</label>
                    <input type="text" id="event-id" placeholder="Enter event ID">
                </div>
                <button onclick="getEventDetails()">Get Details</button>
                <div class="response-container">
                    <h4>Event Details:</h4>
                    <div id="event-details-output"></div>
                </div>
            </div>
        </div>
    </div>

    <div id="authenticated-api" class="tab-content">
        <h2>Authenticated API</h2>
        <p>These endpoints require authentication with an API key.</p>

        <div class="container">
            <div class="card">
                <h3>API Key Authentication</h3>
                <div class="form-group">
                    <label for="api-key">API Key</label>
                    <input type="text" id="api-key" placeholder="Enter your API key">
                </div>
                <button onclick="testApiKey()">Test API Key</button>
                <div class="response-container">
                    <h4>Response:</h4>
                    <pre id="api-key-output" class="code-block"></pre>
                </div>
            </div>

            <div class="card">
                <h3>Get Events with API Key</h3>
                <p>This will return more detailed event information when authenticated.</p>
                <div class="form-group">
                    <label for="auth-category">Category (optional)</label>
                    <select id="auth-category">
                        <option value="">All Categories</option>
                        <option value="MUSIC">Music</option>
                        <option value="BUSINESS">Business</option>
                        <option value="TECHNOLOGY">Technology</option>
                        <option value="SPORTS_AND_FITNESS">Sports & Fitness</option>
                    </select>
                </div>
                <button onclick="getEventsWithApiKey()">Get Events</button>
                <div class="response-container">
                    <h4>Events:</h4>
                    <div id="auth-events-output" class="events-container"></div>
                </div>
            </div>
        </div>
    </div>

    <div id="documentation" class="tab-content">
        <h2>API Documentation</h2>
        <p>Here's how to use the Event API in your own applications.</p>

        <div class="container">
            <div class="card">
                <h3>Public Endpoints</h3>
                <h4>Get Published Events</h4>
                <pre class="code-block">
fetch('http://localhost:3001/api/events/published')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));
                </pre>

                <h4>Get Event Details</h4>
                <pre class="code-block">
fetch('http://localhost:3001/api/eventdetails/[event-id]')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));
                </pre>
            </div>

            <div class="card">
                <h3>Authenticated Endpoints</h3>
                <h4>Get Events with API Key</h4>
                <pre class="code-block">
fetch('http://localhost:3001/api/external/events', {
  headers: {
    'X-API-Key': 'your-api-key'
  }
})
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));
                </pre>
            </div>
        </div>
    </div>

    <script>
        // Function to open tabs
        function openTab(evt, tabName) {
            const tabContents = document.getElementsByClassName('tab-content');
            for (let i = 0; i < tabContents.length; i++) {
                tabContents[i].classList.remove('active');
            }

            const tabs = document.getElementsByClassName('tab');
            for (let i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }

            document.getElementById(tabName).classList.add('active');
            evt.currentTarget.classList.add('active');
        }

        // Function to format date
        function formatDate(dateString) {
            const options = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            return new Date(dateString).toLocaleDateString(undefined, options);
        }

        // Function to get published events
        async function getPublishedEvents() {
            const category = document.getElementById('category').value;
            let url = 'http://localhost:3001/api/events/published';
            
            if (category) {
                url += `?category=${category}`;
            }

            try {
                const response = await fetch(url);
                const data = await response.json();
                
                const eventsContainer = document.getElementById('events-output');
                eventsContainer.innerHTML = '';
                
                if (data.events && data.events.length > 0) {
                    data.events.forEach(event => {
                        const eventCard = document.createElement('div');
                        eventCard.className = 'event-card';
                        
                        const imagePath = event.imagePath || '/placeholder-event.jpg';
                        
                        eventCard.innerHTML = `
                            <img src="${imagePath}" alt="${event.title}" class="event-image">
                            <div class="event-details">
                                <div class="event-title">${event.title}</div>
                                <div class="event-date">${formatDate(event.startDate)}</div>
                                <div class="event-location">${event.venue}, ${event.location}</div>
                                <div class="event-description">${event.description}</div>
                            </div>
                        `;
                        
                        eventsContainer.appendChild(eventCard);
                    });
                } else {
                    eventsContainer.innerHTML = '<p>No events found.</p>';
                }
            } catch (error) {
                console.error('Error fetching events:', error);
                document.getElementById('events-output').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }

        // Function to get event details
        async function getEventDetails() {
            const eventId = document.getElementById('event-id').value;
            
            if (!eventId) {
                document.getElementById('event-details-output').innerHTML = '<p>Please enter an event ID.</p>';
                return;
            }
            
            try {
                const response = await fetch(`http://localhost:3001/api/eventdetails/${eventId}`);
                const data = await response.json();
                
                if (data.error) {
                    document.getElementById('event-details-output').innerHTML = `<p>Error: ${data.error}</p>`;
                    return;
                }
                
                const eventDetails = document.getElementById('event-details-output');
                eventDetails.innerHTML = `
                    <div class="event-card">
                        <img src="${data.imagePath || '/placeholder-event.jpg'}" alt="${data.title}" class="event-image">
                        <div class="event-details">
                            <div class="event-title">${data.title}</div>
                            <div class="event-date">Start: ${formatDate(data.startDate)}</div>
                            <div class="event-date">End: ${formatDate(data.endDate)}</div>
                            <div class="event-location">${data.venue}, ${data.location}</div>
                            <div class="event-description">${data.description}</div>
                        </div>
                    </div>
                `;
            } catch (error) {
                console.error('Error fetching event details:', error);
                document.getElementById('event-details-output').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }

        // Function to test API key
        async function testApiKey() {
            const apiKey = document.getElementById('api-key').value;
            
            if (!apiKey) {
                document.getElementById('api-key-output').textContent = 'Please enter an API key.';
                return;
            }
            
            try {
                const response = await fetch('http://localhost:3001/api/external/events', {
                    headers: {
                        'X-API-Key': apiKey
                    }
                });
                
                const data = await response.json();
                document.getElementById('api-key-output').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                console.error('Error testing API key:', error);
                document.getElementById('api-key-output').textContent = `Error: ${error.message}`;
            }
        }

        // Function to get events with API key
        async function getEventsWithApiKey() {
            const apiKey = document.getElementById('api-key').value;
            const category = document.getElementById('auth-category').value;
            
            if (!apiKey) {
                document.getElementById('auth-events-output').innerHTML = '<p>Please enter an API key.</p>';
                return;
            }
            
            let url = 'http://localhost:3001/api/external/events';
            
            if (category) {
                url += `?category=${category}`;
            }
            
            try {
                const response = await fetch(url, {
                    headers: {
                        'X-API-Key': apiKey
                    }
                });
                
                const data = await response.json();
                
                const eventsContainer = document.getElementById('auth-events-output');
                eventsContainer.innerHTML = '';
                
                if (data.events && data.events.length > 0) {
                    data.events.forEach(event => {
                        const eventCard = document.createElement('div');
                        eventCard.className = 'event-card';
                        
                        const imagePath = event.imagePath || '/placeholder-event.jpg';
                        
                        let additionalInfo = '';
                        if (data.authenticated) {
                            additionalInfo = `
                                <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #eee;">
                                    <strong>Additional Info (Authenticated):</strong>
                                    <div>Age Restriction: ${event.ageRestriction ? `${event.ageRestriction.minAge || 'None'}+` : 'None'}</div>
                                    <div>Tickets Available: ${event.tickets ? event.tickets.length : 'Unknown'}</div>
                                </div>
                            `;
                        }
                        
                        eventCard.innerHTML = `
                            <img src="${imagePath}" alt="${event.title}" class="event-image">
                            <div class="event-details">
                                <div class="event-title">${event.title}</div>
                                <div class="event-date">${formatDate(event.startDate)}</div>
                                <div class="event-location">${event.venue}, ${event.location}</div>
                                <div class="event-description">${event.description}</div>
                                ${additionalInfo}
                            </div>
                        `;
                        
                        eventsContainer.appendChild(eventCard);
                    });
                } else {
                    eventsContainer.innerHTML = '<p>No events found.</p>';
                }
            } catch (error) {
                console.error('Error fetching events with API key:', error);
                document.getElementById('auth-events-output').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }

        // Load events on page load
        window.onload = function() {
            getPublishedEvents();
        };
    </script>
</body>
</html>
