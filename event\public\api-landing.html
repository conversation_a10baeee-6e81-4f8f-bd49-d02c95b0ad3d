<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Event Platform API - Developer Portal</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
    }
    .hero {
      background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
      color: white;
      padding: 6rem 0;
      margin-bottom: 3rem;
    }
    .hero h1 {
      font-weight: 700;
      font-size: 3.5rem;
      margin-bottom: 1.5rem;
    }
    .hero p {
      font-size: 1.25rem;
      opacity: 0.9;
      max-width: 700px;
      margin: 0 auto 2rem;
    }
    .feature-icon {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: #6a11cb;
    }
    .feature-card {
      padding: 2rem;
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.05);
      height: 100%;
      transition: all 0.3s ease;
    }
    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }
    .code-block {
      background-color: #f8f9fa;
      padding: 1.5rem;
      border-radius: 10px;
      font-family: monospace;
      margin-bottom: 2rem;
      position: relative;
    }
    .code-block pre {
      margin: 0;
      white-space: pre-wrap;
    }
    .code-block .copy-btn {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      background-color: #fff;
      border: none;
      border-radius: 4px;
      padding: 0.25rem 0.5rem;
      font-size: 0.8rem;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    .code-block .copy-btn:hover {
      background-color: #e9ecef;
    }
    .pricing-card {
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.05);
      transition: all 0.3s ease;
      height: 100%;
    }
    .pricing-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }
    .pricing-header {
      background-color: #f8f9fa;
      padding: 2rem;
      text-align: center;
      border-bottom: 1px solid #e9ecef;
    }
    .pricing-price {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }
    .pricing-period {
      color: #6c757d;
      font-size: 0.9rem;
    }
    .pricing-features {
      padding: 2rem;
    }
    .pricing-features ul {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }
    .pricing-features li {
      padding: 0.5rem 0;
      border-bottom: 1px solid #f8f9fa;
    }
    .pricing-features li:last-child {
      border-bottom: none;
    }
    .pricing-features i {
      color: #28a745;
      margin-right: 0.5rem;
    }
    .pricing-footer {
      padding: 1rem 2rem 2rem;
      text-align: center;
    }
    .cta-section {
      background-color: #f8f9fa;
      padding: 5rem 0;
      margin-top: 3rem;
    }
    .footer {
      background-color: #343a40;
      color: white;
      padding: 3rem 0;
    }
    .footer a {
      color: rgba(255,255,255,0.7);
      text-decoration: none;
    }
    .footer a:hover {
      color: white;
    }
    .footer-heading {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
    }
    .footer-list {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }
    .footer-list li {
      margin-bottom: 0.5rem;
    }
    .social-icons a {
      display: inline-block;
      margin-right: 1rem;
      font-size: 1.5rem;
    }
  </style>
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
    <div class="container">
      <a class="navbar-brand" href="#">Event Platform API</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="#features">Features</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#documentation">Documentation</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#pricing">Pricing</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#support">Support</a>
          </li>
        </ul>
        <div class="d-flex">
          <a href="/api-showcase.html" class="btn btn-outline-primary me-2">API Showcase</a>
          <a href="/api-keys.html" class="btn btn-primary">Get API Key</a>
        </div>
      </div>
    </div>
  </nav>

  <section class="hero text-center">
    <div class="container">
      <h1>Powerful Event Data API</h1>
      <p>Integrate event data into your applications with our comprehensive API. Access events, tickets, analytics, and more.</p>
      <div class="d-flex justify-content-center">
        <a href="#documentation" class="btn btn-light btn-lg me-3">Documentation</a>
        <a href="/api-showcase.html" class="btn btn-outline-light btn-lg">Try It Now</a>
      </div>
    </div>
  </section>

  <section id="features" class="py-5">
    <div class="container">
      <div class="text-center mb-5">
        <h2 class="display-5 fw-bold">API Features</h2>
        <p class="lead">Everything you need to build amazing event applications</p>
      </div>
      
      <div class="row g-4">
        <div class="col-md-4">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="bi bi-calendar-event"></i>
            </div>
            <h3>Event Discovery</h3>
            <p>Access comprehensive event data with powerful filtering and search capabilities. Find events by location, category, date, and more.</p>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="bi bi-ticket-perforated"></i>
            </div>
            <h3>Ticketing Integration</h3>
            <p>Sell tickets to events directly from your application. Create orders, manage inventory, and track sales.</p>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="bi bi-graph-up"></i>
            </div>
            <h3>Analytics & Insights</h3>
            <p>Get valuable insights into event trends, audience demographics, and market data to make informed decisions.</p>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="bi bi-shield-check"></i>
            </div>
            <h3>Secure Authentication</h3>
            <p>API keys with granular permissions ensure your data is secure. Control exactly what each application can access.</p>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="bi bi-speedometer2"></i>
            </div>
            <h3>High Performance</h3>
            <p>Our API is built for speed and reliability. Get responses in milliseconds, even under heavy load.</p>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="bi bi-code-square"></i>
            </div>
            <h3>Developer Friendly</h3>
            <p>Comprehensive documentation, SDKs for popular languages, and interactive examples make integration a breeze.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section id="documentation" class="py-5 bg-light">
    <div class="container">
      <div class="text-center mb-5">
        <h2 class="display-5 fw-bold">API Documentation</h2>
        <p class="lead">Everything you need to get started with our API</p>
      </div>
      
      <div class="row align-items-center mb-5">
        <div class="col-lg-6">
          <h3>Quick Start</h3>
          <p>Getting started with our API is easy. Just follow these simple steps:</p>
          <ol>
            <li>Sign up for an account</li>
            <li>Generate an API key</li>
            <li>Make your first API request</li>
          </ol>
          <p>Our API uses standard HTTP methods and returns JSON responses. All requests require an API key for authentication.</p>
          <a href="/api-showcase.html" class="btn btn-primary">Try the API</a>
        </div>
        <div class="col-lg-6">
          <div class="code-block">
            <button class="copy-btn">Copy</button>
            <pre><code>// Example: Fetch events
fetch('https://api.eventplatform.com/api/monetized/events', {
  headers: {
    'X-API-Key': 'your_api_key_here'
  }
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));</code></pre>
          </div>
        </div>
      </div>
      
      <div class="row">
        <div class="col-md-4 mb-4">
          <div class="card h-100">
            <div class="card-body">
              <h4 class="card-title">Event Discovery API</h4>
              <p class="card-text">Access event data with powerful filtering and search capabilities.</p>
              <ul class="list-unstyled">
                <li><i class="bi bi-check-circle-fill text-success me-2"></i> List events</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i> Get event details</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i> Search events</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i> Get categories and types</li>
              </ul>
            </div>
            <div class="card-footer bg-white border-top-0">
              <a href="#" class="btn btn-outline-primary">View Documentation</a>
            </div>
          </div>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="card h-100">
            <div class="card-body">
              <h4 class="card-title">Ticketing API</h4>
              <p class="card-text">Sell tickets to events directly from your application.</p>
              <ul class="list-unstyled">
                <li><i class="bi bi-check-circle-fill text-success me-2"></i> Get available tickets</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i> Create orders</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i> Check order status</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i> Validate tickets</li>
              </ul>
            </div>
            <div class="card-footer bg-white border-top-0">
              <a href="#" class="btn btn-outline-primary">View Documentation</a>
            </div>
          </div>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="card h-100">
            <div class="card-body">
              <h4 class="card-title">Analytics API</h4>
              <p class="card-text">Get valuable insights into event trends and audience demographics.</p>
              <ul class="list-unstyled">
                <li><i class="bi bi-check-circle-fill text-success me-2"></i> Event trends</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i> Audience demographics</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i> Pricing insights</li>
                <li><i class="bi bi-check-circle-fill text-success me-2"></i> Market analysis</li>
              </ul>
            </div>
            <div class="card-footer bg-white border-top-0">
              <a href="#" class="btn btn-outline-primary">View Documentation</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section id="pricing" class="py-5">
    <div class="container">
      <div class="text-center mb-5">
        <h2 class="display-5 fw-bold">API Pricing</h2>
        <p class="lead">Choose the plan that fits your needs</p>
      </div>
      
      <div class="row g-4">
        <div class="col-lg-3 col-md-6">
          <div class="pricing-card">
            <div class="pricing-header">
              <h3>Free</h3>
              <div class="pricing-price">$0</div>
              <div class="pricing-period">per month</div>
            </div>
            <div class="pricing-features">
              <ul>
                <li><i class="bi bi-check-circle-fill"></i> Basic event data</li>
                <li><i class="bi bi-check-circle-fill"></i> 100 requests per day</li>
                <li><i class="bi bi-check-circle-fill"></i> 10 requests per minute</li>
                <li><i class="bi bi-check-circle-fill"></i> Limited search</li>
                <li><i class="bi bi-x-circle-fill text-danger"></i> No analytics</li>
                <li><i class="bi bi-x-circle-fill text-danger"></i> No ticketing</li>
                <li><i class="bi bi-x-circle-fill text-danger"></i> Community support only</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <a href="/api-keys.html" class="btn btn-outline-primary">Get Started</a>
            </div>
          </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
          <div class="pricing-card">
            <div class="pricing-header">
              <h3>Basic</h3>
              <div class="pricing-price">$49</div>
              <div class="pricing-period">per month</div>
            </div>
            <div class="pricing-features">
              <ul>
                <li><i class="bi bi-check-circle-fill"></i> Full event data</li>
                <li><i class="bi bi-check-circle-fill"></i> 5,000 requests per day</li>
                <li><i class="bi bi-check-circle-fill"></i> 60 requests per minute</li>
                <li><i class="bi bi-check-circle-fill"></i> Advanced search</li>
                <li><i class="bi bi-check-circle-fill"></i> Categories and types</li>
                <li><i class="bi bi-x-circle-fill text-danger"></i> No analytics</li>
                <li><i class="bi bi-x-circle-fill text-danger"></i> No ticketing</li>
                <li><i class="bi bi-check-circle-fill"></i> Email support</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <a href="/api-keys.html" class="btn btn-primary">Subscribe</a>
            </div>
          </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
          <div class="pricing-card">
            <div class="pricing-header bg-primary text-white">
              <h3>Professional</h3>
              <div class="pricing-price">$199</div>
              <div class="pricing-period">per month</div>
            </div>
            <div class="pricing-features">
              <ul>
                <li><i class="bi bi-check-circle-fill"></i> Everything in Basic</li>
                <li><i class="bi bi-check-circle-fill"></i> 20,000 requests per day</li>
                <li><i class="bi bi-check-circle-fill"></i> 300 requests per minute</li>
                <li><i class="bi bi-check-circle-fill"></i> Analytics data</li>
                <li><i class="bi bi-check-circle-fill"></i> Ticket availability</li>
                <li><i class="bi bi-check-circle-fill"></i> Order management</li>
                <li><i class="bi bi-check-circle-fill"></i> Priority support</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <a href="/api-keys.html" class="btn btn-primary">Subscribe</a>
            </div>
          </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
          <div class="pricing-card">
            <div class="pricing-header">
              <h3>Enterprise</h3>
              <div class="pricing-price">Custom</div>
              <div class="pricing-period">contact us</div>
            </div>
            <div class="pricing-features">
              <ul>
                <li><i class="bi bi-check-circle-fill"></i> Everything in Professional</li>
                <li><i class="bi bi-check-circle-fill"></i> Unlimited requests per day</li>
                <li><i class="bi bi-check-circle-fill"></i> 1,000 requests per minute</li>
                <li><i class="bi bi-check-circle-fill"></i> Custom endpoints</li>
                <li><i class="bi bi-check-circle-fill"></i> SLA guarantees</li>
                <li><i class="bi bi-check-circle-fill"></i> Dedicated support</li>
                <li><i class="bi bi-check-circle-fill"></i> Account manager</li>
              </ul>
            </div>
            <div class="pricing-footer">
              <a href="#" class="btn btn-outline-primary">Contact Sales</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="cta-section">
    <div class="container text-center">
      <h2 class="display-5 fw-bold mb-4">Ready to get started?</h2>
      <p class="lead mb-4">Sign up for a free account and start using our API today.</p>
      <div class="d-flex justify-content-center">
        <a href="/api-keys.html" class="btn btn-primary btn-lg me-3">Get API Key</a>
        <a href="/api-showcase.html" class="btn btn-outline-primary btn-lg">Try the API</a>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="container">
      <div class="row">
        <div class="col-lg-4 mb-4 mb-lg-0">
          <h5 class="footer-heading">Event Platform API</h5>
          <p>Powerful APIs for integrating event data into your applications.</p>
          <div class="social-icons mt-3">
            <a href="#"><i class="bi bi-twitter"></i></a>
            <a href="#"><i class="bi bi-facebook"></i></a>
            <a href="#"><i class="bi bi-github"></i></a>
            <a href="#"><i class="bi bi-linkedin"></i></a>
          </div>
        </div>
        
        <div class="col-lg-2 col-md-4 mb-4 mb-md-0">
          <h5 class="footer-heading">API</h5>
          <ul class="footer-list">
            <li><a href="#">Documentation</a></li>
            <li><a href="#">Pricing</a></li>
            <li><a href="#">Status</a></li>
            <li><a href="#">Changelog</a></li>
          </ul>
        </div>
        
        <div class="col-lg-2 col-md-4 mb-4 mb-md-0">
          <h5 class="footer-heading">Resources</h5>
          <ul class="footer-list">
            <li><a href="#">Guides</a></li>
            <li><a href="#">Examples</a></li>
            <li><a href="#">SDKs</a></li>
            <li><a href="#">Blog</a></li>
          </ul>
        </div>
        
        <div class="col-lg-2 col-md-4 mb-4 mb-md-0">
          <h5 class="footer-heading">Company</h5>
          <ul class="footer-list">
            <li><a href="#">About</a></li>
            <li><a href="#">Careers</a></li>
            <li><a href="#">Contact</a></li>
            <li><a href="#">Legal</a></li>
          </ul>
        </div>
        
        <div class="col-lg-2 col-md-4">
          <h5 class="footer-heading">Support</h5>
          <ul class="footer-list">
            <li><a href="#">Help Center</a></li>
            <li><a href="#">Community</a></li>
            <li><a href="#">Status</a></li>
            <li><a href="#">Contact</a></li>
          </ul>
        </div>
      </div>
      
      <hr class="mt-4 mb-4" style="border-color: rgba(255,255,255,0.1);">
      
      <div class="row">
        <div class="col-md-6">
          <p class="mb-0">&copy; 2023 Event Platform. All rights reserved.</p>
        </div>
        <div class="col-md-6 text-md-end">
          <a href="#" class="me-3">Terms of Service</a>
          <a href="#" class="me-3">Privacy Policy</a>
          <a href="#">Cookie Policy</a>
        </div>
      </div>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Copy code function
    document.querySelectorAll('.copy-btn').forEach(button => {
      button.addEventListener('click', () => {
        const codeBlock = button.nextElementSibling;
        const code = codeBlock.textContent;
        
        navigator.clipboard.writeText(code).then(() => {
          button.textContent = 'Copied!';
          setTimeout(() => {
            button.textContent = 'Copy';
          }, 2000);
        });
      });
    });
  </script>
</body>
</html>
