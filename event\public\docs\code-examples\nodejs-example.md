# Node.js API Examples

This guide provides examples of how to use the QuickTime Events API with Node.js.

## Prerequisites

- Node.js 14.x or higher
- `axios` library (install with `npm install axios`)

## Authentication

```javascript
const axios = require('axios');

const API_KEY = 'your-api-key';
const BASE_URL = 'https://your-domain.com/api';

// Create an axios instance with default headers
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'X-API-Key': API_KEY,
    'Content-Type': 'application/json'
  }
});
```

## Get Published Events

```javascript
const axios = require('axios');

const API_KEY = 'your-api-key';
const BASE_URL = 'https://your-domain.com/api';

async function getPublishedEvents() {
  try {
    const response = await axios.get(`${BASE_URL}/events/published`, {
      headers: {
        'X-API-Key': API_KEY
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching events:', error.response?.data || error.message);
    return null;
  }
}

// Usage
getPublishedEvents()
  .then(events => {
    if (events) {
      console.log(`Found ${events.length} events`);
      events.forEach(event => {
        console.log(`Event: ${event.title}`);
        console.log(`Date: ${event.startDate}`);
        console.log(`Location: ${event.location}`);
        console.log('---');
      });
    }
  });
```

## Create an Event

```javascript
const axios = require('axios');

const API_KEY = 'your-api-key';
const BASE_URL = 'https://your-domain.com/api';

async function createEvent(eventData) {
  try {
    const response = await axios.post(`${BASE_URL}/events/create`, eventData, {
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error creating event:', error.response?.data || error.message);
    return null;
  }
}

// Create an event starting tomorrow
const tomorrow = new Date();
tomorrow.setDate(tomorrow.getDate() + 1);

const dayAfter = new Date(tomorrow);
dayAfter.setDate(dayAfter.getDate() + 1);

const eventData = {
  title: 'Node.js Meetup',
  description: 'A meetup for Node.js enthusiasts',
  startDate: tomorrow.toISOString(),
  endDate: dayAfter.toISOString(),
  location: 'San Francisco',
  venue: 'Tech Hub',
  category: 'TECHNOLOGY',
  eventType: 'WORKSHOP'
};

// Usage
createEvent(eventData)
  .then(newEvent => {
    if (newEvent) {
      console.log(`Event created with ID: ${newEvent.id}`);
    }
  });
```

## Get Event Details

```javascript
const axios = require('axios');

const API_KEY = 'your-api-key';
const BASE_URL = 'https://your-domain.com/api';

async function getEventDetails(eventId) {
  try {
    const response = await axios.get(`${BASE_URL}/eventdetails/${eventId}`, {
      headers: {
        'X-API-Key': API_KEY
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching event details:', error.response?.data || error.message);
    return null;
  }
}

// Usage
const eventId = 'your-event-id';

getEventDetails(eventId)
  .then(event => {
    if (event) {
      console.log(`Event: ${event.title}`);
      console.log(`Description: ${event.description}`);
      console.log(`Date: ${event.startDate} to ${event.endDate}`);
      console.log(`Location: ${event.location}, ${event.venue}`);
      console.log(`Category: ${event.category}`);
      console.log(`Type: ${event.eventType}`);
    }
  });
```

## Handling Rate Limits

```javascript
const axios = require('axios');

const API_KEY = 'your-api-key';
const BASE_URL = 'https://your-domain.com/api';

async function makeRequestWithRateLimitHandling(url, method = 'GET', data = null) {
  const maxRetries = 5;
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      const options = {
        method,
        url,
        headers: {
          'X-API-Key': API_KEY,
          'Content-Type': 'application/json'
        }
      };
      
      if (data && (method === 'POST' || method === 'PUT')) {
        options.data = data;
      }
      
      const response = await axios(options);
      
      // Check for rate limit headers
      const limit = response.headers['x-ratelimit-limit'];
      const remaining = response.headers['x-ratelimit-remaining'];
      
      if (limit && remaining) {
        console.log(`Rate limit: ${remaining}/${limit} requests remaining`);
      }
      
      return response.data;
    } catch (error) {
      // If rate limited (429 Too Many Requests)
      if (error.response && error.response.status === 429) {
        retries++;
        
        // Get retry delay from headers or default to 60 seconds
        const retryAfter = error.response.headers['retry-after'] 
          ? parseInt(error.response.headers['retry-after']) 
          : 60;
        
        console.log(`Rate limit exceeded. Retrying in ${retryAfter} seconds...`);
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
        continue;
      }
      
      // For other errors, retry with exponential backoff
      retries++;
      if (retries === maxRetries) {
        console.error('Max retries exceeded');
        throw error;
      }
      
      const waitTime = Math.pow(2, retries);
      console.log(`Request failed. Retrying in ${waitTime} seconds...`);
      await new Promise(resolve => setTimeout(resolve, waitTime * 1000));
    }
  }
}

// Usage
makeRequestWithRateLimitHandling(`${BASE_URL}/events/published`)
  .then(events => {
    console.log(`Found ${events.length} events`);
  })
  .catch(error => {
    console.error('Error:', error.message);
  });
```

## Complete Example: Event Manager Class

```javascript
const axios = require('axios');

class EventAPIClient {
  constructor(apiKey, baseUrl = 'https://your-domain.com/api') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
    this.client = axios.create({
      baseURL: baseUrl,
      headers: {
        'X-API-Key': apiKey,
        'Content-Type': 'application/json'
      }
    });
  }
  
  async makeRequest(endpoint, method = 'GET', data = null, params = null) {
    const maxRetries = 5;
    let retries = 0;
    
    while (retries < maxRetries) {
      try {
        const options = {
          method,
          url: endpoint,
        };
        
        if (params) {
          options.params = params;
        }
        
        if (data && (method === 'POST' || method === 'PUT')) {
          options.data = data;
        }
        
        const response = await this.client(options);
        
        // Check for rate limit headers
        const limit = response.headers['x-ratelimit-limit'];
        const remaining = response.headers['x-ratelimit-remaining'];
        
        if (limit && remaining) {
          console.log(`Rate limit: ${remaining}/${limit} requests remaining`);
        }
        
        return {
          success: true,
          status: response.status,
          data: response.data,
          headers: response.headers
        };
      } catch (error) {
        // If rate limited (429 Too Many Requests)
        if (error.response && error.response.status === 429) {
          retries++;
          
          // Get retry delay from headers or default to 60 seconds
          const retryAfter = error.response.headers['retry-after'] 
            ? parseInt(error.response.headers['retry-after']) 
            : 60;
          
          console.log(`Rate limit exceeded. Retrying in ${retryAfter} seconds...`);
          await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
          continue;
        }
        
        // For other errors, retry with exponential backoff
        retries++;
        if (retries === maxRetries) {
          return {
            success: false,
            error: error.message,
            status: error.response?.status,
            data: error.response?.data
          };
        }
        
        const waitTime = Math.pow(2, retries);
        console.log(`Request failed. Retrying in ${waitTime} seconds...`);
        await new Promise(resolve => setTimeout(resolve, waitTime * 1000));
      }
    }
    
    return {
      success: false,
      error: 'Max retries exceeded'
    };
  }
  
  async getPublishedEvents(page = 1, limit = 10) {
    const result = await this.makeRequest('/events/published', 'GET', null, { page, limit });
    return result.success ? result.data : null;
  }
  
  async getEventDetails(eventId) {
    const result = await this.makeRequest(`/eventdetails/${eventId}`);
    return result.success ? result.data : null;
  }
  
  async createEvent(eventData) {
    const result = await this.makeRequest('/events/create', 'POST', eventData);
    return result.success ? result.data : null;
  }
  
  async updateEvent(eventId, eventData) {
    const result = await this.makeRequest(`/events/${eventId}`, 'PUT', eventData);
    return result.success ? result.data : null;
  }
  
  async deleteEvent(eventId) {
    const result = await this.makeRequest(`/events/${eventId}`, 'DELETE');
    return result.success;
  }
}

// Example usage
async function main() {
  const client = new EventAPIClient('your-api-key');
  
  // Get published events
  const events = await client.getPublishedEvents();
  if (events) {
    console.log(`Found ${events.length} events`);
    
    // Show first 3 events
    events.slice(0, 3).forEach(event => {
      console.log(`- ${event.title} (${event.startDate})`);
    });
  }
  
  // Create a new event
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  const dayAfter = new Date(tomorrow);
  dayAfter.setDate(dayAfter.getDate() + 1);
  
  const newEventData = {
    title: 'Node.js API Workshop',
    description: 'Learn how to use APIs with Node.js',
    startDate: tomorrow.toISOString(),
    endDate: dayAfter.toISOString(),
    location: 'Online',
    venue: 'Zoom',
    category: 'TECHNOLOGY',
    eventType: 'WORKSHOP'
  };
  
  const newEvent = await client.createEvent(newEventData);
  if (newEvent) {
    console.log(`Created new event: ${newEvent.title} (ID: ${newEvent.id})`);
    
    // Get the event details
    const eventDetails = await client.getEventDetails(newEvent.id);
    if (eventDetails) {
      console.log(`Event details: ${JSON.stringify(eventDetails, null, 2)}`);
    }
    
    // Update the event
    const updateData = {
      title: 'Updated: Node.js API Workshop',
      description: 'Updated description: Learn how to use APIs with Node.js'
    };
    const updatedEvent = await client.updateEvent(newEvent.id, updateData);
    if (updatedEvent) {
      console.log(`Updated event title to: ${updatedEvent.title}`);
    }
    
    // Delete the event
    if (await client.deleteEvent(newEvent.id)) {
      console.log(`Deleted event with ID: ${newEvent.id}`);
    }
  }
}

main().catch(error => {
  console.error('Error:', error);
});
```
