# Ruby API Examples

This guide provides examples of how to use the QuickTime Events API with Ruby.

## Prerequisites

- Ruby 2.6 or higher
- `httparty` gem (install with `gem install httparty`)

## Authentication

```ruby
require 'httparty'
require 'json'

API_KEY = 'your-api-key'
BASE_URL = 'https://your-domain.com/api'

# Default headers for all requests
HEADERS = {
  'X-API-Key' => API_KEY,
  'Content-Type' => 'application/json'
}
```

## Get Published Events

```ruby
require 'httparty'
require 'json'

API_KEY = 'your-api-key'
BASE_URL = 'https://your-domain.com/api'

def get_published_events
  url = "#{BASE_URL}/events/published"
  
  response = HTTParty.get(url, headers: { 'X-API-Key' => API_KEY })
  
  if response.code == 200
    return JSON.parse(response.body)
  else
    puts "Error: #{response.code}"
    puts response.body
    return nil
  end
end

# Usage
events = get_published_events
if events
  puts "Found #{events.length} events"
  events.each do |event|
    puts "Event: #{event['title']}"
    puts "Date: #{event['startDate']}"
    puts "Location: #{event['location']}"
    puts "---"
  end
end
```

## Create an Event

```ruby
require 'httparty'
require 'json'
require 'date'

API_KEY = 'your-api-key'
BASE_URL = 'https://your-domain.com/api'

def create_event(event_data)
  url = "#{BASE_URL}/events/create"
  
  response = HTTParty.post(
    url,
    headers: {
      'X-API-Key' => API_KEY,
      'Content-Type' => 'application/json'
    },
    body: event_data.to_json
  )
  
  if response.code == 200
    return JSON.parse(response.body)
  else
    puts "Error: #{response.code}"
    puts response.body
    return nil
  end
end

# Create an event starting tomorrow
tomorrow = Date.today + 1
day_after = tomorrow + 1

event_data = {
  title: 'Ruby Meetup',
  description: 'A meetup for Ruby enthusiasts',
  startDate: tomorrow.iso8601,
  endDate: day_after.iso8601,
  location: 'San Francisco',
  venue: 'Tech Hub',
  category: 'TECHNOLOGY',
  eventType: 'WORKSHOP'
}

# Usage
new_event = create_event(event_data)
if new_event
  puts "Event created with ID: #{new_event['id']}"
end
```

## Get Event Details

```ruby
require 'httparty'
require 'json'

API_KEY = 'your-api-key'
BASE_URL = 'https://your-domain.com/api'

def get_event_details(event_id)
  url = "#{BASE_URL}/eventdetails/#{event_id}"
  
  response = HTTParty.get(
    url,
    headers: { 'X-API-Key' => API_KEY }
  )
  
  if response.code == 200
    return JSON.parse(response.body)
  else
    puts "Error: #{response.code}"
    puts response.body
    return nil
  end
end

# Usage
event_id = 'your-event-id'
event = get_event_details(event_id)
if event
  puts "Event: #{event['title']}"
  puts "Description: #{event['description']}"
  puts "Date: #{event['startDate']} to #{event['endDate']}"
  puts "Location: #{event['location']}, #{event['venue']}"
  puts "Category: #{event['category']}"
  puts "Type: #{event['eventType']}"
end
```

## Handling Rate Limits

```ruby
require 'httparty'
require 'json'

API_KEY = 'your-api-key'
BASE_URL = 'https://your-domain.com/api'

def make_request_with_rate_limit_handling(url, method = :get, data = nil)
  max_retries = 5
  retries = 0
  
  while retries < max_retries
    begin
      options = { headers: { 'X-API-Key' => API_KEY } }
      
      if data && (method == :post || method == :put)
        options[:headers]['Content-Type'] = 'application/json'
        options[:body] = data.to_json
      end
      
      response = HTTParty.send(method, url, options)
      
      # Check for rate limit headers
      if response.headers['x-ratelimit-limit'] && response.headers['x-ratelimit-remaining']
        limit = response.headers['x-ratelimit-limit']
        remaining = response.headers['x-ratelimit-remaining']
        puts "Rate limit: #{remaining}/#{limit} requests remaining"
      end
      
      # If rate limited, wait and retry
      if response.code == 429
        retries += 1
        
        # Get retry delay from headers or default to 60 seconds
        retry_after = response.headers['retry-after'] ? response.headers['retry-after'].to_i : 60
        
        puts "Rate limited. Waiting #{retry_after} seconds..."
        sleep(retry_after)
        next
      end
      
      return response
    rescue => e
      retries += 1
      if retries == max_retries
        puts "Max retries exceeded"
        raise e
      end
      
      wait_time = 2 ** retries
      puts "Request failed: #{e.message}. Retrying in #{wait_time} seconds..."
      sleep(wait_time)
    end
  end
end

# Usage
response = make_request_with_rate_limit_handling("#{BASE_URL}/events/published")
if response.code == 200
  events = JSON.parse(response.body)
  puts "Found #{events.length} events"
else
  puts "Error: #{response.code}"
  puts response.body
end
```

## Complete Example: Event Manager Class

```ruby
require 'httparty'
require 'json'
require 'date'

class EventAPIClient
  attr_reader :api_key, :base_url
  
  def initialize(api_key, base_url = 'https://your-domain.com/api')
    @api_key = api_key
    @base_url = base_url
  end
  
  def make_request(endpoint, method = :get, data = nil, params = nil)
    url = "#{@base_url}/#{endpoint}"
    max_retries = 5
    retries = 0
    
    while retries < max_retries
      begin
        options = { 
          headers: { 
            'X-API-Key' => @api_key 
          }
        }
        
        if params
          options[:query] = params
        end
        
        if data && (method == :post || method == :put)
          options[:headers]['Content-Type'] = 'application/json'
          options[:body] = data.to_json
        end
        
        response = HTTParty.send(method, url, options)
        
        # Check for rate limit headers
        if response.headers['x-ratelimit-limit'] && response.headers['x-ratelimit-remaining']
          limit = response.headers['x-ratelimit-limit']
          remaining = response.headers['x-ratelimit-remaining']
          puts "Rate limit: #{remaining}/#{limit} requests remaining"
        end
        
        # If rate limited, wait and retry
        if response.code == 429
          retries += 1
          
          # Get retry delay from headers or default to 60 seconds
          retry_after = response.headers['retry-after'] ? response.headers['retry-after'].to_i : 60
          
          puts "Rate limited. Waiting #{retry_after} seconds..."
          sleep(retry_after)
          next
        end
        
        return {
          success: true,
          status: response.code,
          data: JSON.parse(response.body),
          headers: response.headers
        }
      rescue => e
        retries += 1
        if retries == max_retries
          return {
            success: false,
            error: e.message
          }
        end
        
        wait_time = 2 ** retries
        puts "Request failed: #{e.message}. Retrying in #{wait_time} seconds..."
        sleep(wait_time)
      end
    end
    
    return {
      success: false,
      error: 'Max retries exceeded'
    }
  end
  
  def get_published_events(page = 1, limit = 10)
    result = make_request('events/published', :get, nil, { page: page, limit: limit })
    result[:success] ? result[:data] : nil
  end
  
  def get_event_details(event_id)
    result = make_request("eventdetails/#{event_id}")
    result[:success] ? result[:data] : nil
  end
  
  def create_event(event_data)
    result = make_request('events/create', :post, event_data)
    result[:success] ? result[:data] : nil
  end
  
  def update_event(event_id, event_data)
    result = make_request("events/#{event_id}", :put, event_data)
    result[:success] ? result[:data] : nil
  end
  
  def delete_event(event_id)
    result = make_request("events/#{event_id}", :delete)
    result[:success]
  end
end

# Example usage
client = EventAPIClient.new('your-api-key')

# Get published events
events = client.get_published_events
if events
  puts "Found #{events.length} events"
  
  # Show first 3 events
  events[0..2].each do |event|
    puts "- #{event['title']} (#{event['startDate']})"
  end
end

# Create a new event
tomorrow = Date.today + 1
day_after = tomorrow + 1

new_event_data = {
  title: 'Ruby API Workshop',
  description: 'Learn how to use APIs with Ruby',
  startDate: tomorrow.iso8601,
  endDate: day_after.iso8601,
  location: 'Online',
  venue: 'Zoom',
  category: 'TECHNOLOGY',
  eventType: 'WORKSHOP'
}

new_event = client.create_event(new_event_data)
if new_event
  puts "Created new event: #{new_event['title']} (ID: #{new_event['id']})"
  
  # Get the event details
  event_details = client.get_event_details(new_event['id'])
  if event_details
    puts "Event details: #{JSON.pretty_generate(event_details)}"
  end
  
  # Update the event
  update_data = {
    title: 'Updated: Ruby API Workshop',
    description: 'Updated description: Learn how to use APIs with Ruby'
  }
  updated_event = client.update_event(new_event['id'], update_data)
  if updated_event
    puts "Updated event title to: #{updated_event['title']}"
  end
  
  # Delete the event
  if client.delete_event(new_event['id'])
    puts "Deleted event with ID: #{new_event['id']}"
  end
end
```
