// scripts/apply-nfc-migrations.js
const { PrismaClient } = require('@prisma/client');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

const prisma = new PrismaClient();

async function main() {
  console.log('Starting NFC system migration...');
  
  try {
    // Apply migrations
    console.log('Applying database migrations...');
    await execPromise('npx prisma migrate dev --name nfc-system');
    
    console.log('Migrations applied successfully.');
    
    // Create test data
    console.log('Creating test NFC system settings...');
    
    // Get a test event
    const testEvent = await prisma.event.findFirst({
      where: {
        status: 'Published'
      }
    });
    
    if (!testEvent) {
      console.log('No published events found. Please create a published event first.');
      return;
    }
    
    // Create NFC system settings for the test event
    const nfcSettings = await prisma.nFCSystemSettings.create({
      data: {
        eventId: testEvent.id,
        systemName: 'Event NFC Payment System',
        currencySymbol: 'K',
        defaultLanguage: 'en',
        maxTransactionAmount: 500,
        requirePinForHighValue: true,
        highValueThreshold: 100,
        cardLockoutThreshold: 3,
        offlineModeEnabled: true,
        maxOfflineTransactions: 50,
        offlineTransactionLimit: 50,
        syncInterval: 15,
        receiptEnabled: true,
        analyticsEnabled: true
      }
    });
    
    console.log('NFC system settings created:', nfcSettings);
    
    // Create test NFC cards
    console.log('Creating test NFC cards...');
    
    // Get a test user
    const testUser = await prisma.user.findFirst({
      where: {
        role: 'USER'
      }
    });
    
    if (!testUser) {
      console.log('No users found. Please create a user first.');
      return;
    }
    
    // Create 5 test NFC cards
    const cards = [];
    for (let i = 1; i <= 5; i++) {
      const card = await prisma.nFCCard.create({
        data: {
          uid: `NFC-${Math.floor(Math.random() * 900000) + 100000}`,
          status: 'active',
          balance: Math.floor(Math.random() * 500) + 100,
          assignedTo: i <= 3 ? testUser.name : null,
          userId: i <= 3 ? testUser.id : null,
          eventId: testEvent.id,
          isActive: true
        }
      });
      
      cards.push(card);
    }
    
    console.log(`Created ${cards.length} test NFC cards`);
    
    // Create test vendor if needed
    let vendorUser = await prisma.user.findFirst({
      where: {
        role: 'VENDOR'
      },
      include: {
        vendorProfile: true
      }
    });
    
    if (!vendorUser || !vendorUser.vendorProfile) {
      console.log('Creating test vendor...');
      
      // Create vendor user if needed
      if (!vendorUser) {
        vendorUser = await prisma.user.create({
          data: {
            name: 'Test Vendor',
            email: '<EMAIL>',
            role: 'VENDOR',
            emailVerified: new Date()
          }
        });
      }
      
      // Create vendor profile
      const vendorProfile = await prisma.vendorProfile.create({
        data: {
          userId: vendorUser.id,
          businessName: 'Test Vendor Business',
          businessType: 'Food & Beverages',
          productCategories: 'FOOD_AND_BEVERAGES',
          verificationStatus: 'APPROVED',
          city: 'Lusaka',
          province: 'Lusaka',
          phoneNumber: '+260123456789'
        }
      });
      
      console.log('Vendor profile created:', vendorProfile);
      
      // Create vendor participation in the event
      await prisma.eventVendor.create({
        data: {
          eventId: testEvent.id,
          vendorId: vendorProfile.id,
          status: 'APPROVED',
          boothNumber: 'A-123'
        }
      });
      
      console.log('Vendor added to event');
    }
    
    // Create test products for the vendor
    const vendorProfile = vendorUser.vendorProfile || 
      await prisma.vendorProfile.findUnique({
        where: { userId: vendorUser.id }
      });
    
    console.log('Creating test products...');
    
    const productNames = [
      'Burger', 'Pizza', 'Soft Drink', 'Ice Cream', 'Coffee'
    ];
    
    const products = [];
    for (const name of productNames) {
      const product = await prisma.product.create({
        data: {
          name,
          description: `Delicious ${name.toLowerCase()}`,
          price: Math.floor(Math.random() * 50) + 10,
          userId: vendorUser.id,
          vendorId: vendorProfile.id,
          eventId: testEvent.id,
          category: 'FOOD_AND_BEVERAGES',
          dimensions: '10x10x5',
          material: 'Food',
          productType: 'PHYSICAL',
          status: 'Onsale',
          stockQuantity: 100,
          weight: 0.5
        }
      });
      
      products.push(product);
    }
    
    console.log(`Created ${products.length} test products`);
    
    // Create test transactions
    console.log('Creating test transactions...');
    
    const transactions = [];
    for (let i = 0; i < 3; i++) {
      const card = cards[i];
      const amount = Math.floor(Math.random() * 100) + 20;
      
      // Select 1-3 random products
      const numProducts = Math.floor(Math.random() * 3) + 1;
      const selectedProducts = [];
      for (let j = 0; j < numProducts; j++) {
        const product = products[Math.floor(Math.random() * products.length)];
        const quantity = Math.floor(Math.random() * 3) + 1;
        selectedProducts.push({
          productId: product.id,
          quantity,
          unitPrice: product.price,
          totalPrice: product.price * quantity
        });
      }
      
      // Calculate total amount
      const totalAmount = selectedProducts.reduce((sum, item) => sum + item.totalPrice, 0);
      
      // Create transaction
      const transaction = await prisma.vendorNFCTransaction.create({
        data: {
          vendorId: vendorProfile.id,
          eventId: testEvent.id,
          cardId: card.id,
          userId: testUser.id,
          amount: totalAmount,
          status: 'COMPLETED',
          processedAt: new Date(),
          products: {
            create: selectedProducts
          }
        },
        include: {
          products: true
        }
      });
      
      transactions.push(transaction);
      
      // Update card balance
      await prisma.nFCCard.update({
        where: { id: card.id },
        data: {
          balance: { decrement: totalAmount },
          lastUsed: new Date()
        }
      });
    }
    
    console.log(`Created ${transactions.length} test transactions`);
    
    console.log('NFC system migration completed successfully!');
    
  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
