#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create events for test organizer accounts so they have events to display in their dashboards
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message) {
  log(`\n🎯 ${message}`, colors.cyan + colors.bright);
}

// Test events to create for each organizer
const eventTemplates = [
  {
    title: 'Annual Tech Conference 2024',
    description: 'Join us for the biggest technology conference of the year featuring industry leaders, innovative startups, and cutting-edge technology demonstrations.',
    location: 'Lusaka Convention Centre',
    venue: 'Main Conference Hall',
    category: 'TECHNOLOGY',
    eventType: 'PHYSICAL',
    status: 'Published',
    daysFromNow: 30,
    duration: 8
  },
  {
    title: 'Business Leadership Summit',
    description: 'An exclusive summit for business leaders to network, share insights, and explore new opportunities in the evolving business landscape.',
    location: 'InterContinental Lusaka',
    venue: 'Executive Conference Room',
    category: 'BUSINESS',
    eventType: 'PHYSICAL',
    status: 'Published',
    daysFromNow: 45,
    duration: 6
  },
  {
    title: 'Digital Marketing Workshop',
    description: 'Learn the latest digital marketing strategies and tools to grow your business online. Hands-on workshop with industry experts.',
    location: 'Lusaka Business Hub',
    venue: 'Training Center',
    category: 'BUSINESS',
    eventType: 'HYBRID',
    status: 'Draft',
    daysFromNow: 60,
    duration: 4
  },
  {
    title: 'Music & Arts Festival',
    description: 'Celebrate local and international artists in this vibrant festival featuring live music, art exhibitions, and cultural performances.',
    location: 'Lusaka National Stadium',
    venue: 'Festival Grounds',
    category: 'MUSIC',
    eventType: 'PHYSICAL',
    status: 'Published',
    daysFromNow: 20,
    duration: 12
  }
];

async function createEventForOrganizer(organizer, eventTemplate, index) {
  try {
    const startDate = new Date(Date.now() + eventTemplate.daysFromNow * 24 * 60 * 60 * 1000);
    const endDate = new Date(startDate.getTime() + eventTemplate.duration * 60 * 60 * 1000);

    const event = await prisma.event.create({
      data: {
        title: `${eventTemplate.title} - ${organizer.name}`,
        description: eventTemplate.description,
        location: eventTemplate.location,
        venue: eventTemplate.venue,
        startDate: startDate,
        endDate: endDate,
        startTime: '09:00',
        endTime: `${9 + eventTemplate.duration}:00`,
        category: eventTemplate.category,
        eventType: eventTemplate.eventType,
        status: eventTemplate.status,
        userId: organizer.id,
        timeZone: 'UTC+2'
      }
    });

    logSuccess(`Created event: ${event.title}`);
    return event;

  } catch (error) {
    logError(`Failed to create event for ${organizer.email}: ${error.message}`);
    return null;
  }
}

async function assignEventsToTestOrganizers() {
  logHeader('Creating Events for Test Organizer Accounts');
  
  console.log(`
${colors.cyan}╔══════════════════════════════════════════════════════════════╗
║                🎯 EVENT ASSIGNMENT                           ║
║                                                              ║
║  Creating events for test organizer accounts:               ║
║  • <EMAIL>                              ║
║  • <EMAIL>                            ║
║  • <EMAIL>                              ║
║  • <EMAIL>                               ║
║                                                              ║
║  Each organizer will get 2-3 events with different          ║
║  statuses and categories for testing                        ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}
  `);

  try {
    // Get test organizers
    const testOrganizerEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    const testOrganizers = await prisma.user.findMany({
      where: {
        email: {
          in: testOrganizerEmails
        },
        role: 'ORGANIZER'
      }
    });

    logInfo(`Found ${testOrganizers.length} test organizers`);

    if (testOrganizers.length === 0) {
      logError('No test organizers found. Make sure they exist in the database.');
      return;
    }

    let totalEventsCreated = 0;

    // Create events for each organizer
    for (let i = 0; i < testOrganizers.length; i++) {
      const organizer = testOrganizers[i];
      logHeader(`Creating events for ${organizer.email} (${organizer.name})`);

      // Check if organizer already has events
      const existingEventCount = await prisma.event.count({
        where: { userId: organizer.id }
      });

      if (existingEventCount > 0) {
        logInfo(`${organizer.email} already has ${existingEventCount} events. Skipping...`);
        continue;
      }

      // Create 2-3 events per organizer
      const eventsToCreate = eventTemplates.slice(0, 3); // Take first 3 templates
      
      for (let j = 0; j < eventsToCreate.length; j++) {
        const eventTemplate = eventsToCreate[j];
        const event = await createEventForOrganizer(organizer, eventTemplate, j);
        if (event) {
          totalEventsCreated++;
        }
      }

      logSuccess(`Created events for ${organizer.email}`);
    }

    // Summary
    logHeader('Summary');
    logSuccess(`✅ Created ${totalEventsCreated} events for test organizers`);

    // Verify the results
    logHeader('Verification');
    for (const organizer of testOrganizers) {
      const eventCount = await prisma.event.count({
        where: { userId: organizer.id }
      });
      
      if (eventCount > 0) {
        logSuccess(`${organizer.email}: ${eventCount} events (dashboard will show events)`);
      } else {
        logError(`${organizer.email}: 0 events (dashboard will still be empty)`);
      }
    }

    console.log('\n');
    logSuccess('🎉 Test organizers now have events to display in their dashboards!');
    logInfo('💡 You can now log in with any test organizer account and see events.');
    logInfo('');
    logInfo('Test with these accounts:');
    testOrganizerEmails.forEach(email => {
      logInfo(`   • ${email} / Password123`);
    });

  } catch (error) {
    logError(`Database error: ${error.message}`);
    console.error(error);
  }
}

async function main() {
  await assignEventsToTestOrganizers();
}

// Run the main function
main()
  .catch((error) => {
    logError(`Unexpected error: ${error.message}`);
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
