#!/usr/bin/env node

/**
 * Deployment Readiness Checker
 * Checks if all required environment variables and configurations are set for deployment
 */

const requiredEnvVars = [
  'DATABASE_URL',
  'DIRECT_URL',
  'AUTH_SECRET',
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL',
];

const optionalEnvVars = [
  'STRIPE_SECRET_KEY',
  'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET',
  'GITHUB_CLIENT_ID',
  'GITHUB_CLIENT_SECRET',
  'RESEND_API_KEY',
  'NEXT_PUBLIC_GOOGLE_MAPS_API_KEY',
];

function checkEnvironmentVariables() {
  console.log('🔍 Checking deployment readiness...\n');

  let allRequired = true;
  let warnings = [];

  // Check required variables
  console.log('✅ Required Environment Variables:');
  requiredEnvVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`  ✓ ${varName}: Set`);
    } else {
      console.log(`  ❌ ${varName}: Missing`);
      allRequired = false;
    }
  });

  // Check optional variables
  console.log('\n⚠️  Optional Environment Variables:');
  optionalEnvVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`  ✓ ${varName}: Set`);
    } else {
      console.log(`  - ${varName}: Not set (optional)`);
      warnings.push(`${varName} not set - related features may not work`);
    }
  });

  // Check specific configurations
  console.log('\n🔧 Configuration Checks:');
  
  // Check NODE_ENV
  const nodeEnv = process.env.NODE_ENV;
  if (nodeEnv === 'production') {
    console.log('  ✓ NODE_ENV: production');
  } else {
    console.log(`  ⚠️  NODE_ENV: ${nodeEnv || 'not set'} (should be "production" for deployment)`);
    warnings.push('NODE_ENV should be set to "production" for deployment');
  }

  // Check NEXTAUTH_URL format
  const nextAuthUrl = process.env.NEXTAUTH_URL;
  if (nextAuthUrl) {
    if (nextAuthUrl.startsWith('https://') || nextAuthUrl.startsWith('http://localhost')) {
      console.log('  ✓ NEXTAUTH_URL: Valid format');
    } else {
      console.log('  ❌ NEXTAUTH_URL: Should start with https:// for production');
      allRequired = false;
    }
  }

  // Check database URL format
  const dbUrl = process.env.DATABASE_URL;
  if (dbUrl) {
    if (dbUrl.startsWith('postgresql://') || dbUrl.startsWith('postgres://')) {
      console.log('  ✓ DATABASE_URL: Valid PostgreSQL format');
    } else {
      console.log('  ⚠️  DATABASE_URL: Should be a PostgreSQL connection string');
      warnings.push('DATABASE_URL should be a PostgreSQL connection string');
    }
  }

  // Summary
  console.log('\n📋 Summary:');
  if (allRequired) {
    console.log('  ✅ All required environment variables are set');
  } else {
    console.log('  ❌ Some required environment variables are missing');
  }

  if (warnings.length > 0) {
    console.log(`  ⚠️  ${warnings.length} warning(s):`);
    warnings.forEach(warning => {
      console.log(`    - ${warning}`);
    });
  }

  console.log('\n🚀 Deployment Status:');
  if (allRequired) {
    console.log('  ✅ Ready for deployment!');
    if (warnings.length > 0) {
      console.log('  ⚠️  Some optional features may not work without additional configuration');
    }
  } else {
    console.log('  ❌ Not ready for deployment - fix required environment variables first');
  }

  return allRequired;
}

// Run the check
if (require.main === module) {
  const isReady = checkEnvironmentVariables();
  process.exit(isReady ? 0 : 1);
}

module.exports = { checkEnvironmentVariables };
