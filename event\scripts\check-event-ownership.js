#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to check event ownership and verify that events are properly assigned to organizers
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message) {
  log(`\n🔍 ${message}`, colors.cyan + colors.bright);
}

async function checkEventOwnership() {
  logHeader('Event Ownership Analysis');
  
  console.log(`
${colors.cyan}╔══════════════════════════════════════════════════════════════╗
║                🎯 EVENT OWNERSHIP CHECK                      ║
║                                                              ║
║  Analyzing event ownership and organizer assignments         ║
║  • Total events in database                                 ║
║  • Events per organizer                                     ║
║  • Orphaned events (no owner)                               ║
║  • Event status distribution                                ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}
  `);

  try {
    // Get all events with their owners
    const events = await prisma.event.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    logInfo(`Found ${events.length} total events in database`);

    if (events.length === 0) {
      logWarning('No events found in database. Events may not have been seeded yet.');
      return;
    }

    // Analyze events by owner
    const eventsByOwner = {};
    const orphanedEvents = [];
    const eventsByStatus = {};

    events.forEach(event => {
      // Track by status
      if (!eventsByStatus[event.status]) {
        eventsByStatus[event.status] = 0;
      }
      eventsByStatus[event.status]++;

      // Track by owner
      if (event.user) {
        const ownerKey = `${event.user.email} (${event.user.name})`;
        if (!eventsByOwner[ownerKey]) {
          eventsByOwner[ownerKey] = {
            user: event.user,
            events: []
          };
        }
        eventsByOwner[ownerKey].events.push(event);
      } else {
        orphanedEvents.push(event);
      }
    });

    // Display results
    logHeader('Event Status Distribution');
    Object.entries(eventsByStatus).forEach(([status, count]) => {
      logInfo(`${status}: ${count} events`);
    });

    logHeader('Events by Owner');
    Object.entries(eventsByOwner).forEach(([ownerKey, data]) => {
      const { user, events } = data;
      logSuccess(`${ownerKey} (${user.role}): ${events.length} events`);
      
      events.forEach(event => {
        const statusColor = event.status === 'Published' ? colors.green : 
                           event.status === 'Draft' ? colors.yellow : colors.reset;
        log(`  • ${event.title} (${event.status})`, statusColor);
      });
    });

    if (orphanedEvents.length > 0) {
      logHeader('Orphaned Events (No Owner)');
      logError(`Found ${orphanedEvents.length} events without owners:`);
      orphanedEvents.forEach(event => {
        logError(`  • ${event.title} (ID: ${event.id})`);
      });
    } else {
      logSuccess('No orphaned events found - all events have valid owners');
    }

    // Get organizers and check if they have events
    logHeader('Organizer Analysis');
    const organizers = await prisma.user.findMany({
      where: {
        role: 'ORGANIZER'
      },
      select: {
        id: true,
        name: true,
        email: true,
        _count: {
          select: {
            events: true
          }
        }
      },
      orderBy: {
        email: 'asc'
      }
    });

    logInfo(`Found ${organizers.length} organizers in database`);

    const organizersWithEvents = organizers.filter(org => org._count.events > 0);
    const organizersWithoutEvents = organizers.filter(org => org._count.events === 0);

    logSuccess(`${organizersWithEvents.length} organizers have events:`);
    organizersWithEvents.forEach(org => {
      logInfo(`  • ${org.email} (${org.name}): ${org._count.events} events`);
    });

    if (organizersWithoutEvents.length > 0) {
      logWarning(`${organizersWithoutEvents.length} organizers have no events:`);
      organizersWithoutEvents.forEach(org => {
        logWarning(`  • ${org.email} (${org.name}): 0 events`);
      });
    }

    // Summary and recommendations
    logHeader('Summary & Recommendations');
    
    if (events.length > 0 && orphanedEvents.length === 0) {
      logSuccess('✅ All events have valid owners');
    }
    
    if (organizersWithEvents.length > 0) {
      logSuccess(`✅ ${organizersWithEvents.length} organizers have events to display`);
    }
    
    if (organizersWithoutEvents.length > 0) {
      logWarning(`⚠️  ${organizersWithoutEvents.length} organizers will see empty dashboards`);
      logInfo('💡 Consider running additional seeding or creating test events for these organizers');
    }

    // Test specific organizer accounts
    logHeader('Test Organizer Account Check');
    const testOrganizers = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    for (const email of testOrganizers) {
      const organizer = organizers.find(org => org.email === email);
      if (organizer) {
        if (organizer._count.events > 0) {
          logSuccess(`${email}: ${organizer._count.events} events (dashboard will show events)`);
        } else {
          logWarning(`${email}: 0 events (dashboard will be empty)`);
        }
      } else {
        logError(`${email}: User not found`);
      }
    }

    console.log('\n');
    if (organizersWithoutEvents.length > 0) {
      logInfo('🔧 To fix empty dashboards, you can:');
      logInfo('   1. Run: npm run db:seed-manual (to create more events)');
      logInfo('   2. Create events manually through the dashboard');
      logInfo('   3. Assign existing events to organizers without events');
    } else {
      logSuccess('🎉 All organizers should see events in their dashboards!');
    }

  } catch (error) {
    logError(`Database error: ${error.message}`);
    console.error(error);
  }
}

async function main() {
  await checkEventOwnership();
}

// Run the main function
main()
  .catch((error) => {
    logError(`Unexpected error: ${error.message}`);
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
