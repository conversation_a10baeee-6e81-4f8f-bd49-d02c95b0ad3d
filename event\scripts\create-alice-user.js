const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Creating <PERSON> user...');
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('<PERSON> already exists:', existingUser.email);
      
      // Check if user has a password
      if (!existingUser.password) {
        console.log('User exists but has no password. Adding password...');
        const hashedPassword = await bcrypt.hash('Password123', 10);
        
        await prisma.user.update({
          where: { id: existingUser.id },
          data: { 
            password: hashedPassword,
            emailVerified: new Date()
          }
        });
        
        console.log('Password added for <PERSON>');
      } else {
        console.log('<PERSON> already has a password');
      }
      
      return;
    }

    // Create <PERSON> user
    const hashedPassword = await bcrypt.hash('Password123', 10);
    
    const user = await prisma.user.create({
      data: {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ORGANIZER',
        emailVerified: new Date(),
        subscriptionTier: 'PREMIUM',
        accountBalance: 5000.0,
      }
    });

    console.log('Alice Johnson created successfully:', user.email);
    console.log('Login credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: Password123');
    
  } catch (error) {
    console.error('Error creating Alice Johnson:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
