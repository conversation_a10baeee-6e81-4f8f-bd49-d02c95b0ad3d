#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to create missing users and fix password issues for all user roles
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message) {
  log(`\n🔧 ${message}`, colors.cyan + colors.bright);
}

// Missing users to create
const missingUsers = [
  // SYSTEM_ADMIN
  {
    email: '<EMAIL>',
    password: 'Developer@123456',
    name: 'System Developer',
    role: 'DEVELOPER',
    category: 'SYSTEM_ADMIN'
  },

  // PARTNERS
  {
    email: '<EMAIL>',
    password: 'Password123!',
    name: 'Grand Hotel',
    role: 'PARTNER',
    category: 'PARTNER'
  },
  {
    email: '<EMAIL>',
    password: 'Password123!',
    name: 'Tasty Restaurant',
    role: 'PARTNER',
    category: 'PARTNER'
  },
  {
    email: '<EMAIL>',
    password: 'Password123!',
    name: 'Downtown Bar',
    role: 'PARTNER',
    category: 'PARTNER'
  },
  {
    email: '<EMAIL>',
    password: 'Password123!',
    name: 'Nightlife Club',
    role: 'PARTNER',
    category: 'PARTNER'
  },

  // VENDORS
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'Test Vendor',
    role: 'VENDOR',
    category: 'VENDOR'
  },
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'Food Vendor',
    role: 'VENDOR',
    category: 'VENDOR'
  },
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'Tech Vendor',
    role: 'VENDOR',
    category: 'VENDOR'
  },

  // USERS
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'Regular User',
    role: 'USER',
    category: 'USER'
  },
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'Event Attendee',
    role: 'USER',
    category: 'USER'
  },
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'Customer User',
    role: 'USER',
    category: 'USER'
  }
];

// Users that need password fixes
const passwordFixUsers = [
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'Alice Johnson'
  },
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'Michael Brown'
  },
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'Emma Davis'
  },
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'John Smith'
  },
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'Maria Garcia'
  },
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'David Wilson'
  },
  {
    email: '<EMAIL>',
    password: 'Password123',
    name: 'Sarah Johnson'
  }
];

async function createMissingUser(userInfo) {
  logHeader(`Creating ${userInfo.category} user: ${userInfo.name} (${userInfo.email})`);

  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: userInfo.email }
    });

    if (existingUser) {
      logInfo(`User already exists: ${userInfo.email}`);
      return true;
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(userInfo.password, 12);
    logInfo(`Generated password hash`);

    // Create the user
    const user = await prisma.user.create({
      data: {
        email: userInfo.email,
        name: userInfo.name,
        password: hashedPassword,
        role: userInfo.role,
        emailVerified: new Date()
      }
    });

    logSuccess(`Created user: ${userInfo.name} (${userInfo.email})`);
    logInfo(`  ID: ${user.id}`);
    logInfo(`  Role: ${user.role}`);

    // If it's a partner, create partner profile
    if (userInfo.role === 'PARTNER') {
      const partnerType = userInfo.email.includes('hotel') ? 'HOTEL' :
                         userInfo.email.includes('restaurant') ? 'RESTAURANT' :
                         userInfo.email.includes('bar') ? 'BAR' :
                         userInfo.email.includes('nightclub') ? 'NIGHTCLUB' : 'HOTEL';

      await prisma.partner.create({
        data: {
          businessName: userInfo.name,
          partnerType: partnerType,
          contactName: userInfo.name,
          contactEmail: userInfo.email,
          contactPhone: '+260-XXX-XXXX',
          userId: user.id,
          address: 'Sample Address',
          city: 'Lusaka',
          province: 'Lusaka'
        }
      });
      logSuccess(`Created partner profile for ${userInfo.name}`);
    }

    // If it's a vendor, create vendor profile
    if (userInfo.role === 'VENDOR') {
      await prisma.vendorProfile.create({
        data: {
          userId: user.id,
          businessName: userInfo.name,
          businessType: userInfo.email.includes('food') ? 'Food & Beverage' :
                       userInfo.email.includes('tech') ? 'Technology' : 'General',
          description: `${userInfo.name} - Professional vendor services`,
          email: userInfo.email,
          phoneNumber: '+260-XXX-XXXX',
          physicalAddress: 'Sample Address',
          city: 'Lusaka',
          province: 'Lusaka',
          productCategories: userInfo.email.includes('food') ? 'FOOD_AND_BEVERAGES' :
                            userInfo.email.includes('tech') ? 'TECHNOLOGY' : 'GENERAL',
          verificationStatus: 'PENDING'
        }
      });
      logSuccess(`Created vendor profile for ${userInfo.name}`);
    }

    return true;

  } catch (error) {
    logError(`Error creating user ${userInfo.email}: ${error.message}`);
    return false;
  }
}

async function fixUserPassword(userInfo) {
  logHeader(`Fixing password for: ${userInfo.name} (${userInfo.email})`);

  try {
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email: userInfo.email }
    });

    if (!user) {
      logError(`User not found: ${userInfo.email}`);
      return false;
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(userInfo.password, 12);
    logInfo(`Generated password hash`);

    // Update the user's password
    await prisma.user.update({
      where: { id: user.id },
      data: { password: hashedPassword }
    });

    logSuccess(`Password updated for: ${userInfo.email}`);
    return true;

  } catch (error) {
    logError(`Error fixing password for ${userInfo.email}: ${error.message}`);
    return false;
  }
}

async function main() {
  logHeader('Creating Missing Users & Fixing Passwords');

  console.log(`
${colors.cyan}╔══════════════════════════════════════════════════════════════╗
║                🚀 COMPLETE USER SETUP                       ║
║                                                              ║
║  Creating missing users for all roles:                      ║
║  • DEVELOPER (System Admin)                                 ║
║  • PARTNERS (Hotel, Restaurant, Bar, Nightclub)             ║
║  • VENDORS (General, Food, Tech)                            ║
║  • USERS (Regular Users, Attendees, Customers)              ║
║                                                              ║
║  Fixing passwords for existing users without passwords      ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}
  `);

  let createdCount = 0;
  let fixedCount = 0;
  let failureCount = 0;

  // Create missing users
  logHeader('Creating Missing Users');
  for (const userInfo of missingUsers) {
    const success = await createMissingUser(userInfo);
    if (success) {
      createdCount++;
    } else {
      failureCount++;
    }
    console.log(''); // Add spacing
  }

  // Fix passwords for existing users
  logHeader('Fixing Passwords for Existing Users');
  for (const userInfo of passwordFixUsers) {
    const success = await fixUserPassword(userInfo);
    if (success) {
      fixedCount++;
    } else {
      failureCount++;
    }
    console.log(''); // Add spacing
  }

  // Summary
  logHeader('Summary');

  logSuccess(`✅ Created ${createdCount} new users`);
  logSuccess(`✅ Fixed passwords for ${fixedCount} existing users`);

  if (failureCount > 0) {
    logError(`❌ ${failureCount} operations failed`);
  }

  if (failureCount === 0) {
    logSuccess('🎉 ALL USER ROLES are now ready for authentication!');
    console.log('\n🚀 You can now test login with:');
    console.log('   • System Admins: <EMAIL>, <EMAIL>, <EMAIL>');
    console.log('   • Organizers: <EMAIL>, <EMAIL>, etc.');
    console.log('   • Partners: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>');
    console.log('   • Vendors: <EMAIL>, <EMAIL>, <EMAIL>');
    console.log('   • Users: <EMAIL>, <EMAIL>, <EMAIL>');
    console.log('   • Elite Users: <EMAIL>, <EMAIL>, etc.');
    console.log('\n💡 Run "npm run db:check-users" to verify all users are working.');
  } else {
    logError('⚠️  Some operations failed. Check the errors above.');
  }
}

// Run the main function
main()
  .catch((error) => {
    logError(`Unexpected error: ${error.message}`);
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
