/**
 * Production Deployment Script
 * 
 * This script performs pre-deployment checks and builds the application for production.
 * It verifies that all required environment variables are set and runs database migrations.
 * 
 * Usage:
 * node scripts/deploy-production.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Required environment variables for production
const requiredEnvVars = [
  'DATABASE_URL',
  'DIRECT_URL',
  'AUTH_SECRET',
  'NEXTAUTH_URL',
  'NEXT_PUBLIC_API_URL',
  'NEXT_PUBLIC_BASE_URL',
  'NEXT_PUBLIC_APP_URL',
  'NEXT_PUBLIC_SITE_URL',
  'RESEND_API_KEY'
];

// Optional but recommended environment variables
const recommendedEnvVars = [
  'GITHUB_CLIENT_ID',
  'GITHUB_CLIENT_SECRET',
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET',
  'ADMIN_EMAIL'
];

// Check if .env.production file exists
function checkEnvFile() {
  console.log('Checking .env.production file...');
  
  const envFilePath = path.join(__dirname, '..', '.env.production');
  
  if (!fs.existsSync(envFilePath)) {
    console.error('❌ .env.production file not found!');
    console.error('Please create a .env.production file with the required environment variables.');
    process.exit(1);
  }
  
  console.log('✅ .env.production file found');
  
  // Load environment variables from .env.production
  require('dotenv').config({ path: envFilePath });
  
  // Check required environment variables
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingVars.forEach(varName => console.error(`   - ${varName}`));
    process.exit(1);
  }
  
  console.log('✅ All required environment variables are set');
  
  // Check recommended environment variables
  const missingRecommended = recommendedEnvVars.filter(varName => !process.env[varName]);
  
  if (missingRecommended.length > 0) {
    console.warn('⚠️ Missing recommended environment variables:');
    missingRecommended.forEach(varName => console.warn(`   - ${varName}`));
  } else {
    console.log('✅ All recommended environment variables are set');
  }
}

// Run database migrations
function runMigrations() {
  console.log('Running database migrations...');
  
  try {
    execSync('npx prisma migrate deploy', { stdio: 'inherit' });
    console.log('✅ Database migrations completed successfully');
  } catch (error) {
    console.error('❌ Database migration failed:', error.message);
    process.exit(1);
  }
}

// Build the application for production
function buildApp() {
  console.log('Building application for production...');
  
  try {
    execSync('npm run build:prod', { stdio: 'inherit' });
    console.log('✅ Production build completed successfully');
  } catch (error) {
    console.error('❌ Production build failed:', error.message);
    process.exit(1);
  }
}

// Main function
function main() {
  console.log('Starting production deployment process...');
  
  // Check environment variables
  checkEnvFile();
  
  // Run database migrations
  runMigrations();
  
  // Build the application
  buildApp();
  
  console.log('✅ Production deployment preparation completed successfully');
  console.log('You can now deploy the application to your production environment.');
}

// Run the main function
main();
