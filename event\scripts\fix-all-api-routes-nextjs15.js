#!/usr/bin/env node

/**
 * Comprehensive fix for Next.js 15+ Promise-based params for ALL API routes
 * 
 * This script updates all API route.ts files to use Promise-based params
 * as required by Next.js 15+
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Comprehensive fix for Next.js 15+ Promise-based params for ALL API routes...\n');

// Find all route.ts files
function findAllApiRoutes(dir) {
  const routes = [];
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (item === 'route.ts') {
        routes.push(fullPath);
      }
    }
  }
  
  scanDirectory(dir);
  return routes;
}

// Check if file needs updating
function needsUpdate(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Check for old params patterns
  const patterns = [
    /params\s*:\s*\{\s*[^}]*\s*\}/,  // params: { ... }
    /params\s*:\s*Promise<\{[^}]*\}>\s*\|\s*\{[^}]*\}/,  // union types
  ];
  
  for (const pattern of patterns) {
    if (pattern.test(content)) {
      // Check if it's already using Promise<{ ... }> correctly
      if (!content.includes('params: Promise<{') || content.includes('| {')) {
        return true;
      }
    }
  }
  
  return false;
}

// Update file to use Promise-based params
function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  // Pattern 1: Standard HTTP method handlers with params
  const httpMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
  
  for (const method of httpMethods) {
    // Pattern: export async function METHOD(request, { params }: { params: { ... } })
    const pattern1 = new RegExp(
      `(export\\s+async\\s+function\\s+${method}\\s*\\([^,]+,\\s*\\{\\s*params\\s*\\}\\s*:\\s*\\{\\s*params:\\s*)\\{([^}]+)\\}(\\s*\\})`,
      'g'
    );
    
    content = content.replace(pattern1, (match, prefix, paramTypes, suffix) => {
      updated = true;
      return `${prefix}Promise<{${paramTypes}}>${suffix}`;
    });
    
    // Pattern: export async function METHOD(request, context: { params: { ... } })
    const pattern2 = new RegExp(
      `(export\\s+async\\s+function\\s+${method}\\s*\\([^,]+,\\s*\\w+\\s*:\\s*\\{\\s*params:\\s*)\\{([^}]+)\\}(\\s*\\})`,
      'g'
    );
    
    content = content.replace(pattern2, (match, prefix, paramTypes, suffix) => {
      updated = true;
      return `${prefix}Promise<{${paramTypes}}>${suffix}`;
    });
    
    // Pattern: Remove union types like Promise<{ ... }> | { ... }
    const pattern3 = new RegExp(
      `(params:\\s*)Promise<\\{([^}]+)\\}>\\s*\\|\\s*\\{[^}]+\\}`,
      'g'
    );
    
    content = content.replace(pattern3, (match, prefix, paramTypes) => {
      updated = true;
      return `${prefix}Promise<{${paramTypes}}>`;
    });
  }
  
  // Pattern 2: Update params usage inside functions
  if (updated) {
    // Find function bodies and update params usage
    const functionPattern = /export\s+async\s+function\s+\w+\s*\([^)]*\)\s*\{/g;
    let match;
    
    while ((match = functionPattern.exec(content)) !== null) {
      const funcStart = match.index + match[0].length;
      const funcBody = extractFunctionBody(content, funcStart);
      
      if (funcBody && funcBody.includes('params.') && !funcBody.includes('resolvedParams') && !funcBody.includes('await params')) {
        // Insert params resolution at the beginning of the function
        const beforeFunc = content.substring(0, funcStart);
        const afterFunc = content.substring(funcStart + funcBody.length);
        
        // Add params resolution
        const paramsResolution = '\n    const resolvedParams = await params;\n';
        
        // Replace params.xxx with resolvedParams.xxx in the function body
        const updatedFuncBody = funcBody.replace(/params\.(\w+)/g, 'resolvedParams.$1');
        
        content = beforeFunc + paramsResolution + updatedFuncBody + afterFunc;
        break; // Only update the first function to avoid conflicts
      }
    }
  }
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Updated: ${path.relative(process.cwd(), filePath)}`);
  }
  
  return updated;
}

// Extract function body (simplified)
function extractFunctionBody(content, start) {
  let braceCount = 1;
  let i = start;
  
  while (i < content.length && braceCount > 0) {
    if (content[i] === '{') braceCount++;
    if (content[i] === '}') braceCount--;
    i++;
  }
  
  return braceCount === 0 ? content.substring(start, i - 1) : null;
}

// Main execution
try {
  const apiDir = path.join(__dirname, '..', 'src', 'app', 'api');
  console.log(`Searching for ALL API routes in ${apiDir}...`);
  
  const allRoutes = findAllApiRoutes(apiDir);
  console.log(`Found ${allRoutes.length} API route files\n`);
  
  let updatedCount = 0;
  
  for (const routePath of allRoutes) {
    const relativePath = path.relative(process.cwd(), routePath);
    
    if (needsUpdate(routePath)) {
      if (updateFile(routePath)) {
        updatedCount++;
      }
    } else {
      console.log(`⏭️  Skipped: ${relativePath} (already updated or no params)`);
    }
  }
  
  console.log(`\n🎉 Updated ${updatedCount} API route files for Next.js 15+ compatibility`);
  console.log('\n📋 Next steps:');
  console.log('1. Test the build: npm run build');
  console.log('2. Check for any remaining TypeScript errors');
  console.log('3. Test the API functionality');
  
} catch (error) {
  console.error('❌ Error:', error);
}
