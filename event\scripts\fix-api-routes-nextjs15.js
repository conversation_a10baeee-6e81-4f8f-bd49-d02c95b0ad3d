#!/usr/bin/env node

/**
 * Fix Next.js 15+ Promise-based params for API routes
 * 
 * This script updates all API route.ts files to use Promise-based params
 * as required by Next.js 15+
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Next.js 15+ Promise-based params for API routes...\n');

// Find all route.ts files in dynamic routes
function findDynamicApiRoutes(dir) {
  const routes = [];
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Check if this is a dynamic route directory (contains brackets)
        if (item.includes('[') && item.includes(']')) {
          // Look for route.ts in this directory
          const routePath = path.join(fullPath, 'route.ts');
          if (fs.existsSync(routePath)) {
            routes.push(routePath);
          }
        }
        // Continue scanning subdirectories
        scanDirectory(fullPath);
      }
    }
  }
  
  scanDirectory(dir);
  return routes;
}

// Check if file needs updating
function needsUpdate(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Check if it already uses Promise<{ ... }> in params
  if (content.includes('params: Promise<{')) {
    return false;
  }
  
  // Check if it has params prop that needs updating
  const hasParamsProps = content.match(/params\s*:\s*\{\s*[^}]+\s*\}/);
  return !!hasParamsProps;
}

// Update file to use Promise-based params
function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  // Pattern 1: HTTP method handlers with params
  const httpMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
  
  for (const method of httpMethods) {
    const pattern = new RegExp(
      `export\\s+async\\s+function\\s+${method}\\s*\\(([^)]+)\\)`,
      'g'
    );
    
    content = content.replace(pattern, (match, params) => {
      // Check if this function has params parameter
      if (params.includes('params')) {
        // Update the params type
        const updatedParams = params.replace(
          /\{\s*params\s*\}:\s*\{\s*params:\s*\{([^}]+)\}\s*\}/g,
          '{ params }: { params: Promise<{$1}> }'
        );
        
        if (updatedParams !== params) {
          updated = true;
          return `export async function ${method}(${updatedParams})`;
        }
      }
      return match;
    });
  }
  
  // Pattern 2: Update params usage inside functions
  if (updated) {
    // Find function bodies and update params usage
    const functionBodies = content.match(/export\s+async\s+function\s+\w+\s*\([^)]*\)\s*\{[^}]*\}/gs);
    
    if (functionBodies) {
      for (const funcBody of functionBodies) {
        // Check if this function uses params.something
        if (funcBody.includes('params.') && !funcBody.includes('resolvedParams')) {
          // Find the opening brace of the function
          const funcStart = content.indexOf(funcBody);
          const openBrace = content.indexOf('{', funcStart);
          
          // Insert params resolution after the opening brace
          const beforeBrace = content.substring(0, openBrace + 1);
          const afterBrace = content.substring(openBrace + 1);
          
          // Add params resolution
          const paramsResolution = '\n    const resolvedParams = await params;\n';
          
          // Replace params.xxx with resolvedParams.xxx in the function body
          const updatedAfterBrace = afterBrace.replace(/params\.(\w+)/g, 'resolvedParams.$1');
          
          content = beforeBrace + paramsResolution + updatedAfterBrace;
          break; // Only update the first function to avoid conflicts
        }
      }
    }
  }
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Updated: ${filePath}`);
  }
  
  return updated;
}

// Main execution
try {
  const apiDir = path.join(__dirname, '..', 'src', 'app', 'api');
  console.log(`Searching for dynamic API routes in ${apiDir}...`);
  
  const dynamicRoutes = findDynamicApiRoutes(apiDir);
  console.log(`Found ${dynamicRoutes.length} dynamic API routes\n`);
  
  let updatedCount = 0;
  
  for (const routePath of dynamicRoutes) {
    const relativePath = path.relative(process.cwd(), routePath);
    
    if (needsUpdate(routePath)) {
      if (updateFile(routePath)) {
        updatedCount++;
      }
    } else {
      console.log(`⏭️  Skipped: ${relativePath} (already updated)`);
    }
  }
  
  console.log(`\n🎉 Updated ${updatedCount} API route files for Next.js 15+ compatibility`);
  console.log('\n📋 Next steps:');
  console.log('1. Test the build: npm run build');
  console.log('2. Check for any remaining TypeScript errors');
  console.log('3. Test the API functionality');
  
} catch (error) {
  console.error('❌ Error:', error);
}
