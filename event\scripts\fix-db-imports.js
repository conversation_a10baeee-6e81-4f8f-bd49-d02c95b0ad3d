#!/usr/bin/env node

/**
 * Fix all instances of import { db } from '@/lib/db' to import { db } from '@/lib/prisma'
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing all db import paths in the codebase...\n');

// Find all TypeScript files in src directory
function findAllTsFiles(dir) {
  const files = [];
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and .next directories
        if (!item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        }
      } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(dir);
  return files;
}

// Check if file contains incorrect db import
function needsUpdate(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return content.includes("from '@/lib/db'");
}

// Update file to fix db import
function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  // Replace the import
  const originalContent = content;
  content = content.replace(/from ['"]@\/lib\/db['"]/g, "from '@/lib/prisma'");
  
  if (content !== originalContent) {
    updated = true;
    fs.writeFileSync(filePath, content);
    console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)}`);
    return true;
  }
  
  return false;
}

// Main execution
try {
  const srcDir = path.join(__dirname, '..', 'src');
  console.log(`Searching for TypeScript files in ${srcDir}...`);
  
  const allFiles = findAllTsFiles(srcDir);
  console.log(`Found ${allFiles.length} TypeScript files\n`);
  
  let updatedCount = 0;
  
  for (const filePath of allFiles) {
    if (needsUpdate(filePath)) {
      if (updateFile(filePath)) {
        updatedCount++;
      }
    }
  }
  
  console.log(`\n🎉 Fixed ${updatedCount} files with incorrect db import paths!`);
  
  if (updatedCount === 0) {
    console.log('✨ No files needed updating - all db imports are already correct!');
  }
  
} catch (error) {
  console.error('❌ Error fixing db imports:', error);
  process.exit(1);
}
