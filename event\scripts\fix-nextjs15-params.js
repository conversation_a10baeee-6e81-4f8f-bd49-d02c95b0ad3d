#!/usr/bin/env node

/**
 * Fix Next.js 15+ Promise-based params
 * 
 * This script updates all dynamic route page.tsx files to use Promise-based params
 * as required by Next.js 15+
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Next.js 15+ Promise-based params...\n');

// Find all page.tsx files in dynamic routes
function findDynamicRoutePages(dir) {
  const pages = [];
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Check if this is a dynamic route directory (contains brackets)
        if (item.includes('[') && item.includes(']')) {
          // Look for page.tsx in this directory
          const pagePath = path.join(fullPath, 'page.tsx');
          if (fs.existsSync(pagePath)) {
            pages.push(pagePath);
          }
        }
        // Continue scanning subdirectories
        scanDirectory(fullPath);
      }
    }
  }
  
  scanDirectory(dir);
  return pages;
}

// Check if file needs updating
function needsUpdate(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Check if it already uses Promise<{ ... }> in params
  if (content.includes('params: Promise<{')) {
    return false;
  }
  
  // Check if it has params prop that needs updating
  const hasParamsProps = content.match(/params\s*:\s*\{\s*[^}]+\s*\}/);
  return !!hasParamsProps;
}

// Update file to use Promise-based params
function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  // Pattern 1: Function component with params prop
  const functionPattern = /(\w+)\(\{\s*params\s*\}:\s*\{\s*params:\s*\{([^}]+)\}\s*\}\)/g;
  if (functionPattern.test(content)) {
    content = content.replace(functionPattern, (match, funcName, paramTypes) => {
      updated = true;
      return `${funcName}({ params }: { params: Promise<{${paramTypes}}> })`;
    });
  }
  
  // Pattern 2: Interface-based params
  const interfacePattern = /interface\s+\w*Props\s*\{\s*params:\s*\{([^}]+)\};\s*\}/g;
  if (interfacePattern.test(content)) {
    content = content.replace(interfacePattern, (match, paramTypes) => {
      updated = true;
      return match.replace(`params: {${paramTypes}}`, `params: Promise<{${paramTypes}}>`);
    });
  }
  
  // Add use import if needed and file is client component
  if (updated && content.includes("'use client'") && !content.includes('import { use }') && !content.includes('import React, { use }')) {
    // Add use to existing React imports
    if (content.includes('import React,')) {
      content = content.replace(/import React,\s*\{([^}]+)\}/, (match, imports) => {
        if (!imports.includes('use')) {
          return `import React, { ${imports.trim()}, use }`;
        }
        return match;
      });
    } else if (content.includes('import {') && content.includes("} from 'react'")) {
      content = content.replace(/import\s*\{([^}]+)\}\s*from\s*['"]react['"]/, (match, imports) => {
        if (!imports.includes('use')) {
          return `import { ${imports.trim()}, use } from 'react'`;
        }
        return match;
      });
    } else {
      // Add new import
      content = content.replace("'use client';\n", "'use client';\n\nimport { use } from 'react';\n");
    }
  }
  
  // Update params usage in function body for client components
  if (updated && content.includes("'use client'")) {
    // Add params resolution
    const funcBodyPattern = /(\w+)\(\{\s*params\s*\}:[^{]+\{[^}]+\}\s*\)\s*\{/;
    const match = content.match(funcBodyPattern);
    if (match) {
      const insertPoint = content.indexOf('{', match.index) + 1;
      const resolveCode = '\n  const resolvedParams = use(params);\n';
      
      if (!content.includes('resolvedParams = use(params)')) {
        content = content.slice(0, insertPoint) + resolveCode + content.slice(insertPoint);
        
        // Replace params.id with resolvedParams.id
        content = content.replace(/params\.(\w+)/g, 'resolvedParams.$1');
      }
    }
  }
  
  // Update params usage in async server components
  if (updated && !content.includes("'use client'")) {
    // Add await params resolution
    const funcBodyPattern = /async\s+function\s+\w+\(\{\s*params\s*\}:[^{]+\{[^}]+\}\s*\)\s*\{/;
    const match = content.match(funcBodyPattern);
    if (match) {
      const insertPoint = content.indexOf('{', match.index) + 1;
      const resolveCode = '\n  const resolvedParams = await params;\n';
      
      if (!content.includes('resolvedParams = await params')) {
        content = content.slice(0, insertPoint) + resolveCode + content.slice(insertPoint);
        
        // Replace params.id with resolvedParams.id
        content = content.replace(/params\.(\w+)/g, 'resolvedParams.$1');
      }
    }
  }
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Updated: ${filePath}`);
  }
  
  return updated;
}

// Main execution
try {
  const appDir = path.join(__dirname, '..', 'src', 'app');
  console.log(`Searching for dynamic route pages in ${appDir}...`);
  
  const dynamicPages = findDynamicRoutePages(appDir);
  console.log(`Found ${dynamicPages.length} dynamic route pages\n`);
  
  let updatedCount = 0;
  
  for (const pagePath of dynamicPages) {
    const relativePath = path.relative(process.cwd(), pagePath);
    
    if (needsUpdate(pagePath)) {
      if (updateFile(pagePath)) {
        updatedCount++;
      }
    } else {
      console.log(`⏭️  Skipped: ${relativePath} (already updated)`);
    }
  }
  
  console.log(`\n🎉 Updated ${updatedCount} files for Next.js 15+ compatibility`);
  console.log('\n📋 Next steps:');
  console.log('1. Test the build: npm run build');
  console.log('2. Check for any remaining TypeScript errors');
  console.log('3. Test the application functionality');
  
} catch (error) {
  console.error('❌ Error:', error);
}
