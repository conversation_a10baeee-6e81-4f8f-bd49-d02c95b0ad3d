#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix password issues for specific test users
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message) {
  log(`\n🔧 ${message}`, colors.cyan + colors.bright);
}

async function fixUserPassword(email, password, description) {
  logHeader(`Fixing password for ${description} (${email})`);
  
  try {
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true, email: true, name: true, password: true }
    });

    if (!user) {
      logError(`User not found: ${email}`);
      return false;
    }

    logInfo(`Found user: ${user.name} (${user.id})`);

    // Hash the new password
    const hashedPassword = await bcrypt.hash(password, 12);
    logInfo(`Generated new password hash`);

    // Update the user's password
    await prisma.user.update({
      where: { id: user.id },
      data: { password: hashedPassword }
    });

    logSuccess(`Password updated successfully for ${email}`);

    // Verify the password works
    const updatedUser = await prisma.user.findUnique({
      where: { email },
      select: { password: true }
    });

    if (updatedUser?.password) {
      const passwordMatch = await bcrypt.compare(password, updatedUser.password);
      if (passwordMatch) {
        logSuccess(`Password verification successful`);
        return true;
      } else {
        logError(`Password verification failed after update`);
        return false;
      }
    } else {
      logError(`No password found after update`);
      return false;
    }

  } catch (error) {
    logError(`Error fixing password for ${email}: ${error.message}`);
    return false;
  }
}

async function main() {
  logHeader('Fixing Test User Password Issues');
  
  console.log(`
${colors.cyan}╔══════════════════════════════════════════════════════════════╗
║                    🔧 PASSWORD REPAIR                       ║
║                                                              ║
║  Fixing password issues for problematic test users          ║
║  • <EMAIL> (no password hash)           ║
║  • <EMAIL> (wrong password hash)       ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}
  `);

  const usersToFix = [
    {
      email: '<EMAIL>',
      password: 'Password123',
      description: 'Elite Organizer'
    },
    {
      email: '<EMAIL>',
      password: 'SuperAdmin@1234567',
      description: 'Super Admin'
    }
  ];

  let successCount = 0;
  let failureCount = 0;

  for (const userInfo of usersToFix) {
    const success = await fixUserPassword(userInfo.email, userInfo.password, userInfo.description);
    if (success) {
      successCount++;
    } else {
      failureCount++;
    }
    console.log(''); // Add spacing
  }

  // Summary
  logHeader('Summary');
  
  if (failureCount === 0) {
    logSuccess(`🎉 All ${successCount} users fixed successfully!`);
    logInfo('All test users should now be able to authenticate properly.');
    logInfo('');
    logInfo('Test the following logins:');
    logInfo('• <EMAIL> / Password123');
    logInfo('• <EMAIL> / SuperAdmin@1234567');
  } else {
    logError(`❌ ${failureCount} users could not be fixed`);
    logSuccess(`✅ ${successCount} users fixed successfully`);
  }
}

// Run the main function
main()
  .catch((error) => {
    logError(`Unexpected error: ${error.message}`);
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
