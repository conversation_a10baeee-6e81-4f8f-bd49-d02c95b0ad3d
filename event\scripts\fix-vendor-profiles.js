#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create vendor profiles for existing vendor users
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message) {
  log(`\n🔧 ${message}`, colors.cyan + colors.bright);
}

async function createVendorProfile(user) {
  logHeader(`Creating vendor profile for: ${user.name} (${user.email})`);
  
  try {
    // Check if vendor profile already exists
    const existingProfile = await prisma.vendorProfile.findUnique({
      where: { userId: user.id }
    });

    if (existingProfile) {
      logInfo(`Vendor profile already exists for ${user.email}`);
      return true;
    }

    // Create vendor profile with correct schema
    const vendorProfile = await prisma.vendorProfile.create({
      data: {
        userId: user.id,
        businessName: user.name,
        businessType: user.email.includes('food') ? 'Food & Beverage' :
                     user.email.includes('tech') ? 'Technology' : 'General Services',
        description: `${user.name} - Professional vendor services for events and businesses`,
        email: user.email,
        phoneNumber: '+260-XXX-XXXX',
        physicalAddress: 'Sample Business Address',
        city: 'Lusaka',
        province: 'Lusaka',
        postalCode: '10101',
        country: 'Zambia',
        productCategories: user.email.includes('food') ? 'FOOD_AND_BEVERAGES' :
                          user.email.includes('tech') ? 'TECHNOLOGY' : 'GENERAL_SERVICES',
        verificationStatus: 'PENDING',
        featured: false,
        totalReviews: 0,
        totalSales: 0,
        totalOrders: 0
      }
    });

    logSuccess(`Created vendor profile for ${user.name}`);
    logInfo(`  Profile ID: ${vendorProfile.id}`);
    logInfo(`  Business Type: ${vendorProfile.businessType}`);
    logInfo(`  Product Categories: ${vendorProfile.productCategories}`);

    return true;

  } catch (error) {
    logError(`Error creating vendor profile for ${user.email}: ${error.message}`);
    return false;
  }
}

async function main() {
  logHeader('Creating Vendor Profiles for Existing Vendor Users');
  
  console.log(`
${colors.cyan}╔══════════════════════════════════════════════════════════════╗
║                🏪 VENDOR PROFILE CREATION                   ║
║                                                              ║
║  Creating vendor profiles for existing vendor users:        ║
║  • <EMAIL>                                       ║
║  • <EMAIL>                                   ║
║  • <EMAIL>                                   ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}
  `);

  try {
    // Find all vendor users
    const vendorUsers = await prisma.user.findMany({
      where: {
        role: 'VENDOR'
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true
      }
    });

    logInfo(`Found ${vendorUsers.length} vendor users`);

    if (vendorUsers.length === 0) {
      logError('No vendor users found in the database');
      return;
    }

    let successCount = 0;
    let failureCount = 0;

    // Create vendor profiles for each vendor user
    for (const user of vendorUsers) {
      const success = await createVendorProfile(user);
      if (success) {
        successCount++;
      } else {
        failureCount++;
      }
      console.log(''); // Add spacing
    }

    // Summary
    logHeader('Summary');
    
    logSuccess(`✅ Created ${successCount} vendor profiles`);
    
    if (failureCount > 0) {
      logError(`❌ ${failureCount} vendor profiles failed to create`);
    }

    if (failureCount === 0) {
      logSuccess('🎉 All vendor profiles created successfully!');
      console.log('\n🚀 Vendor users can now:');
      console.log('   • Log in with their credentials');
      console.log('   • Access vendor dashboard');
      console.log('   • Manage their business profiles');
      console.log('   • Apply for event participation');
      console.log('\n💡 Run "npm run db:check-users" to verify all users are working.');
    } else {
      logError('⚠️  Some vendor profiles could not be created. Check the errors above.');
    }

  } catch (error) {
    logError(`Database error: ${error.message}`);
    console.error(error);
  }
}

// Run the main function
main()
  .catch((error) => {
    logError(`Unexpected error: ${error.message}`);
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
