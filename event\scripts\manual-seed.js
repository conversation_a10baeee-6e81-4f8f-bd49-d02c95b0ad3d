#!/usr/bin/env node

/**
 * Manual Database Seeding Script
 * 
 * This script manually triggers database seeding when automatic seeding fails.
 * Run this if you're getting "No user found" errors during login.
 */

const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logHeader(message) {
  log(`\n🚀 ${message}`, colors.cyan + colors.bright);
}

async function main() {
  logHeader('Manual Database Seeding');
  
  console.log(`
${colors.cyan}╔══════════════════════════════════════════════════════════════╗
║                    🛠️  MANUAL SEEDING                        ║
║                                                              ║
║  This will create all test users including:                 ║
║  • <EMAIL> (Password: Password123)    ║
║  • <EMAIL> (Password: Password123)      ║
║  • <EMAIL> (Password: Password123)      ║
║  • <EMAIL> (Password: Admin@123456)         ║
║  • <EMAIL> (Password: SuperAdmin@1234567) ║
║                                                              ║
║  Plus all partner and system data                           ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}
  `);

  try {
    logInfo('Checking database connection...');
    
    // Try to connect to database
    try {
      execSync('npx prisma db execute --stdin < /dev/null', {
        stdio: 'pipe',
        timeout: 10000
      });
      logSuccess('Database connection established');
    } catch (error) {
      logError('Database connection failed');
      logInfo('Please ensure your database is running and DATABASE_URL is correct');
      process.exit(1);
    }

    logInfo('Running database migrations...');
    try {
      execSync('npx prisma migrate deploy', { stdio: 'inherit' });
      logSuccess('Migrations applied successfully');
    } catch (error) {
      logError('Migration failed, but continuing...');
    }

    logInfo('Running database seeding...');
    logInfo('This may take a few minutes...');
    
    try {
      execSync('npx prisma db seed', {
        stdio: 'inherit',
        timeout: 180000 // 3 minutes timeout
      });
      logSuccess('Database seeding completed successfully!');
    } catch (error) {
      logError('Prisma seeding failed, trying alternative method...');
      
      try {
        execSync('npx tsx prisma/seed.ts', {
          stdio: 'inherit',
          timeout: 180000
        });
        logSuccess('Alternative seeding completed successfully!');
      } catch (altError) {
        logError('All seeding methods failed');
        logError('Error:', altError.message);
        process.exit(1);
      }
    }

    logSuccess('🎉 Manual seeding completed!');
    logInfo('');
    logInfo('You can now log in with these test accounts:');
    logInfo('');
    logInfo('📧 <EMAIL>');
    logInfo('🔑 Password123');
    logInfo('');
    logInfo('📧 <EMAIL>');
    logInfo('🔑 Admin@123456');
    logInfo('');
    logInfo('📧 <EMAIL>');
    logInfo('🔑 SuperAdmin@1234567');
    logInfo('');
    logSuccess('Try logging in again - it should work now!');

  } catch (error) {
    logError(`Unexpected error: ${error.message}`);
    process.exit(1);
  }
}

// Run the main function
main().catch((error) => {
  logError(`Unexpected error: ${error.message}`);
  process.exit(1);
});
