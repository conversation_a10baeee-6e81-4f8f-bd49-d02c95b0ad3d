const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Function to recursively get the size of a directory
function getDirSize(dirPath) {
  let size = 0;
  const files = fs.readdirSync(dirPath);
  
  for (const file of files) {
    const filePath = path.join(dirPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      size += getDirSize(filePath);
    } else {
      size += stats.size;
    }
  }
  
  return size;
}

// Function to format bytes to a human-readable format
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Main function
async function main() {
  console.log('🔍 Analyzing build size...');
  
  // Check if .next directory exists
  if (!fs.existsSync('.next')) {
    console.error('❌ .next directory not found. Run "npm run build" first.');
    process.exit(1);
  }
  
  // Get size of serverless functions
  const serverlessDir = path.join('.next', 'server', 'app');
  if (fs.existsSync(serverlessDir)) {
    const serverlessSize = getDirSize(serverlessDir);
    console.log(`📊 Serverless functions size: ${formatBytes(serverlessSize)}`);
    
    if (serverlessSize > 250 * 1024 * 1024) {
      console.warn('⚠️ Warning: Serverless functions exceed 250MB limit!');
    }
  }
  
  // Get size of static assets
  const staticDir = path.join('.next', 'static');
  if (fs.existsSync(staticDir)) {
    const staticSize = getDirSize(staticDir);
    console.log(`📊 Static assets size: ${formatBytes(staticSize)}`);
  }
  
  console.log('✅ Build analysis complete');
}

// Run the main function
main().catch(err => {
  console.error('❌ Error:', err);
  process.exit(1);
});
