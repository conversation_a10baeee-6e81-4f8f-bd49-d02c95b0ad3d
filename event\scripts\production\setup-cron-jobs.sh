#!/bin/bash

# This script sets up cron jobs for the production environment

# Load environment variables
if [ -f ".env.production" ]; then
  source .env.production
else
  echo "Error: .env.production file not found"
  exit 1
fi

# Ensure CRON_SECRET is set
if [ -z "$CRON_SECRET" ]; then
  CRON_SECRET=$(openssl rand -hex 32)
  echo "Generated CRON_SECRET: $CRON_SECRET"
  
  # Add to .env.production file
  echo "CRON_SECRET=$CRON_SECRET" >> .env.production
  echo "Added CRON_SECRET to .env.production file"
fi

# Get the production domain
if [ -z "$PRODUCTION_DOMAIN" ]; then
  echo "Error: PRODUCTION_DOMAIN not set in .env.production"
  exit 1
fi

# Create a temporary crontab file
TEMP_CRONTAB=$(mktemp)

# Get existing crontab
crontab -l > "$TEMP_CRONTAB" 2>/dev/null || echo "# Event platform cron jobs" > "$TEMP_CRONTAB"

# Remove any existing featuring expiration jobs
grep -v "featuring-expiration" "$TEMP_CRONTAB" > "${TEMP_CRONTAB}.new"
mv "${TEMP_CRONTAB}.new" "$TEMP_CRONTAB"

# Add header comment
echo "# Event platform featuring expiration jobs - Updated $(date)" >> "$TEMP_CRONTAB"

# Add featuring expiration job - runs daily at midnight
echo "0 0 * * * curl -X GET https://${PRODUCTION_DOMAIN}/api/cron/featuring-expiration -H \"Authorization: Bearer ${CRON_SECRET}\" >> /var/log/event-platform/cron.log 2>&1" >> "$TEMP_CRONTAB"

# Add featuring expiration notification job - runs daily at 9 AM
echo "0 9 * * * curl -X GET https://${PRODUCTION_DOMAIN}/api/cron/featuring-expiration-notification -H \"Authorization: Bearer ${CRON_SECRET}\" >> /var/log/event-platform/cron.log 2>&1" >> "$TEMP_CRONTAB"

# Install the new crontab
crontab "$TEMP_CRONTAB"
rm "$TEMP_CRONTAB"

# Create log directory if it doesn't exist
sudo mkdir -p /var/log/event-platform
sudo chown $(whoami) /var/log/event-platform
sudo chmod 755 /var/log/event-platform

echo "Cron jobs set up successfully for featuring expiration"
echo "Logs will be written to /var/log/event-platform/cron.log"
