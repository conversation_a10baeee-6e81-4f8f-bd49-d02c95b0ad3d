#!/usr/bin/env node

/**
 * Master Comprehensive Seeding Script
 * 
 * Runs all seeding scripts in the correct order to create a complete,
 * realistic testing environment for the event management system.
 */

const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message) {
  log(`\n🚀 ${message}`, colors.cyan + colors.bright);
}

function logStep(step, total, message) {
  log(`\n📋 Step ${step}/${total}: ${message}`, colors.magenta + colors.bright);
}

async function runCommand(command, description) {
  logInfo(`Running: ${description}`);
  try {
    execSync(command, { 
      stdio: 'inherit',
      timeout: 300000 // 5 minutes timeout
    });
    logSuccess(`✅ ${description} completed successfully`);
    return true;
  } catch (error) {
    logError(`❌ ${description} failed: ${error.message}`);
    return false;
  }
}

async function main() {
  logHeader('🎯 COMPREHENSIVE EVENT MANAGEMENT SYSTEM SEEDING');
  
  console.log(`
${colors.cyan}╔══════════════════════════════════════════════════════════════╗
║                🎯 COMPLETE SYSTEM SEEDING                    ║
║                                                              ║
║  This will create a fully populated testing environment:    ║
║                                                              ║
║  📊 PHASE 1: Foundation Data                                ║
║  • Users across all roles (29 accounts)                    ║
║  • Events with proper ownership                             ║
║  • Basic system configuration                              ║
║                                                              ║
║  💰 PHASE 2: Transaction & Commerce                         ║
║  • Realistic ticket purchases                              ║
║  • Payment processing records                              ║
║  • Revenue and financial data                              ║
║                                                              ║
║  🎫 PHASE 3: Attendance & Engagement                        ║
║  • Event check-in/check-out records                        ║
║  • User interactions and reviews                           ║
║  • Waitlist and capacity management                        ║
║                                                              ║
║  📈 PHASE 4: Analytics & Insights                           ║
║  • Performance metrics                                      ║
║  • User behavior analytics                                 ║
║  • Business intelligence data                              ║
║                                                              ║
║  🎉 RESULT: Complete testing environment with thousands     ║
║     of realistic data points across all system features    ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}
  `);

  const totalSteps = 8;
  let currentStep = 1;
  let successCount = 0;
  let failureCount = 0;

  // Phase 1: Foundation Data
  logHeader('📊 PHASE 1: Foundation Data');
  
  logStep(currentStep++, totalSteps, 'Database Migration and Setup');
  if (await runCommand('npx prisma migrate deploy', 'Database migrations')) {
    successCount++;
  } else {
    failureCount++;
  }

  logStep(currentStep++, totalSteps, 'Core System Seeding');
  if (await runCommand('npx prisma db seed', 'Core system data (users, events, partners)')) {
    successCount++;
  } else {
    failureCount++;
  }

  logStep(currentStep++, totalSteps, 'Test User Creation');
  if (await runCommand('npm run db:create-all-users', 'All user roles and test accounts')) {
    successCount++;
  } else {
    failureCount++;
  }

  logStep(currentStep++, totalSteps, 'Event Assignment');
  if (await runCommand('npm run db:assign-test-events', 'Events for test organizer accounts')) {
    successCount++;
  } else {
    failureCount++;
  }

  // Phase 2: Transaction & Commerce
  logHeader('💰 PHASE 2: Transaction & Commerce Data');
  
  logStep(currentStep++, totalSteps, 'Transaction and Order Data');
  if (await runCommand('node scripts/seed-comprehensive-data.js', 'Ticket purchases, payments, and financial records')) {
    successCount++;
  } else {
    failureCount++;
  }

  // Phase 3: Attendance & Engagement
  logHeader('🎫 PHASE 3: Attendance & Engagement Data');
  
  logStep(currentStep++, totalSteps, 'Attendance and Check-in Data');
  if (await runCommand('node scripts/seed-attendance-data.js', 'Event attendance, check-ins, and waitlists')) {
    successCount++;
  } else {
    failureCount++;
  }

  // Phase 4: Analytics & Insights
  logHeader('📈 PHASE 4: Analytics & Insights Data');
  
  logStep(currentStep++, totalSteps, 'User Interactions and Reviews');
  if (await runCommand('node scripts/seed-analytics-data.js', 'User interactions, reviews, and analytics')) {
    successCount++;
  } else {
    failureCount++;
  }

  logStep(currentStep++, totalSteps, 'System Verification');
  if (await runCommand('npm run db:check-users', 'Verify all users can authenticate')) {
    successCount++;
  } else {
    failureCount++;
  }

  // Final Summary
  logHeader('🎉 SEEDING COMPLETE - SYSTEM SUMMARY');
  
  if (failureCount === 0) {
    logSuccess(`🎉 ALL ${totalSteps} SEEDING STEPS COMPLETED SUCCESSFULLY!`);
    
    console.log(`
${colors.green}╔══════════════════════════════════════════════════════════════╗
║                    🎉 SUCCESS SUMMARY                        ║
║                                                              ║
║  Your event management system is now fully populated with:  ║
║                                                              ║
║  👥 USERS & ACCOUNTS                                         ║
║  • 29 test user accounts across all roles                  ║
║  • System admins, organizers, partners, vendors, users     ║
║  • All accounts verified and ready for login               ║
║                                                              ║
║  🎪 EVENTS & CONTENT                                         ║
║  • 20+ realistic events with proper ownership              ║
║  • Multiple categories and event types                     ║
║  • Past, current, and upcoming events                      ║
║                                                              ║
║  💰 COMMERCE & TRANSACTIONS                                  ║
║  • Hundreds of ticket purchase transactions                ║
║  • Multiple payment methods and statuses                   ║
║  • Realistic revenue and financial data                    ║
║                                                              ║
║  🎫 ATTENDANCE & ENGAGEMENT                                  ║
║  • Event check-in/check-out records                        ║
║  • User reviews and ratings                                ║
║  • Waitlist and capacity management                        ║
║                                                              ║
║  📊 ANALYTICS & INSIGHTS                                     ║
║  • Performance metrics and KPIs                            ║
║  • User behavior analytics                                 ║
║  • Business intelligence data                              ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}
    `);
    
    logInfo('🚀 READY FOR TESTING:');
    logInfo('');
    logInfo('📧 LOGIN WITH ANY TEST ACCOUNT:');
    logInfo('   • <EMAIL> / Password123');
    logInfo('   • <EMAIL> / Admin@123456');
    logInfo('   • <EMAIL> / Password123!');
    logInfo('   • <EMAIL> / Password123');
    logInfo('');
    logInfo('🎯 TEST ALL FEATURES:');
    logInfo('   • Event creation and management');
    logInfo('   • Ticket sales and payment processing');
    logInfo('   • Attendance tracking and check-ins');
    logInfo('   • Analytics and reporting dashboards');
    logInfo('   • User interactions and reviews');
    logInfo('   • Partner and vendor management');
    logInfo('');
    logInfo('📊 AVAILABLE COMMANDS:');
    logInfo('   • npm run db:check-users - Verify user accounts');
    logInfo('   • npm run db:check-events - Check event ownership');
    logInfo('   • npm run dev - Start the application');
    
  } else {
    logError(`❌ ${failureCount} out of ${totalSteps} steps failed`);
    logSuccess(`✅ ${successCount} steps completed successfully`);
    logWarning('⚠️  Some features may not have complete test data');
    logInfo('💡 You can re-run individual seeding scripts to fix specific issues');
  }

  console.log('\n');
  logHeader('🎊 HAPPY TESTING!');
  logInfo('Your comprehensive event management testing environment is ready!');
}

// Run the main function
main().catch((error) => {
  logError(`Unexpected error: ${error.message}`);
  console.error(error);
  process.exit(1);
});
