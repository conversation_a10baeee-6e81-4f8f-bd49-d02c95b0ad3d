const { execSync } = require('child_process');
const path = require('path');

console.log('Running partner seed script...');

try {
  // Execute the TypeScript seed file with proper escaping
  execSync('ts-node --compiler-options \'{\"module\":\"CommonJS\"}\' prisma/seed-partners.ts', {
    stdio: 'inherit',
  });

  console.log('Partner seed completed successfully!');
} catch (error) {
  console.error('Error running partner seed:', error.message);
  process.exit(1);
}
