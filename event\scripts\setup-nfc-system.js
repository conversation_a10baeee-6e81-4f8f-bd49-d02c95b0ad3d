// scripts/setup-nfc-system.js
const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

async function main() {
  console.log('Starting NFC system setup...');

  try {
    // Check if we have a published event
    console.log('Looking for a published event...');
    let testEvent = await prisma.event.findFirst({
      where: {
        status: 'Published'
      }
    });

    if (!testEvent) {
      console.log('No published events found. Creating a test event...');

      // Find a user with ORGANIZER role
      const organizer = await prisma.user.findFirst({
        where: {
          role: 'ORGANIZER'
        }
      });

      if (!organizer) {
        console.log('No organizer found. Please create an organizer user first.');
        return;
      }

      // Create a test event
      testEvent = await prisma.event.create({
        data: {
          title: `Test Event ${Date.now()}`, // Ensure unique title
          location: 'Lusaka, Zambia',
          venue: 'Test Venue',
          description: 'This is a test event for NFC system',
          category: 'TECHNOLOGY',
          eventType: 'PHYSICAL',
          status: 'Published',
          startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
          endDate: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000), // 8 days from now
          startTime: '10:00',
          endTime: '18:00',
          userId: organizer.id
        }
      });

      console.log('Test event created:', testEvent.title);
    } else {
      console.log('Found published event:', testEvent.title);
    }

    // Create NFC system settings for the test event if they don't exist
    console.log('Setting up NFC system settings...');

    // Check if NFCSystemSettings model exists
    let hasNFCSystemSettings = false;
    try {
      await prisma.$queryRaw`SELECT 1 FROM "NFCSystemSettings" LIMIT 1`;
      hasNFCSystemSettings = true;
    } catch (e) {
      console.log('NFCSystemSettings table does not exist. Creating it...');

      // Create the table using raw SQL - one statement at a time
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS "NFCSystemSettings" (
          "id" TEXT NOT NULL,
          "eventId" TEXT NOT NULL,
          "systemName" TEXT NOT NULL,
          "currencySymbol" TEXT NOT NULL,
          "defaultLanguage" TEXT NOT NULL,
          "maxTransactionAmount" DOUBLE PRECISION NOT NULL,
          "requirePinForHighValue" BOOLEAN NOT NULL,
          "highValueThreshold" DOUBLE PRECISION,
          "cardLockoutThreshold" INTEGER NOT NULL,
          "offlineModeEnabled" BOOLEAN NOT NULL,
          "maxOfflineTransactions" INTEGER,
          "offlineTransactionLimit" DOUBLE PRECISION,
          "syncInterval" INTEGER,
          "receiptEnabled" BOOLEAN NOT NULL,
          "analyticsEnabled" BOOLEAN NOT NULL,
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP(3) NOT NULL,
          CONSTRAINT "NFCSystemSettings_pkey" PRIMARY KEY ("id")
        )
      `;

      // Create index in a separate statement
      await prisma.$executeRaw`
        CREATE UNIQUE INDEX IF NOT EXISTS "NFCSystemSettings_eventId_key" ON "NFCSystemSettings"("eventId")
      `;
    }

    // Check if settings already exist for this event
    const existingSettings = hasNFCSystemSettings ?
      await prisma.$queryRaw`SELECT * FROM "NFCSystemSettings" WHERE "eventId" = ${testEvent.id}` :
      [];

    if (existingSettings.length === 0) {
      // Create settings using raw SQL if the table exists
      if (hasNFCSystemSettings) {
        const settingsId = crypto.randomUUID();
        await prisma.$executeRaw`
          INSERT INTO "NFCSystemSettings" (
            "id", "eventId", "systemName", "currencySymbol", "defaultLanguage",
            "maxTransactionAmount", "requirePinForHighValue", "highValueThreshold",
            "cardLockoutThreshold", "offlineModeEnabled", "maxOfflineTransactions",
            "offlineTransactionLimit", "syncInterval", "receiptEnabled", "analyticsEnabled",
            "createdAt", "updatedAt"
          ) VALUES (
            ${settingsId}, ${testEvent.id}, 'Event NFC Payment System', 'K', 'en',
            500, true, 100, 3, true, 50, 50, 15, true, true,
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          )
        `;
      }

      console.log('NFC system settings created for event:', testEvent.title);
    } else {
      console.log('NFC system settings already exist for this event');
    }

    // Check if NFCCard model has the required fields
    console.log('Checking NFCCard model...');

    let hasNFCCardTable = false;
    try {
      await prisma.$queryRaw`SELECT 1 FROM "NFCCard" LIMIT 1`;
      hasNFCCardTable = true;
    } catch (e) {
      console.log('NFCCard table does not exist. Creating it...');

      // Create the table using raw SQL - one statement at a time
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS "NFCCard" (
          "id" TEXT NOT NULL,
          "uid" TEXT NOT NULL,
          "isActive" BOOLEAN NOT NULL DEFAULT true,
          "status" TEXT NOT NULL DEFAULT 'active',
          "balance" DOUBLE PRECISION NOT NULL DEFAULT 0,
          "assignedTo" TEXT,
          "lastUsed" TIMESTAMP(3),
          "userId" TEXT,
          "eventId" TEXT NOT NULL,
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP(3) NOT NULL,
          CONSTRAINT "NFCCard_pkey" PRIMARY KEY ("id")
        )
      `;

      // Create indexes in separate statements
      await prisma.$executeRaw`CREATE UNIQUE INDEX IF NOT EXISTS "NFCCard_uid_key" ON "NFCCard"("uid")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "NFCCard_uid_idx" ON "NFCCard"("uid")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "NFCCard_isActive_idx" ON "NFCCard"("isActive")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "NFCCard_userId_idx" ON "NFCCard"("userId")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "NFCCard_eventId_idx" ON "NFCCard"("eventId")`;
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "NFCCard_status_idx" ON "NFCCard"("status")`;


      hasNFCCardTable = true;
    }

    // Check if the NFCCard table has the required fields
    if (hasNFCCardTable) {
      try {
        // Check if status column exists
        try {
          await prisma.$queryRaw`SELECT "status" FROM "NFCCard" LIMIT 1`;
        } catch (e) {
          console.log('Adding status column to NFCCard table...');
          await prisma.$executeRaw`ALTER TABLE "NFCCard" ADD COLUMN "status" TEXT NOT NULL DEFAULT 'active'`;
          try {
            await prisma.$executeRaw`CREATE INDEX "NFCCard_status_idx" ON "NFCCard"("status")`;
          } catch (indexError) {
            console.log('Status index already exists or could not be created');
          }
        }

        // Check if balance column exists
        try {
          await prisma.$queryRaw`SELECT "balance" FROM "NFCCard" LIMIT 1`;
        } catch (e) {
          console.log('Adding balance column to NFCCard table...');
          await prisma.$executeRaw`ALTER TABLE "NFCCard" ADD COLUMN "balance" DOUBLE PRECISION NOT NULL DEFAULT 0`;
        }

        // Check if assignedTo column exists
        try {
          await prisma.$queryRaw`SELECT "assignedTo" FROM "NFCCard" LIMIT 1`;
        } catch (e) {
          console.log('Adding assignedTo column to NFCCard table...');
          await prisma.$executeRaw`ALTER TABLE "NFCCard" ADD COLUMN "assignedTo" TEXT`;
        }

        // Check if eventId column exists
        try {
          await prisma.$queryRaw`SELECT "eventId" FROM "NFCCard" LIMIT 1`;
        } catch (e) {
          console.log('Adding eventId column to NFCCard table...');
          await prisma.$executeRaw`ALTER TABLE "NFCCard" ADD COLUMN "eventId" TEXT NOT NULL DEFAULT ''`;
          try {
            await prisma.$executeRaw`CREATE INDEX "NFCCard_eventId_idx" ON "NFCCard"("eventId")`;
          } catch (indexError) {
            console.log('EventId index already exists or could not be created');
          }
        }

        // Make userId nullable if it's not already
        try {
          await prisma.$executeRaw`ALTER TABLE "NFCCard" ALTER COLUMN "userId" DROP NOT NULL`;
        } catch (e) {
          // Column might already be nullable, ignore error
        }
      } catch (e) {
        console.error('Error updating NFCCard table:', e);
      }
    }

    // Create test NFC cards
    console.log('Creating test NFC cards...');

    // Get a test user
    const testUser = await prisma.user.findFirst({
      where: {
        role: 'USER'
      }
    });

    if (!testUser) {
      console.log('No users found. Please create a user first.');
      return;
    }

    // Create 5 test NFC cards
    const cards = [];
    for (let i = 1; i <= 5; i++) {
      const cardUid = `NFC-${Math.floor(Math.random() * 900000) + 100000}`;

      // Check if card already exists
      const existingCard = await prisma.nFCCard.findFirst({
        where: { uid: cardUid }
      });

      if (!existingCard) {
        const card = await prisma.nFCCard.create({
          data: {
            uid: cardUid,
            status: 'active',
            balance: Math.floor(Math.random() * 500) + 100,
            assignedTo: i <= 3 ? testUser.name : null,
            userId: i <= 3 ? testUser.id : null,
            eventId: testEvent.id,
            isActive: true
          }
        });

        cards.push(card);
        console.log(`Created NFC card: ${card.uid}`);
      }
    }

    console.log(`Created ${cards.length} test NFC cards`);

    // Create test vendor if needed
    let vendorUser = await prisma.user.findFirst({
      where: {
        role: 'VENDOR'
      },
      include: {
        vendorProfile: true
      }
    });

    if (!vendorUser || !vendorUser.vendorProfile) {
      console.log('Creating test vendor...');

      // Create vendor user if needed
      if (!vendorUser) {
        vendorUser = await prisma.user.create({
          data: {
            name: 'Test Vendor',
            email: `vendor${Date.now()}@example.com`, // Ensure unique email
            role: 'VENDOR',
            emailVerified: new Date()
          }
        });
      }

      // Create vendor profile
      const vendorProfile = await prisma.vendorProfile.create({
        data: {
          userId: vendorUser.id,
          businessName: 'Test Vendor Business',
          businessType: 'Food & Beverages',
          productCategories: 'FOOD_AND_BEVERAGES',
          verificationStatus: 'APPROVED',
          city: 'Lusaka',
          province: 'Lusaka',
          phoneNumber: '+260123456789'
        }
      });

      console.log('Vendor profile created:', vendorProfile.businessName);

      // Create vendor participation in the event
      await prisma.eventVendor.create({
        data: {
          eventId: testEvent.id,
          vendorId: vendorProfile.id,
          status: 'APPROVED',
          boothNumber: 'A-123'
        }
      });

      console.log('Vendor added to event');
    }

    // Create test products for the vendor
    const vendorProfile = vendorUser.vendorProfile ||
      await prisma.vendorProfile.findUnique({
        where: { userId: vendorUser.id }
      });

    console.log('Creating test products...');

    const productNames = [
      'Burger', 'Pizza', 'Soft Drink', 'Ice Cream', 'Coffee'
    ];

    const products = [];
    for (const name of productNames) {
      // Check if product already exists
      const existingProduct = await prisma.product.findFirst({
        where: {
          name,
          vendorId: vendorProfile.id
        }
      });

      if (!existingProduct) {
        const product = await prisma.product.create({
          data: {
            name,
            description: `Delicious ${name.toLowerCase()}`,
            price: Math.floor(Math.random() * 50) + 10,
            userId: vendorUser.id,
            vendorId: vendorProfile.id,
            eventId: testEvent.id,
            category: 'FOOD_AND_BEVERAGES',
            dimensions: '10x10x5',
            material: 'Food',
            productType: 'PHYSICAL',
            status: 'Onsale',
            stockQuantity: 100,
            weight: 0.5
          }
        });

        products.push(product);
        console.log(`Created product: ${product.name}`);
      }
    }

    console.log(`Created ${products.length} test products`);

    // Check if VendorNFCTransaction and VendorNFCTransactionItem tables exist
    let hasTransactionTables = false;
    try {
      await prisma.$queryRaw`SELECT 1 FROM "VendorNFCTransaction" LIMIT 1`;
      await prisma.$queryRaw`SELECT 1 FROM "VendorNFCTransactionItem" LIMIT 1`;
      hasTransactionTables = true;
    } catch (e) {
      console.log('Transaction tables do not exist. Skipping transaction creation.');
    }

    if (hasTransactionTables) {
      // Create test transactions
      console.log('Creating test transactions...');

      // Get all cards
      const allCards = await prisma.nFCCard.findMany({
        where: {
          eventId: testEvent.id,
          isActive: true
        }
      });

      // Get all products
      const allProducts = await prisma.product.findMany({
        where: {
          vendorId: vendorProfile.id
        }
      });

      if (allCards.length > 0 && allProducts.length > 0) {
        const transactions = [];
        for (let i = 0; i < Math.min(3, allCards.length); i++) {
          const card = allCards[i];

          // Select 1-3 random products
          const numProducts = Math.floor(Math.random() * 3) + 1;
          const selectedProducts = [];
          for (let j = 0; j < numProducts; j++) {
            const product = allProducts[Math.floor(Math.random() * allProducts.length)];
            const quantity = Math.floor(Math.random() * 3) + 1;
            selectedProducts.push({
              productId: product.id,
              quantity,
              unitPrice: product.price,
              totalPrice: product.price * quantity
            });
          }

          // Calculate total amount
          const totalAmount = selectedProducts.reduce((sum, item) => sum + item.totalPrice, 0);

          // Create transaction
          const transaction = await prisma.vendorNFCTransaction.create({
            data: {
              vendorId: vendorProfile.id,
              eventId: testEvent.id,
              cardId: card.id,
              userId: card.userId || testUser.id,
              amount: totalAmount,
              status: 'COMPLETED',
              processedAt: new Date(),
              products: {
                create: selectedProducts
              }
            },
            include: {
              products: true
            }
          });

          transactions.push(transaction);
          console.log(`Created transaction: ${transaction.id}`);

          // Update card balance
          await prisma.nFCCard.update({
            where: { id: card.id },
            data: {
              balance: { decrement: totalAmount },
              lastUsed: new Date()
            }
          });
        }

        console.log(`Created ${transactions.length} test transactions`);
      } else {
        console.log('No cards or products available for creating transactions');
      }
    }

    console.log('NFC system setup completed successfully!');

  } catch (error) {
    console.error('Error during setup:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
