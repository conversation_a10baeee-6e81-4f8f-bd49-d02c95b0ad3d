// 'use server'

// import { hash } from 'bcryptjs'
// import { db } from "@/lib/prisma"
// import { getUserByEmail } from "@/data/user"
// import { sendVerificationEmail } from "@/lib/mail"
// import { generateVerificationToken } from "@/data/tokens"
// import { registerSchema, RegisterSchema } from "@/schemas"

// export async function register(values: RegisterSchema) {
//   const validatedFields = registerSchema.safeParse(values)

//   if (!validatedFields.success) {
//     return { error: 'Invalid fields!' }
//   }

//   const { name, email, password } = validatedFields.data
//   const hashedPassword = await hash(password, 10)

//   const existingUser = await getUserByEmail(email)

//   if (existingUser) {
//     return { error: 'Email already in use!' }
//   }

//   await db.user.create({
//     data: {
//       name,
//       email,
//       password: hashedPassword,
//     }
//   })

//   const verificationToken = await generateVerificationToken(email)
//   await sendVerificationEmail(verificationToken.email, verificationToken.token)

//   return { success: 'Confirmation email sent!' }
// }


'use server'

import { hash } from 'bcryptjs'
import { db } from "@/lib/prisma"
import { getUserByEmail } from "@/data/user"
import { sendVerificationEmail } from "@/lib/mail"
import { generateVerificationToken } from "@/data/tokens"
import { registerSchema, RegisterSchema } from "@/schemas"
import crypto from 'crypto'

export async function register(values: RegisterSchema) {
  const validatedFields = registerSchema.safeParse(values)

  if (!validatedFields.success) {
    return { error: 'Invalid fields!' }
  }

  const { name, email, password, role } = validatedFields.data
  console.log("[Register Action] Attempting to register user:", { name, email, role });
  console.log("[Register Action] Password received for hashing:", `"${password}"`);
  const hashedPassword = await hash(password, 10)
  console.log("[Register Action] Password hashed to:", hashedPassword);

  const existingUser = await getUserByEmail(email)

  if (existingUser) {
    return { error: 'Email already in use!' }
  }

  // Generate a random access token
  const accessToken = crypto.randomBytes(32).toString('hex');

  // Create user with access token
  await db.user.create({
    data: {
      name,
      email,
      password: hashedPassword,
      role: role,
      accessToken: accessToken,
    }
  })

  console.log(`Created user with email ${email} and access token`);

  const verificationToken = await generateVerificationToken(email)

  console.log('Generated verification token:', verificationToken)

  const emailResult = await sendVerificationEmail(verificationToken.email, verificationToken.token)

  if (!emailResult.success) {
    console.error('Failed to send verification email:', emailResult.error)
    return { error: 'Failed to send verification email. Please try again or contact support.' }
  }

  return {
    success: 'Confirmation email sent! Please check your inbox and spam folder.',
    redirectTo: `/auth/welcome?role=${role}&email=${encodeURIComponent(email)}`
  }
}
