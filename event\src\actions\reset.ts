'use server'

import { resetSchema, ResetSchema } from "@/schemas"
import { getUserByEmail } from "@/data/user"
import { generatePasswordResetToken } from "@/data/tokens"
import { sendPasswordResetEmail } from "@/lib/mail"
import { getAccountByUserId } from "@/data/account"
import { isLoginBlocked, recordFailedLoginAttempt } from "@/lib/rate-limiter-auth"
import { headers } from "next/headers"

export async function reset(values: ResetSchema) {
  // Get IP address from headers
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || 'unknown-ip';
  const userAgent = headersList.get('user-agent') || 'unknown-browser';

  const validatedFields = resetSchema.safeParse(values)

  if (!validatedFields.success) {
    return { error: 'Invalid email!' }
  }

  const { email } = validatedFields.data

  // Check if password reset attempts are blocked for this IP
  const blockStatus = await isLoginBlocked(email, ipAddress);
  if (blockStatus.isBlocked) {
    const minutes = Math.ceil((blockStatus.timeLeft || 0) / 60);
    return {
      error: `Too many password reset attempts. Please try again in ${minutes} minute${minutes > 1 ? 's' : ''}.`
    };
  }

  const existingUser = await getUserByEmail(email)

  if (!existingUser) {
    // Record failed attempt for non-existent email
    await recordFailedLoginAttempt(email, ipAddress, userAgent);
    return { error: 'Email not found!' }
  }

  const existingAccount = await getAccountByUserId(existingUser.id)

  if (existingAccount) {
    // Record failed attempt for OAuth accounts
    await recordFailedLoginAttempt(email, ipAddress, userAgent);
    return { error: 'You are registered with a provider!' }
  }


  const passwordResetToken = await generatePasswordResetToken(email)
  await sendPasswordResetEmail(passwordResetToken.email, passwordResetToken.token)

  return { success: 'Reset email sent!' }
}