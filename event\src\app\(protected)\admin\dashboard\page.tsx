'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useCurrentRole } from '@/hooks/use-current-role';
import {
  BarChart3,
  Users,
  Calendar,
  CheckCircle,
  AlertTriangle,
  DollarSign,
  Clock,
  ArrowUpRight,
  Ticket,
  Building,
  FileText,
  Activity,
  UserPlus,
  Star,
  Store,
  Mail
} from 'lucide-react';
import Link from 'next/link';

// Dashboard stats interface
interface DashboardStats {
  totalUsers: number;
  totalEvents: number;
  pendingVerifications: number;
  pendingWithdrawals: number;
  totalRevenue: number;
  activeEvents: number;
  totalTicketsSold: number;
  // User management stats
  activeUsers: number;
  pendingInvitations: number;
  recentActivity: number;
  recentUsers: {
    id: string;
    name: string;
    email: string;
    role: string;
    createdAt: string;
  }[];
  recentEvents: {
    id: string;
    title: string;
    status: string;
    createdAt: string;
  }[];
}

export default function AdminDashboardPage() {
  const router = useRouter();
  const user = useCurrentUser();
  const role = useCurrentRole();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch dashboard stats
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/dashboard');

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        setStats(data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        setError(error instanceof Error ? error.message : 'Failed to load dashboard data');
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  // Access control - only ADMIN and SUPERADMIN can access
  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin privileges to view this page.</p>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-6 my-6">
          <h3 className="text-lg font-semibold mb-2">Error Loading Dashboard</h3>
          <p>{error}</p>
          <Button
            onClick={() => {
              setError(null);
              setLoading(true);
              fetch('/api/admin/dashboard')
                .then(res => res.json())
                .then(data => {
                  setStats(data);
                  setLoading(false);
                })
                .catch(err => {
                  setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
                  setLoading(false);
                });
            }}
            className="mt-4"
          >
            Try Again
          </Button>
        </div>
      ) : (
        <>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-gray-500 mt-1">
            Manage your platform, users, and events
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-3">
          <Button asChild variant="outline">
            <Link href="/admin/settings">
              <FileText className="mr-2 h-4 w-4" />
              Settings
            </Link>
          </Button>
          <Button asChild>
            <Link href="/admin/reports">
              <BarChart3 className="mr-2 h-4 w-4" />
              View Reports
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-100">
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium text-blue-600">Total Users</p>
                <h3 className="text-3xl font-bold mt-1">{stats?.totalUsers.toLocaleString()}</h3>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4">
              <Button asChild variant="link" className="p-0 h-auto text-blue-600">
                <Link href="/admin/user-management" className="flex items-center">
                  Manage users
                  <ArrowUpRight className="ml-1 h-3 w-3" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-100">
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium text-purple-600">Total Events</p>
                <h3 className="text-3xl font-bold mt-1">{stats?.totalEvents.toLocaleString()}</h3>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <Calendar className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4">
              <Button asChild variant="link" className="p-0 h-auto text-purple-600">
                <Link href="/admin/events" className="flex items-center">
                  View all events
                  <ArrowUpRight className="ml-1 h-3 w-3" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-amber-50 to-yellow-50 border-amber-100">
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium text-amber-600">Pending Verifications</p>
                <h3 className="text-3xl font-bold mt-1">{stats?.pendingVerifications.toLocaleString()}</h3>
              </div>
              <div className="bg-amber-100 p-3 rounded-full">
                <CheckCircle className="h-6 w-6 text-amber-600" />
              </div>
            </div>
            <div className="mt-4 flex flex-col space-y-1">
              <Button asChild variant="link" className="p-0 h-auto text-amber-600">
                <Link href="/admin/verifications" className="flex items-center">
                  Review organizer verifications
                  <ArrowUpRight className="ml-1 h-3 w-3" />
                </Link>
              </Button>
              <Button asChild variant="link" className="p-0 h-auto text-amber-600">
                <Link href="/admin/verifications/vendor" className="flex items-center">
                  Review vendor verifications
                  <ArrowUpRight className="ml-1 h-3 w-3" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-100">
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium text-green-600">Total Revenue</p>
                <h3 className="text-3xl font-bold mt-1">${stats?.totalRevenue.toLocaleString()}</h3>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4">
              <div className="flex flex-col space-y-1">
                <Button asChild variant="link" className="p-0 h-auto text-green-600">
                  <Link href="/admin/finance" className="flex items-center">
                    View financials
                    <ArrowUpRight className="ml-1 h-3 w-3" />
                  </Link>
                </Button>
                <Button asChild variant="link" className="p-0 h-auto text-green-600">
                  <Link href="/admin/finance/reports" className="flex items-center">
                    Revenue reports
                    <ArrowUpRight className="ml-1 h-3 w-3" />
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-semibold text-gray-700">Active Events</h3>
              <div className="bg-blue-100 p-2 rounded-full">
                <Activity className="h-4 w-4 text-blue-600" />
              </div>
            </div>
            <div className="flex items-baseline">
              <span className="text-2xl font-bold">{stats?.activeEvents}</span>
              <span className="ml-2 text-sm text-gray-500">events</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-semibold text-gray-700">Tickets Sold</h3>
              <div className="bg-purple-100 p-2 rounded-full">
                <Ticket className="h-4 w-4 text-purple-600" />
              </div>
            </div>
            <div className="flex items-baseline">
              <span className="text-2xl font-bold">{stats?.totalTicketsSold.toLocaleString()}</span>
              <span className="ml-2 text-sm text-gray-500">tickets</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-semibold text-gray-700">Pending Withdrawals</h3>
              <div className="bg-amber-100 p-2 rounded-full">
                <Clock className="h-4 w-4 text-amber-600" />
              </div>
            </div>
            <div className="flex items-baseline">
              <span className="text-2xl font-bold">{stats?.pendingWithdrawals}</span>
              <span className="ml-2 text-sm text-gray-500">requests</span>
            </div>
            <Button asChild variant="link" className="p-0 h-auto mt-2 text-amber-600">
              <Link href="/admin/withdrawals" className="flex items-center text-sm">
                Process withdrawals
                <ArrowUpRight className="ml-1 h-3 w-3" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* User Management Stats */}
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-semibold text-gray-700">Active Users</h3>
              <div className="bg-green-100 p-2 rounded-full">
                <Users className="h-4 w-4 text-green-600" />
              </div>
            </div>
            <div className="flex items-baseline">
              <span className="text-2xl font-bold">{stats?.activeUsers.toLocaleString()}</span>
              <span className="ml-2 text-sm text-gray-500">verified</span>
            </div>
            <Button asChild variant="link" className="p-0 h-auto mt-2 text-green-600">
              <Link href="/admin/user-management" className="flex items-center text-sm">
                Manage users
                <ArrowUpRight className="ml-1 h-3 w-3" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-semibold text-gray-700">Pending Invitations</h3>
              <div className="bg-orange-100 p-2 rounded-full">
                <Mail className="h-4 w-4 text-orange-600" />
              </div>
            </div>
            <div className="flex items-baseline">
              <span className="text-2xl font-bold">{stats?.pendingInvitations}</span>
              <span className="ml-2 text-sm text-gray-500">invites</span>
            </div>
            <Button asChild variant="link" className="p-0 h-auto mt-2 text-orange-600">
              <Link href="/admin/user-management?tab=activity" className="flex items-center text-sm">
                View invitations
                <ArrowUpRight className="ml-1 h-3 w-3" />
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Users */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="mr-2 h-5 w-5 text-blue-600" />
              Recent Users
            </CardTitle>
            <CardDescription>
              New users who have joined the platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats?.recentUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between border-b pb-3 last:border-0">
                  <div>
                    <p className="font-medium">{user.name}</p>
                    <p className="text-sm text-gray-500">{user.email}</p>
                  </div>
                  <div className="flex items-center">
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full mr-2">
                      {user.role}
                    </span>
                    <span className="text-xs text-gray-500">{user.createdAt}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" className="w-full">
              <Link href="/admin/users">View All Users</Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Recent Events */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5 text-purple-600" />
              Recent Events
            </CardTitle>
            <CardDescription>
              Recently created events on the platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats?.recentEvents.map((event) => (
                <div key={event.id} className="flex items-center justify-between border-b pb-3 last:border-0">
                  <div>
                    <p className="font-medium">{event.title}</p>
                    <p className="text-sm text-gray-500">Created on {event.createdAt}</p>
                  </div>
                  <div>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      event.status === 'Published' ? 'bg-green-100 text-green-800' :
                      event.status === 'Draft' ? 'bg-amber-100 text-amber-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {event.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" className="w-full">
              <Link href="/admin/events">View All Events</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common administrative tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
              <Link href="/admin/user-management">
                <Users className="h-6 w-6 mb-2" />
                <span>User Management</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
              <Link href="/admin/organizers">
                <Users className="h-6 w-6 mb-2" />
                <span>Manage Organizers</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
              <Link href="/admin/events">
                <Calendar className="h-6 w-6 mb-2" />
                <span>Manage Events</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
              <Link href="/admin/teams">
                <UserPlus className="h-6 w-6 mb-2" />
                <span>Manage Teams</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
              <Link href="/admin/withdrawals">
                <DollarSign className="h-6 w-6 mb-2" />
                <span>Process Withdrawals</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
              <Link href="/admin/verifications">
                <CheckCircle className="h-6 w-6 mb-2" />
                <span>Organizer Verifications</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
              <Link href="/admin/verifications/vendor">
                <Store className="h-6 w-6 mb-2" />
                <span>Vendor Verifications</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto py-4 flex flex-col items-center justify-center">
              <Link href="/admin/finance/subscription-tiers">
                <Star className="h-6 w-6 mb-2" />
                <span>Subscription Tiers</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
        </>
      )}
    </div>
  );
}
