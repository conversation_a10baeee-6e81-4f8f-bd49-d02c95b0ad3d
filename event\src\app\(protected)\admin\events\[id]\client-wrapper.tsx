'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Separator } from '@/components/ui/separator';
import { useCurrentRole } from '@/hooks/use-current-role';
import AdminEventDetailClientPage from './client-page';

interface ClientWrapperProps {
  eventId: string;
}

export default function AdminEventDetailClientWrapper({ eventId }: ClientWrapperProps) {
  const router = useRouter();
  const role = useCurrentRole();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if the event exists
  useEffect(() => {
    const checkEvent = async () => {
      try {
        const response = await fetch(`/api/admin/events/${eventId}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError('Event not found');
          } else {
            setError('Failed to load event');
          }
        }

        setLoading(false);
      } catch (error) {
        setError('Failed to load event');
        setLoading(false);
      }
    };

    checkEvent();
  }, [eventId]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <h2 className="text-2xl font-bold mb-2">Event Not Found</h2>
              <p className="text-gray-600 mb-6">The event you&apos;re looking for doesn&apos;t exist or has been removed.</p>
              <Button asChild>
                <Link href="/admin/events">Back to Events</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Access control - only ADMIN and SUPERADMIN can access
  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin privileges to view this page.</p>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </div>
      </div>
    );
  }

  return <AdminEventDetailClientPage eventId={eventId} />;
}
