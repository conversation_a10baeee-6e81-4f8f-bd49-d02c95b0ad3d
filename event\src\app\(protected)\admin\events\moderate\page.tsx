"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  AlertTriangle, 
  CheckCircle, 
  Eye, 
  Calendar,
  MapPin,
  User,
  Clock,
  Users
} from 'lucide-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { RoleGate } from '@/components/auth/role-gate';

interface Event {
  id: string;
  title: string;
  description: string;
  status: string;
  startDate: string;
  endDate: string;
  location: string;
  venue: string;
  category: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  _count: {
    tickets: number;
    attendance: number;
  };
  createdAt: string;
}

export default function EventModerationPage() {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [moderationAction, setModerationAction] = useState<'suspend' | 'restore' | null>(null);
  const [reason, setReason] = useState('');
  const [statusFilter, setStatusFilter] = useState('Published');

  useEffect(() => {
    fetchEvents();
  }, [statusFilter]);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/events/moderate?status=${statusFilter}`);
      const data = await response.json();
      
      if (response.ok) {
        setEvents(data.events);
      } else {
        toast.error('Failed to fetch events');
      }
    } catch (error) {
      console.error('Error fetching events:', error);
      toast.error('Failed to fetch events');
    } finally {
      setLoading(false);
    }
  };

  const handleModeration = async () => {
    if (!selectedEvent || !moderationAction) return;

    try {
      const response = await fetch('/api/admin/events/moderate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventId: selectedEvent.id,
          action: moderationAction,
          reason: reason.trim() || undefined
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`Event ${moderationAction}ed successfully`);
        setSelectedEvent(null);
        setModerationAction(null);
        setReason('');
        fetchEvents(); // Refresh the list
      } else {
        toast.error(data.error || 'Failed to moderate event');
      }
    } catch (error) {
      console.error('Error moderating event:', error);
      toast.error('Failed to moderate event');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Published: { color: 'bg-green-100 text-green-800', label: 'Published' },
      Suspended: { color: 'bg-red-100 text-red-800', label: 'Suspended' },
      Draft: { color: 'bg-gray-100 text-gray-800', label: 'Draft' },
      Cancelled: { color: 'bg-yellow-100 text-yellow-800', label: 'Cancelled' },
      Completed: { color: 'bg-blue-100 text-blue-800', label: 'Completed' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  return (
    <RoleGate allowedRole={["ADMIN", "SUPERADMIN"]}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Event Moderation</h1>
            <p className="text-gray-600">Monitor and moderate published events</p>
          </div>
        </div>

        {/* Status Filter */}
        <div className="flex gap-2">
          {['Published', 'Suspended'].map((status) => (
            <Button
              key={status}
              variant={statusFilter === status ? 'default' : 'outline'}
              onClick={() => setStatusFilter(status)}
            >
              {status} Events
            </Button>
          ))}
        </div>

        {/* Events List */}
        <div className="grid gap-4">
          {loading ? (
            <div className="text-center py-8">Loading events...</div>
          ) : events.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-gray-500">No {statusFilter.toLowerCase()} events found</p>
              </CardContent>
            </Card>
          ) : (
            events.map((event) => (
              <Card key={event.id}>
                <CardContent className="p-6">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold">{event.title}</h3>
                        {getStatusBadge(event.status)}
                      </div>
                      
                      <p className="text-gray-600 mb-3 line-clamp-2">{event.description}</p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          <span>{event.user.name}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(event.startDate).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          <span>{event.location}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4" />
                          <span>{event._count.attendance} attendees</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 ml-4">
                      {event.status === 'Published' && (
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => {
                            setSelectedEvent(event);
                            setModerationAction('suspend');
                          }}
                        >
                          <AlertTriangle className="h-4 w-4 mr-1" />
                          Suspend
                        </Button>
                      )}
                      
                      {event.status === 'Suspended' && (
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => {
                            setSelectedEvent(event);
                            setModerationAction('restore');
                          }}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Restore
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Moderation Dialog */}
        <Dialog open={!!selectedEvent} onOpenChange={() => {
          setSelectedEvent(null);
          setModerationAction(null);
          setReason('');
        }}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {moderationAction === 'suspend' ? 'Suspend Event' : 'Restore Event'}
              </DialogTitle>
              <DialogDescription>
                {moderationAction === 'suspend' 
                  ? 'This will remove the event from public view and notify the organizer.'
                  : 'This will make the event public again and notify the organizer.'
                }
              </DialogDescription>
            </DialogHeader>
            
            {selectedEvent && (
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">{selectedEvent.title}</h4>
                  <p className="text-sm text-gray-500">by {selectedEvent.user.name}</p>
                </div>
                
                {moderationAction === 'suspend' && (
                  <div>
                    <label className="text-sm font-medium">Reason (optional)</label>
                    <Textarea
                      value={reason}
                      onChange={(e) => setReason(e.target.value)}
                      placeholder="Explain why this event is being suspended..."
                      className="mt-1"
                    />
                  </div>
                )}
              </div>
            )}
            
            <DialogFooter>
              <Button variant="outline" onClick={() => {
                setSelectedEvent(null);
                setModerationAction(null);
                setReason('');
              }}>
                Cancel
              </Button>
              <Button 
                variant={moderationAction === 'suspend' ? 'destructive' : 'default'}
                onClick={handleModeration}
              >
                {moderationAction === 'suspend' ? 'Suspend Event' : 'Restore Event'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </RoleGate>
  );
}
