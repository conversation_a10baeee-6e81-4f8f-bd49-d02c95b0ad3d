'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useCurrentRole } from '@/hooks/use-current-role';
import { CheckCircle, XCircle, Search, Calendar, MapPin, Tag, Clock, Eye, AlertTriangle } from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { toast } from '@/components/ui/use-toast';
import Image from 'next/image';

// Event interface
interface Event {
  id: string;
  title: string;
  description: string;
  startDate: string;
  endDate: string | null;
  location: string;
  venue: string;
  category: string;
  status: 'Draft' | 'Pending' | 'UnderReview' | 'Approved' | 'Published' | 'Rejected' | 'Cancelled' | 'Completed';
  eventType: 'PHYSICAL' | 'ONLINE' | 'HYBRID';
  createdAt: string;
  updatedAt: string;
  imagePath: string | null;
  isFree: boolean;
  regularPrice: number | null;
  organizer: {
    id: string;
    name: string;
    email: string;
    isVerified?: boolean;
  };
  stats?: {
    totalOrders: number;
    totalTickets: number;
    engagement: {
      views: number;
      clicks: number;
      shares: number;
      likes: number;
    }
  };
}

export default function AdminEventReviewPage() {
  const router = useRouter();
  const user = useCurrentUser();
  const role = useCurrentRole();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogAction, setDialogAction] = useState<'approve' | 'reject' | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch events
  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true);
      try {
        // Fetch real data from the API
        const response = await fetch(`/api/admin/events/review?search=${encodeURIComponent(searchTerm)}`);

        if (!response.ok) {
          throw new Error(`API returned status: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.events) {
          setEvents(data.events);
        } else {
          setEvents([]);
        }
        setLoading(false);
      } catch (error) {
        console.error('Error fetching events for review:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch events for review. Please try again.',
          variant: 'destructive',
        });
        setLoading(false);
      }
    };

    fetchEvents();
  }, [searchTerm]);

  /* Mock data for reference - commented out
  const mockEvents: Event[] = [
    // Mock event data here
  ];
  */

  // Handle event action
  const handleEventAction = async () => {
    if (!selectedEvent || !dialogAction) return;

    setIsSubmitting(true);

    try {
      // Call the API to update the event status
      const status = dialogAction === 'approve' ? 'Approved' : 'Rejected';

      if (dialogAction === 'reject' && !rejectionReason.trim()) {
        toast({
          title: 'Error',
          description: 'Please provide a reason for rejection',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      const response = await fetch('/api/admin/events/review', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventId: selectedEvent.id,
          status,
          reviewNotes: rejectionReason
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update event status: ${response.status}`);
      }

      const data = await response.json();

      let successMessage = '';

      if (dialogAction === 'approve') {
        successMessage = `Event "${selectedEvent.title}" has been approved and published`;
      } else if (dialogAction === 'reject') {
        successMessage = `Event "${selectedEvent.title}" has been rejected`;
      }

      toast({
        title: 'Success',
        description: successMessage,
      });

      // Update local state
      setEvents(events.filter(event => event.id !== selectedEvent.id));

      // Close dialog and reset state
      setIsDialogOpen(false);
      setSelectedEvent(null);
      setDialogAction(null);
      setRejectionReason('');
    } catch (error) {
      console.error(`Error ${dialogAction}ing event:`, error);
      toast({
        title: 'Error',
        description: `Failed to ${dialogAction} event. Please try again.`,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Open dialog for event action
  const openActionDialog = (event: Event, action: 'approve' | 'reject') => {
    setSelectedEvent(event);
    setDialogAction(action);
    setRejectionReason('');
    setIsDialogOpen(true);
  };

  // Format date for display
  const formatDateDisplay = (dateString: string) => {
    return format(new Date(dateString), 'PPP');
  };

  // Get event type badge
  const getEventTypeBadge = (type: Event['eventType']) => {
    switch (type) {
      case 'PHYSICAL':
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">In-Person</Badge>;
      case 'ONLINE':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">Online</Badge>;
      case 'HYBRID':
        return <Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">Hybrid</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">{type}</Badge>;
    }
  };

  // Access control - only ADMIN and SUPERADMIN can access
  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin privileges to view this page.</p>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Event Review</h1>
          <p className="text-gray-500 mt-1">
            Review and approve events before they are published
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button asChild>
            <Link href="/admin/events">
              View All Events
            </Link>
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="mb-8">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            type="text"
            placeholder="Search events..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Events for review */}
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : events.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <div className="mx-auto w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-8 w-8 text-blue-500" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">No events to review</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              All events have been reviewed. Check back later for new submissions.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {events.map((event) => (
            <Card key={event.id} className="overflow-hidden hover:shadow-md transition-shadow">
              {/* Event image */}
              <div className="relative h-48 w-full">
                <Image
                  src={event.imagePath || '/placeholder-event.jpg'}
                  alt={event.title}
                  fill
                  className="object-cover"
                />
                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-t from-black/60 to-transparent"></div>
                <div className="absolute bottom-4 left-4 right-4">
                  <h3 className="text-white font-bold text-lg line-clamp-1">{event.title}</h3>
                  <p className="text-white/80 text-sm">by {event.organizer.name}</p>
                </div>
              </div>

              <CardContent className="p-4">
                <div className="flex flex-wrap gap-2 mb-3">
                  {getEventTypeBadge(event.eventType)}
                  <Badge className="bg-gray-100 text-gray-800">{event.category.replace(/_/g, ' ')}</Badge>
                </div>

                <p className="text-sm text-gray-600 line-clamp-2 mb-4">{event.description}</p>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    <span>{formatDateDisplay(event.startDate)}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                    <span>{event.location}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Tag className="h-4 w-4 text-gray-400 mr-2" />
                    <span>{event.isFree ? 'Free' : `${event.regularPrice} ZMW`}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-400 mr-2" />
                    <span>Submitted {format(new Date(event.createdAt), 'PP')}</span>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() => router.push(`/events/${event.id}`)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                  <Button
                    variant="outline"
                    className="flex-1 border-red-200 text-red-600 hover:bg-red-50"
                    onClick={() => openActionDialog(event, 'reject')}
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject
                  </Button>
                  <Button
                    className="flex-1 bg-green-600 hover:bg-green-700"
                    onClick={() => openActionDialog(event, 'approve')}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approve
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Action Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {dialogAction === 'approve' ? 'Approve Event' : 'Reject Event'}
            </DialogTitle>
            <DialogDescription>
              {dialogAction === 'approve'
                ? 'This event will be published and visible to all users.'
                : 'This event will be rejected and the organizer will be notified.'}
            </DialogDescription>
          </DialogHeader>

          {selectedEvent && (
            <div className="space-y-4 py-2">
              <div className="p-3 bg-gray-50 rounded-md">
                <h3 className="font-medium">{selectedEvent.title}</h3>
                <p className="text-sm text-gray-500">Organized by {selectedEvent.organizer.name}</p>
              </div>

              {dialogAction === 'reject' && (
                <div className="space-y-2">
                  <label htmlFor="rejection-reason" className="text-sm font-medium">
                    Rejection Reason <span className="text-red-500">*</span>
                  </label>
                  <Textarea
                    id="rejection-reason"
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    placeholder="Provide a reason for rejecting this event"
                    required
                  />
                  <p className="text-xs text-gray-500">
                    This message will be sent to the organizer.
                  </p>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleEventAction}
              disabled={isSubmitting || (dialogAction === 'reject' && !rejectionReason)}
              className={
                dialogAction === 'approve' ? 'bg-green-600 hover:bg-green-700' :
                'bg-red-600 hover:bg-red-700'
              }
            >
              {isSubmitting ? 'Processing...' :
               dialogAction === 'approve' ? 'Approve & Publish' :
               'Reject Event'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
