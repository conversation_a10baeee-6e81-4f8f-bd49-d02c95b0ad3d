'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useCurrentRole } from '@/hooks/use-current-role';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from '@/components/ui/use-toast';
import {
  PlusCircle,
  Edit,
  Trash2,
  Check,
  X,
  Percent,
  DollarSign
} from 'lucide-react';
import { format } from 'date-fns';

// Fee types
const feeTypes = [
  { value: 'PLATFORM_COMMISSION', label: 'Platform Commission' },
  { value: 'PROCESSING_FEE', label: 'Processing Fee' },
  { value: 'POS_RENTAL_FEE', label: 'POS Rental Fee' },
  { value: 'SERVICE_FEE', label: 'Service Fee' },
  { value: 'OTHER', label: 'Other' }
];

// User types
const userTypes = [
  { value: 'ORGANIZER', label: 'Organizer' },
  { value: 'VENDOR', label: 'Vendor' },
  { value: 'USER', label: 'User' }
];

// Transaction types
const transactionTypes = [
  { value: 'TICKET_SALE', label: 'Ticket Sale' },
  { value: 'VENDOR_SALE', label: 'Vendor Sale' },
  { value: 'POS_RENTAL_FEE', label: 'POS Rental Fee' },
  { value: 'PROCESSING_FEE', label: 'Processing Fee' },
  { value: 'PLATFORM_FEE', label: 'Platform Fee' }
];

interface FeeConfiguration {
  id: string;
  name: string;
  description?: string;
  feeType: string;
  value: number;
  isPercentage: boolean;
  isActive: boolean;
  appliesTo: string[];
  transactionType: string[];
  minAmount?: number;
  maxAmount?: number;
  effectiveFrom: string;
  effectiveUntil?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
}

export default function FeesPage() {
  const router = useRouter();
  const user = useCurrentUser();
  const role = useCurrentRole();

  // All state hooks must be declared before any early returns
  const [fees, setFees] = useState<FeeConfiguration[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingFee, setEditingFee] = useState<FeeConfiguration | null>(null);

  // Form state
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [feeType, setFeeType] = useState('');
  const [value, setValue] = useState('');
  const [isPercentage, setIsPercentage] = useState(true);
  const [isActive, setIsActive] = useState(true);
  const [appliesTo, setAppliesTo] = useState<string[]>([]);
  const [transactionType, setTransactionType] = useState<string[]>([]);
  const [minAmount, setMinAmount] = useState('');
  const [maxAmount, setMaxAmount] = useState('');
  const [effectiveFrom, setEffectiveFrom] = useState('');
  const [effectiveUntil, setEffectiveUntil] = useState('');

  // Load fees
  useEffect(() => {
    fetchFees();
  }, []);

  // Access control - only ADMIN and SUPERADMIN can access
  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin privileges to view this page.</p>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </div>
      </div>
    );
  }

  const fetchFees = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/finance/fees', {
        credentials: 'include', // Include cookies for authentication
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();

      // Handle both array and object responses
      if (Array.isArray(data)) {
        setFees(data);
      } else if (data.error) {
        // Handle error response
        throw new Error(data.error);
      } else {
        // If it's an object but not an array, check if it has any properties that might be the fees array
        console.warn('Unexpected response format from fees API:', data);
        // Set empty array as fallback
        setFees([]);
      }
    } catch (error) {
      console.error('Error fetching fees:', error);
      toast({
        title: 'Error',
        description: 'Failed to load fee configurations',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setName('');
    setDescription('');
    setFeeType('');
    setValue('');
    setIsPercentage(true);
    setIsActive(true);
    setAppliesTo([]);
    setTransactionType([]);
    setMinAmount('');
    setMaxAmount('');
    setEffectiveFrom('');
    setEffectiveUntil('');
    setEditingFee(null);
  };

  const handleOpenDialog = (fee?: FeeConfiguration) => {
    if (fee) {
      // Edit mode
      setEditingFee(fee);
      setName(fee.name);
      setDescription(fee.description || '');
      setFeeType(fee.feeType);
      setValue(fee.value.toString());
      setIsPercentage(fee.isPercentage);
      setIsActive(fee.isActive);
      setAppliesTo(fee.appliesTo);
      setTransactionType(fee.transactionType);
      setMinAmount(fee.minAmount?.toString() || '');
      setMaxAmount(fee.maxAmount?.toString() || '');
      setEffectiveFrom(fee.effectiveFrom.split('T')[0]);
      setEffectiveUntil(fee.effectiveUntil?.split('T')[0] || '');
    } else {
      // Create mode
      resetForm();
      setEffectiveFrom(new Date().toISOString().split('T')[0]);
    }

    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    resetForm();
  };

  const handleSubmit = async () => {
    try {
      // Validate form
      if (!name || !feeType || !value || appliesTo.length === 0 || transactionType.length === 0 || !effectiveFrom) {
        toast({
          title: 'Validation Error',
          description: 'Please fill in all required fields',
          variant: 'destructive'
        });
        return;
      }

      const feeData = {
        name,
        description,
        feeType,
        value: parseFloat(value),
        isPercentage,
        isActive,
        appliesTo,
        transactionType,
        minAmount: minAmount ? parseFloat(minAmount) : null,
        maxAmount: maxAmount ? parseFloat(maxAmount) : null,
        effectiveFrom,
        effectiveUntil: effectiveUntil || null
      };

      // Mock implementation until Prisma client is regenerated
      if (editingFee) {
        // Update existing fee in the mock data
        setFees(prevFees =>
          prevFees.map(fee =>
            fee.id === editingFee.id
              ? {
                  ...fee,
                  ...feeData,
                  minAmount: feeData.minAmount ?? undefined,
                  maxAmount: feeData.maxAmount ?? undefined,
                  effectiveUntil: feeData.effectiveUntil ?? undefined,
                  updatedAt: new Date().toISOString()
                }
              : fee
          )
        );
      } else {
        // Create new fee in the mock data
        const newFee = {
          id: `fee-${Date.now()}`,
          ...feeData,
          minAmount: feeData.minAmount ?? undefined,
          maxAmount: feeData.maxAmount ?? undefined,
          effectiveUntil: feeData.effectiveUntil ?? undefined,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        setFees(prevFees => [...prevFees, newFee]);
      }

      toast({
        title: 'Success',
        description: editingFee
          ? 'Fee configuration updated successfully'
          : 'Fee configuration created successfully'
      });

      handleCloseDialog();

      // Commented out until Prisma client is regenerated
      // let response;
      //
      // if (editingFee) {
      //   // Update existing fee
      //   response = await fetch(`/api/admin/fees/${editingFee.id}`, {
      //     method: 'PATCH',
      //     credentials: 'include',
      //     headers: {
      //       'Content-Type': 'application/json'
      //     },
      //     body: JSON.stringify(feeData)
      //   });
      // } else {
      //   // Create new fee
      //   response = await fetch('/api/admin/fees', {
      //     method: 'POST',
      //     credentials: 'include',
      //     headers: {
      //       'Content-Type': 'application/json'
      //     },
      //     body: JSON.stringify(feeData)
      //   });
      // }
      //
      // if (!response.ok) {
      //   throw new Error('Failed to save fee configuration');
      // }
      //
      // fetchFees();
    } catch (error) {
      console.error('Error saving fee configuration:', error);
      toast({
        title: 'Error',
        description: 'Failed to save fee configuration',
        variant: 'destructive'
      });
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this fee configuration?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/fees/${id}`, {
        method: 'DELETE',
        credentials: 'include' // Include cookies for authentication
      });

      if (!response.ok) {
        throw new Error('Failed to delete fee configuration');
      }

      toast({
        title: 'Success',
        description: 'Fee configuration deleted successfully'
      });

      fetchFees();
    } catch (error) {
      console.error('Error deleting fee configuration:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete fee configuration',
        variant: 'destructive'
      });
    }
  };

  const handleToggleActive = async (fee: FeeConfiguration) => {
    try {
      const response = await fetch(`/api/admin/fees/${fee.id}`, {
        method: 'PATCH',
        credentials: 'include', // Include cookies for authentication
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isActive: !fee.isActive
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update fee configuration');
      }

      toast({
        title: 'Success',
        description: `Fee configuration ${fee.isActive ? 'deactivated' : 'activated'} successfully`
      });

      fetchFees();
    } catch (error) {
      console.error('Error updating fee configuration:', error);
      toast({
        title: 'Error',
        description: 'Failed to update fee configuration',
        variant: 'destructive'
      });
    }
  };

  const getFeeTypeLabel = (type: string) => {
    const feeType = feeTypes.find(f => f.value === type);
    return feeType ? feeType.label : type;
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Fee Configuration</h1>
          <p className="text-gray-500">Manage platform fees and commission rates</p>
        </div>
        <Button onClick={() => handleOpenDialog()}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Add New Fee
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Fee Configurations</CardTitle>
          <CardDescription>
            Configure different fee types for organizers and vendors
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-40">
              <p>Loading fee configurations...</p>
            </div>
          ) : fees.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-40">
              <p className="text-gray-500 mb-4">No fee configurations found</p>
              <Button onClick={() => handleOpenDialog()}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add New Fee
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Fee Type</TableHead>
                  <TableHead>Value</TableHead>
                  <TableHead>Applies To</TableHead>
                  <TableHead>Effective Period</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {fees.map((fee) => (
                  <TableRow key={fee.id}>
                    <TableCell>
                      <div className="font-medium">{fee.name}</div>
                      <div className="text-sm text-gray-500">{fee.description}</div>
                    </TableCell>
                    <TableCell>{getFeeTypeLabel(fee.feeType)}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {fee.isPercentage ? (
                          <>
                            <Percent className="h-4 w-4 mr-1 text-gray-500" />
                            {fee.value}%
                          </>
                        ) : (
                          <>
                            <DollarSign className="h-4 w-4 mr-1 text-gray-500" />
                            {fee.value.toFixed(2)}
                          </>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <div className="text-xs font-medium">
                          {fee.appliesTo.join(', ')}
                        </div>
                        <div className="text-xs text-gray-500">
                          {fee.transactionType.join(', ')}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-xs">
                        From: {format(new Date(fee.effectiveFrom), 'MMM d, yyyy')}
                      </div>
                      {fee.effectiveUntil && (
                        <div className="text-xs">
                          To: {format(new Date(fee.effectiveUntil), 'MMM d, yyyy')}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <div
                          className={`h-2.5 w-2.5 rounded-full mr-2 ${
                            fee.isActive ? 'bg-green-500' : 'bg-gray-400'
                          }`}
                        />
                        {fee.isActive ? 'Active' : 'Inactive'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleOpenDialog(fee)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleActive(fee)}
                        >
                          {fee.isActive ? (
                            <X className="h-4 w-4 text-red-500" />
                          ) : (
                            <Check className="h-4 w-4 text-green-500" />
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(fee.id)}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <Dialog open={openDialog} onOpenChange={setOpenDialog}>
        <DialogContent className="sm:max-w-[900px] border-0 shadow-lg dark:bg-gray-800">
          <DialogHeader className="bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-800 dark:to-indigo-900 -mx-6 -mt-6 px-6 py-4 rounded-t-lg">
            <DialogTitle className="text-white text-xl font-bold">
              {editingFee ? 'Edit Fee Configuration' : 'Create Fee Configuration'}
            </DialogTitle>
            <DialogDescription className="text-blue-100 mt-1">
              {editingFee
                ? 'Update the details of this fee configuration'
                : 'Configure a new fee for the platform'}
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-3 gap-6 py-5">
            {/* Left Column - Basic Information */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-semibold text-gray-700 dark:text-gray-300">Name</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="e.g. Organizer Commission"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="feeType" className="text-sm font-semibold text-gray-700 dark:text-gray-300">Fee Type</Label>
                <Select value={feeType} onValueChange={setFeeType}>
                  <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                    <SelectValue placeholder="Select fee type" />
                  </SelectTrigger>
                  <SelectContent className="dark:bg-gray-800 dark:border-gray-700">
                    {feeTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value} className="dark:text-white dark:focus:bg-gray-700">
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-semibold text-gray-700 dark:text-gray-300">Description</Label>
                <Input
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Brief description of this fee"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="value" className="text-sm font-semibold text-gray-700 dark:text-gray-300">Value</Label>
                <Input
                  id="value"
                  type="number"
                  value={value}
                  onChange={(e) => setValue(e.target.value)}
                  placeholder="e.g. 6.0"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">Value Type</Label>
                <div className="flex items-center space-x-4 pt-2 bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="percentage"
                      checked={isPercentage}
                      onChange={() => setIsPercentage(true)}
                      className="text-blue-600 focus:ring-blue-500 dark:text-blue-500 dark:focus:ring-blue-400"
                    />
                    <Label htmlFor="percentage" className="cursor-pointer text-gray-700 dark:text-gray-300">
                      Percentage (%)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="fixed"
                      checked={!isPercentage}
                      onChange={() => setIsPercentage(false)}
                      className="text-blue-600 focus:ring-blue-500 dark:text-blue-500 dark:focus:ring-blue-400"
                    />
                    <Label htmlFor="fixed" className="cursor-pointer text-gray-700 dark:text-gray-300">
                      Fixed Amount
                    </Label>
                  </div>
                </div>
              </div>
            </div>

            {/* Middle Column - Amount Limits and Dates */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="minAmount" className="text-sm font-semibold text-gray-700 dark:text-gray-300">Minimum Amount (Optional)</Label>
                <Input
                  id="minAmount"
                  type="number"
                  value={minAmount}
                  onChange={(e) => setMinAmount(e.target.value)}
                  placeholder="e.g. 10.00"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxAmount" className="text-sm font-semibold text-gray-700 dark:text-gray-300">Maximum Amount (Optional)</Label>
                <Input
                  id="maxAmount"
                  type="number"
                  value={maxAmount}
                  onChange={(e) => setMaxAmount(e.target.value)}
                  placeholder="e.g. 1000.00"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="effectiveFrom" className="text-sm font-semibold text-gray-700 dark:text-gray-300">Effective From</Label>
                <Input
                  id="effectiveFrom"
                  type="date"
                  value={effectiveFrom}
                  onChange={(e) => setEffectiveFrom(e.target.value)}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="effectiveUntil" className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                  Effective Until (Optional)
                </Label>
                <Input
                  id="effectiveUntil"
                  type="date"
                  value={effectiveUntil}
                  onChange={(e) => setEffectiveUntil(e.target.value)}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="flex items-center space-x-2 bg-gray-50 dark:bg-gray-700 p-3 rounded-md mt-auto">
                <Switch
                  id="isActive"
                  checked={isActive}
                  onCheckedChange={setIsActive}
                  className="data-[state=checked]:bg-blue-600 dark:data-[state=checked]:bg-blue-500"
                />
                <Label htmlFor="isActive" className="font-medium text-gray-700 dark:text-gray-300">Active</Label>
              </div>
            </div>

            {/* Right Column - Applies To and Transaction Types */}
            <div className="space-y-4">
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">Applies To</Label>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                  {userTypes.map((type) => (
                    <div key={type.value} className="flex items-center space-x-2 mb-2">
                      <Checkbox
                        id={`appliesTo-${type.value}`}
                        checked={appliesTo.includes(type.value)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setAppliesTo([...appliesTo, type.value]);
                          } else {
                            setAppliesTo(appliesTo.filter((t) => t !== type.value));
                          }
                        }}
                        className="text-blue-600 focus:ring-blue-500 dark:text-blue-500 dark:focus:ring-blue-400"
                      />
                      <Label
                        htmlFor={`appliesTo-${type.value}`}
                        className="cursor-pointer text-gray-700 dark:text-gray-300"
                      >
                        {type.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700 dark:text-gray-300">Transaction Types</Label>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md h-[calc(100%-3rem)]">
                  <div className="grid grid-cols-1 gap-2">
                    {transactionTypes.map((type) => (
                      <div key={type.value} className="flex items-center space-x-2">
                        <Checkbox
                          id={`transactionType-${type.value}`}
                          checked={transactionType.includes(type.value)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setTransactionType([...transactionType, type.value]);
                            } else {
                              setTransactionType(
                                transactionType.filter((t) => t !== type.value)
                              );
                            }
                          }}
                          className="text-blue-600 focus:ring-blue-500 dark:text-blue-500 dark:focus:ring-blue-400"
                        />
                        <Label
                          htmlFor={`transactionType-${type.value}`}
                          className="cursor-pointer text-gray-700 dark:text-gray-300"
                        >
                          {type.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="bg-gray-50 dark:bg-gray-800 -mx-6 -mb-6 px-6 py-4 rounded-b-lg border-t border-gray-200 dark:border-gray-700">
            <Button
              variant="outline"
              onClick={handleCloseDialog}
              className="border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              className="bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-700 dark:hover:bg-blue-800"
            >
              {editingFee ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
