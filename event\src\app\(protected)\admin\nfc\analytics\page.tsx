'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { 
  Download, 
  RefreshCw, 
  AlertCircle, 
  BarChart3, 
  <PERSON><PERSON><PERSON> as PieChartIcon, 
  <PERSON><PERSON>hart as LineChartIcon,
  DollarSign,
  CreditCard,
  Users,
  Store
} from 'lucide-react';
import Link from 'next/link';

// Mock data for development
const generateMockData = (timeRange: string) => {
  // Transaction data by day
  const transactionsByDay = Array.from({ length: timeRange === 'week' ? 7 : 30 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - i);
    return {
      date: date.toISOString().split('T')[0],
      completed: Math.floor(Math.random() * 50) + 10,
      pending: Math.floor(Math.random() * 20),
      failed: Math.floor(Math.random() * 5),
    };
  }).reverse();

  // Transaction data by status
  const transactionsByStatus = [
    { status: 'COMPLETED', count: 450, color: '#10b981' },
    { status: 'PENDING', count: 120, color: '#f59e0b' },
    { status: 'FAILED', count: 30, color: '#ef4444' },
    { status: 'CANCELLED', count: 15, color: '#6b7280' },
  ];

  // Transaction data by vendor
  const transactionsByVendor = Array.from({ length: 10 }, (_, i) => ({
    name: `Vendor ${i + 1}`,
    transactions: Math.floor(Math.random() * 100) + 20,
    revenue: Math.floor(Math.random() * 5000) + 500,
  })).sort((a, b) => b.revenue - a.revenue);

  // Transaction data by event
  const transactionsByEvent = Array.from({ length: 8 }, (_, i) => ({
    name: `Event ${i + 1}`,
    transactions: Math.floor(Math.random() * 200) + 50,
    revenue: Math.floor(Math.random() * 10000) + 1000,
  })).sort((a, b) => b.revenue - a.revenue);

  return {
    transactionsByDay,
    transactionsByStatus,
    transactionsByVendor,
    transactionsByEvent,
    totalRevenue: 45750.25,
    totalTransactions: 615,
    activeCards: 850,
    activeVendors: 32
  };
};

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'ZMW',
    minimumFractionDigits: 2
  }).format(amount);
};

export default function AdminNFCAnalyticsPage() {
  const { toast } = useToast();
  const [timeRange, setTimeRange] = useState('week');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [analyticsData, setAnalyticsData] = useState<any>(null);

  // Fetch analytics data
  const fetchAnalytics = async (refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }
    setError(null);

    try {
      // In a real implementation, this would call the API
      // const response = await fetch(`/api/admin/nfc/analytics?timeRange=${timeRange}`);
      // if (!response.ok) throw new Error(`Error: ${response.status}`);
      // const data = await response.json();
      
      // For development, use mock data
      const mockData = generateMockData(timeRange);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAnalyticsData(mockData);
    } catch (err) {
      console.error('Error fetching NFC analytics:', err);
      setError('Failed to load analytics data. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Refresh data
  const refreshData = () => {
    fetchAnalytics(true);
    toast({
      title: 'Refreshing Analytics',
      description: 'Fetching the latest NFC transaction data...',
    });
  };

  // Export data
  const exportData = (format: string) => {
    toast({
      title: 'Export Started',
      description: `Exporting analytics as ${format.toUpperCase()}...`,
    });
    
    // Simulate export delay
    setTimeout(() => {
      toast({
        title: 'Export Complete',
        description: `Analytics have been exported as ${format.toUpperCase()}.`,
      });
    }, 1500);
  };

  // Initial data fetch
  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">NFC Analytics</h1>
          <p className="text-gray-600 mt-1">
            Comprehensive analytics for the NFC payment system
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Last 24 Hours</SelectItem>
              <SelectItem value="week">Last 7 Days</SelectItem>
              <SelectItem value="month">Last 30 Days</SelectItem>
              <SelectItem value="year">Last 12 Months</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={refreshData} disabled={isRefreshing}>
            {isRefreshing ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
          
          <Select onValueChange={exportData}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="Export" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="csv">Export CSV</SelectItem>
              <SelectItem value="excel">Export Excel</SelectItem>
              <SelectItem value="pdf">Export PDF</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Revenue */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <DollarSign className="h-5 w-5 mr-2 text-green-500" />
              Total Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ) : (
              <>
                <div className="text-3xl font-bold">{formatCurrency(analyticsData?.totalRevenue || 0)}</div>
                <p className="text-sm text-gray-500">
                  From {analyticsData?.totalTransactions.toLocaleString() || 0} transactions
                </p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Total Transactions */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <BarChart3 className="h-5 w-5 mr-2 text-blue-500" />
              Total Transactions
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ) : (
              <>
                <div className="text-3xl font-bold">{analyticsData?.totalTransactions.toLocaleString() || 0}</div>
                <p className="text-sm text-gray-500">
                  Average {formatCurrency((analyticsData?.totalRevenue || 0) / (analyticsData?.totalTransactions || 1))} per transaction
                </p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Active Cards */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <CreditCard className="h-5 w-5 mr-2 text-purple-500" />
              Active Cards
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ) : (
              <>
                <div className="text-3xl font-bold">{analyticsData?.activeCards.toLocaleString() || 0}</div>
                <p className="text-sm text-gray-500">
                  In circulation
                </p>
              </>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="ghost" size="sm" className="w-full" asChild>
              <Link href="/admin/nfc/cards">
                Manage Cards
              </Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Active Vendors */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Store className="h-5 w-5 mr-2 text-amber-500" />
              Active Vendors
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ) : (
              <>
                <div className="text-3xl font-bold">{analyticsData?.activeVendors.toLocaleString() || 0}</div>
                <p className="text-sm text-gray-500">
                  Using NFC terminals
                </p>
              </>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="ghost" size="sm" className="w-full" asChild>
              <Link href="/admin/nfc/vendors">
                Manage Vendors
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Charts */}
      <Tabs defaultValue="transactions" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="transactions" className="flex items-center">
            <LineChartIcon className="mr-2 h-4 w-4" />
            Transactions Over Time
          </TabsTrigger>
          <TabsTrigger value="status" className="flex items-center">
            <PieChartIcon className="mr-2 h-4 w-4" />
            Transaction Status
          </TabsTrigger>
          <TabsTrigger value="vendors" className="flex items-center">
            <BarChart3 className="mr-2 h-4 w-4" />
            Top Vendors & Events
          </TabsTrigger>
        </TabsList>

        {/* Transactions Over Time Tab */}
        <TabsContent value="transactions">
          <Card>
            <CardHeader>
              <CardTitle>Transactions Over Time</CardTitle>
              <CardDescription>
                Daily transaction volume breakdown by status
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              {isLoading ? (
                <div className="w-full h-full flex items-center justify-center">
                  <Skeleton className="h-full w-full" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={analyticsData?.transactionsByDay}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="completed" stroke="#10b981" name="Completed" />
                    <Line type="monotone" dataKey="pending" stroke="#f59e0b" name="Pending" />
                    <Line type="monotone" dataKey="failed" stroke="#ef4444" name="Failed" />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Transaction Status Tab */}
        <TabsContent value="status">
          <Card>
            <CardHeader>
              <CardTitle>Transaction Status Distribution</CardTitle>
              <CardDescription>
                Breakdown of transactions by their current status
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              {isLoading ? (
                <div className="w-full h-full flex items-center justify-center">
                  <Skeleton className="h-full w-full" />
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={analyticsData?.transactionsByStatus}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                      outerRadius={150}
                      fill="#8884d8"
                      dataKey="count"
                      nameKey="status"
                    >
                      {analyticsData?.transactionsByStatus.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value} transactions`, 'Count']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Top Vendors & Events Tab */}
        <TabsContent value="vendors">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Top Vendors */}
            <Card>
              <CardHeader>
                <CardTitle>Top Vendors by Revenue</CardTitle>
                <CardDescription>
                  Vendors with the highest transaction volume
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[400px]">
                {isLoading ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <Skeleton className="h-full w-full" />
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={analyticsData?.transactionsByVendor.slice(0, 5)}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis type="category" dataKey="name" width={100} />
                      <Tooltip formatter={(value) => [formatCurrency(value as number), 'Revenue']} />
                      <Legend />
                      <Bar dataKey="revenue" fill="#8884d8" name="Revenue" />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/admin/nfc/vendors">
                    View All Vendors
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Top Events */}
            <Card>
              <CardHeader>
                <CardTitle>Top Events by Revenue</CardTitle>
                <CardDescription>
                  Events with the highest transaction volume
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[400px]">
                {isLoading ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <Skeleton className="h-full w-full" />
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={analyticsData?.transactionsByEvent.slice(0, 5)}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis type="category" dataKey="name" width={100} />
                      <Tooltip formatter={(value) => [formatCurrency(value as number), 'Revenue']} />
                      <Legend />
                      <Bar dataKey="revenue" fill="#82ca9d" name="Revenue" />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/admin/dashboard/events">
                    View All Events
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
