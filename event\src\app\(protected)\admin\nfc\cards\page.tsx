'use client';

import { useState, useEffect } from 'react';
import { 
  RefreshCw, 
  Search, 
  Filter, 
  Download, 
  AlertCircle,
  Plus,
  CreditCard,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Edit,
  Trash,
  Ban,
  CheckCircle,
  User
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from '@/components/ui/label';

export default function AdminNFCCardsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  
  // Filters
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [eventFilter, setEventFilter] = useState('all');
  
  // Sorting
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('desc');
  
  // Data
  const [cards, setCards] = useState<any[]>([]);
  const [totalCards, setTotalCards] = useState(0);
  const [events, setEvents] = useState<any[]>([]);
  
  // New card dialog
  const [isNewCardDialogOpen, setIsNewCardDialogOpen] = useState(false);
  const [newCardUid, setNewCardUid] = useState('');
  const [newCardEvent, setNewCardEvent] = useState('');
  const [newCardAssignedTo, setNewCardAssignedTo] = useState('');
  const [newCardBalance, setNewCardBalance] = useState('0');
  const [isCreatingCard, setIsCreatingCard] = useState(false);
  
  // Fetch cards
  const fetchCards = async (refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }
    setError(null);
    
    try {
      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        pageSize: pageSize.toString(),
        status: statusFilter,
        event: eventFilter,
        search: searchQuery,
        sortField: sortField,
        sortDirection: sortDirection
      });
      
      // Call the API to get cards
      const response = await fetch(`/api/admin/nfc/cards?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      const data = await response.json();
      
      setCards(data.cards);
      setTotalCards(data.totalCount);
      setTotalPages(data.totalPages);
      setEvents(data.events || []);
    } catch (err) {
      console.error('Error fetching NFC cards:', err);
      setError('Failed to load NFC cards. Please try again.');
      
      // Fallback to mock data for development
      if (process.env.NODE_ENV === 'development') {
        // Mock data
        const mockCards = Array.from({ length: pageSize }, (_, i) => ({
          id: `card-${i + 1 + (currentPage - 1) * pageSize}`,
          uid: `NFC-${100000 + i + (currentPage - 1) * pageSize}`,
          isActive: Math.random() > 0.2,
          status: ['active', 'active', 'active', 'inactive', 'lost'][Math.floor(Math.random() * 5)],
          balance: Math.random() * 200,
          assignedTo: Math.random() > 0.3 ? `User ${i + 1}` : null,
          lastUsed: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString() : null,
          createdAt: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000).toISOString(),
          event: {
            id: `event-${1 + Math.floor(Math.random() * 5)}`,
            name: ['Summer Festival 2023', 'Tech Conference', 'Music Concert', 'Food Fair', 'Art Exhibition'][Math.floor(Math.random() * 5)]
          },
          user: Math.random() > 0.3 ? {
            id: `user-${i + 1}`,
            name: `User ${i + 1}`,
            email: `user${i + 1}@example.com`,
            image: null
          } : null,
          transactionCount: Math.floor(Math.random() * 20)
        }));
        
        setCards(mockCards);
        setTotalCards(150); // Mock total
        setTotalPages(Math.ceil(150 / pageSize));
        
        // Mock events for filters
        setEvents([
          { id: 'event-1', name: 'Summer Festival 2023' },
          { id: 'event-2', name: 'Tech Conference' },
          { id: 'event-3', name: 'Music Concert' },
          { id: 'event-4', name: 'Food Fair' },
          { id: 'event-5', name: 'Art Exhibition' }
        ]);
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };
  
  // Fetch data when filters or pagination changes
  useEffect(() => {
    fetchCards();
  }, [currentPage, pageSize, statusFilter, eventFilter, sortField, sortDirection]);
  
  // Handle search
  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page
    fetchCards();
  };
  
  // Handle sort
  const handleSort = (field: string) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // New field, default to desc
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };
  
  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };
  
  // Export cards
  const exportCards = () => {
    toast({
      title: 'Export Started',
      description: 'Your card export is being prepared and will download shortly.',
    });
    
    // In a real implementation, this would trigger an API call to generate a CSV/Excel file
    setTimeout(() => {
      toast({
        title: 'Export Complete',
        description: 'Your card data has been exported successfully.',
      });
    }, 2000);
  };
  
  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Inactive</Badge>;
      case 'lost':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Lost</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };
  
  // Create new card
  const createNewCard = async () => {
    setIsCreatingCard(true);
    
    try {
      if (!newCardUid || !newCardEvent) {
        toast({
          title: 'Missing Information',
          description: 'Card UID and event are required.',
          variant: 'destructive',
        });
        return;
      }
      
      const cardData = {
        uid: newCardUid,
        eventId: newCardEvent,
        assignedTo: newCardAssignedTo || undefined,
        initialBalance: parseFloat(newCardBalance) || 0,
        isActive: true
      };
      
      // Call the API to create a new card
      const response = await fetch('/api/admin/nfc/cards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(cardData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create card');
      }
      
      const data = await response.json();
      
      toast({
        title: 'Card Created',
        description: 'The NFC card has been created successfully.',
      });
      
      // Reset form and close dialog
      setNewCardUid('');
      setNewCardEvent('');
      setNewCardAssignedTo('');
      setNewCardBalance('0');
      setIsNewCardDialogOpen(false);
      
      // Refresh the card list
      fetchCards(true);
    } catch (err) {
      console.error('Error creating NFC card:', err);
      
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to create card. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsCreatingCard(false);
    }
  };
  
  // Update card status
  const updateCardStatus = async (cardId: string, isActive: boolean) => {
    try {
      // Call the API to update the card
      const response = await fetch(`/api/admin/nfc/cards/${cardId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isActive,
          status: isActive ? 'active' : 'inactive'
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update card');
      }
      
      toast({
        title: isActive ? 'Card Activated' : 'Card Deactivated',
        description: `The NFC card has been ${isActive ? 'activated' : 'deactivated'} successfully.`,
      });
      
      // Refresh the card list
      fetchCards(true);
    } catch (err) {
      console.error('Error updating NFC card:', err);
      
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to update card. Please try again.',
        variant: 'destructive',
      });
    }
  };
  
  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">NFC Cards</h1>
          <p className="text-gray-600 mt-1">
            Manage all NFC cards across the system
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => fetchCards(true)} disabled={isRefreshing}>
            {isRefreshing ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
          <Button variant="outline" onClick={exportCards}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Dialog open={isNewCardDialogOpen} onOpenChange={setIsNewCardDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Card
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Register New NFC Card</DialogTitle>
                <DialogDescription>
                  Enter the details for the new NFC card.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="cardUid">Card UID (Required)</Label>
                  <Input
                    id="cardUid"
                    placeholder="Enter card UID"
                    value={newCardUid}
                    onChange={(e) => setNewCardUid(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cardEvent">Event (Required)</Label>
                  <Select value={newCardEvent} onValueChange={setNewCardEvent}>
                    <SelectTrigger id="cardEvent">
                      <SelectValue placeholder="Select event" />
                    </SelectTrigger>
                    <SelectContent>
                      {events.map(event => (
                        <SelectItem key={event.id} value={event.id}>{event.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cardAssignedTo">Assigned To (Optional)</Label>
                  <Input
                    id="cardAssignedTo"
                    placeholder="Enter user name or ID"
                    value={newCardAssignedTo}
                    onChange={(e) => setNewCardAssignedTo(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cardBalance">Initial Balance</Label>
                  <Input
                    id="cardBalance"
                    type="number"
                    placeholder="0.00"
                    value={newCardBalance}
                    onChange={(e) => setNewCardBalance(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsNewCardDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={createNewCard} disabled={isCreatingCard}>
                  {isCreatingCard ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Card'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="flex">
                <Input
                  placeholder="Search by UID, user, or name..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="rounded-r-none"
                />
                <Button 
                  variant="secondary" 
                  className="rounded-l-none" 
                  onClick={handleSearch}
                >
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="lost">Lost</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Event</label>
              <Select value={eventFilter} onValueChange={setEventFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by event" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Events</SelectItem>
                  {events.map(event => (
                    <SelectItem key={event.id} value={event.id}>{event.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="mt-4 flex justify-between items-center">
            <div>
              {(statusFilter !== 'all' || eventFilter !== 'all' || searchQuery) && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => {
                    setStatusFilter('all');
                    setEventFilter('all');
                    setSearchQuery('');
                    setCurrentPage(1);
                    fetchCards();
                  }}
                >
                  Clear Filters
                </Button>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                Showing {cards.length} of {totalCards} cards
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Cards Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[100px]">
                    <Button 
                      variant="ghost" 
                      className="p-0 font-medium flex items-center" 
                      onClick={() => handleSort('uid')}
                    >
                      Card UID
                      {sortField === 'uid' && (
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button 
                      variant="ghost" 
                      className="p-0 font-medium flex items-center" 
                      onClick={() => handleSort('event')}
                    >
                      Event
                      {sortField === 'event' && (
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button 
                      variant="ghost" 
                      className="p-0 font-medium flex items-center" 
                      onClick={() => handleSort('status')}
                    >
                      Status
                      {sortField === 'status' && (
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button 
                      variant="ghost" 
                      className="p-0 font-medium flex items-center" 
                      onClick={() => handleSort('balance')}
                    >
                      Balance
                      {sortField === 'balance' && (
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button 
                      variant="ghost" 
                      className="p-0 font-medium flex items-center" 
                      onClick={() => handleSort('assignedTo')}
                    >
                      Assigned To
                      {sortField === 'assignedTo' && (
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button 
                      variant="ghost" 
                      className="p-0 font-medium flex items-center" 
                      onClick={() => handleSort('lastUsed')}
                    >
                      Last Used
                      {sortField === 'lastUsed' && (
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  // Loading skeletons
                  Array.from({ length: pageSize }).map((_, i) => (
                    <TableRow key={i}>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                    </TableRow>
                  ))
                ) : cards.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      No cards found. Try adjusting your filters or create a new card.
                    </TableCell>
                  </TableRow>
                ) : (
                  cards.map((card) => (
                    <TableRow key={card.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <CreditCard className="mr-2 h-4 w-4 text-gray-400" />
                          {card.uid}
                        </div>
                      </TableCell>
                      <TableCell>{card.event?.name || 'N/A'}</TableCell>
                      <TableCell>{getStatusBadge(card.status)}</TableCell>
                      <TableCell>{formatCurrency(card.balance)}</TableCell>
                      <TableCell>
                        {card.assignedTo ? (
                          <div className="flex items-center">
                            <User className="mr-2 h-4 w-4 text-gray-400" />
                            {card.assignedTo}
                          </div>
                        ) : (
                          <span className="text-gray-500">Unassigned</span>
                        )}
                      </TableCell>
                      <TableCell>{formatDate(card.lastUsed)}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Card Actions</DropdownMenuLabel>
                            <DropdownMenuItem>View Details</DropdownMenuItem>
                            <DropdownMenuItem>Edit Card</DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {card.isActive ? (
                              <DropdownMenuItem onClick={() => updateCardStatus(card.id, false)}>
                                <Ban className="mr-2 h-4 w-4" />
                                Deactivate Card
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem onClick={() => updateCardStatus(card.id, true)}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Activate Card
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem>
                              <Trash className="mr-2 h-4 w-4" />
                              Delete Card
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Rows per page:</span>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => {
                setPageSize(Number(value));
                setCurrentPage(1);
              }}
            >
              <SelectTrigger className="w-16">
                <SelectValue placeholder="10" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1 || isLoading}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <span className="text-sm">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages || isLoading}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
