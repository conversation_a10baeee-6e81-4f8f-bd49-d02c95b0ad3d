'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import {
  RefreshCw,
  AlertCircle,
  Search,
  Download,
  Store,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Settings,
  CreditCard,
  BarChart3,
  History
} from 'lucide-react';
import Link from 'next/link';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Interface for vendor data
interface Vendor {
  id: string;
  userId: string;
  businessName: string;
  email: string;
  phone: string;
  status: string;
  verificationStatus: string;
  terminalId: string | null;
  terminalName: string | null;
  deviceId: string | null;
  lastActive: string | null;
  transactionCount: number;
  totalRevenue: number;
  offlineMode: boolean;
  autoSync: boolean;
  notificationsEnabled: boolean;
  autoPrint: boolean;
}

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'ZMW',
    minimumFractionDigits: 2
  }).format(amount);
};

// Format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

export default function AdminNFCVendorsPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [filteredVendors, setFilteredVendors] = useState<Vendor[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [verificationFilter, setVerificationFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);
  const [isAddVendorDialogOpen, setIsAddVendorDialogOpen] = useState(false);

  const pageSize = 10;

  // Fetch vendors
  const fetchVendors = async (refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        pageSize: pageSize.toString(),
        search: searchQuery,
        status: statusFilter,
        verification: verificationFilter
      });

      // Call the API
      const response = await fetch(`/api/admin/nfc/vendors?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();

      // Update state with real data
      setVendors(data.vendors);
      setFilteredVendors(data.vendors);
      setTotalPages(data.totalPages);
      setTotalCount(data.totalCount);

      // If we're not using server-side filtering, apply client-side filters
      // This is only needed if you want to implement additional client-side filtering
      // applyFilters(data.vendors, searchQuery, statusFilter, verificationFilter);
    } catch (err) {
      console.error('Error fetching NFC vendors:', err);
      setError('Failed to load vendor data. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    // Reset to first page when search changes
    setCurrentPage(1);
    // Debounce the API call to avoid too many requests
    const timer = setTimeout(() => {
      fetchVendors();
    }, 500);
    return () => clearTimeout(timer);
  };

  // Handle status filter change
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1); // Reset to first page when filter changes
    fetchVendors();
  };

  // Handle verification filter change
  const handleVerificationFilterChange = (value: string) => {
    setVerificationFilter(value);
    setCurrentPage(1); // Reset to first page when filter changes
    fetchVendors();
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    fetchVendors();
  };

  // Refresh data
  const refreshData = () => {
    fetchVendors(true);
    toast({
      title: 'Refreshing Vendors',
      description: 'Fetching the latest vendor data...',
    });
  };

  // Export data
  const exportVendors = async () => {
    toast({
      title: 'Export Started',
      description: 'Exporting vendor data as CSV...',
    });

    try {
      // Get all vendors for export (no pagination)
      const response = await fetch('/api/admin/nfc/vendors?pageSize=1000');

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      const vendors = data.vendors;

      // Convert to CSV
      const headers = ['Business Name', 'Email', 'Phone', 'Status', 'Verification Status', 'Terminal ID', 'Device ID', 'Last Active', 'Transactions', 'Revenue'];
      const csvRows = [headers.join(',')];

      vendors.forEach((vendor: Vendor) => {
        const row = [
          `"${vendor.businessName}"`,
          `"${vendor.email}"`,
          `"${vendor.phone}"`,
          `"${vendor.status}"`,
          `"${vendor.verificationStatus}"`,
          `"${vendor.terminalId || ''}"`,
          `"${vendor.deviceId || ''}"`,
          `"${vendor.lastActive ? new Date(vendor.lastActive).toLocaleString() : ''}"`,
          vendor.transactionCount,
          vendor.totalRevenue.toFixed(2)
        ];
        csvRows.push(row.join(','));
      });

      const csvContent = csvRows.join('\n');

      // Create a blob and download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `nfc-vendors-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: 'Export Complete',
        description: 'Vendor data has been exported as CSV.',
      });
    } catch (err) {
      console.error('Error exporting vendors:', err);
      toast({
        title: 'Export Failed',
        description: 'Failed to export vendor data. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle edit vendor
  const handleEditVendor = (vendor: any) => {
    setSelectedVendor(vendor);
    setIsEditDialogOpen(true);
  };

  // Handle delete vendor
  const handleDeleteVendor = (vendor: any) => {
    setSelectedVendor(vendor);
    setIsDeleteDialogOpen(true);
  };

  // Handle terminal settings
  const handleTerminalSettings = (vendor: any) => {
    setSelectedVendor(vendor);
    setIsSettingsDialogOpen(true);
  };

  // Confirm delete vendor
  const confirmDeleteVendor = async () => {
    if (!selectedVendor) return;

    try {
      const response = await fetch(`/api/admin/nfc/vendors/${selectedVendor.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      toast({
        title: 'Vendor Deleted',
        description: `${selectedVendor.businessName} has been removed from the NFC system.`,
      });

      // Refresh the vendor list
      fetchVendors();
    } catch (err) {
      console.error('Error deleting vendor:', err);
      toast({
        title: 'Error',
        description: 'Failed to delete vendor. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleteDialogOpen(false);
    }
  };

  // Save terminal settings
  const saveTerminalSettings = async () => {
    if (!selectedVendor) return;

    // Get form values
    const terminalName = (document.getElementById('terminalName') as HTMLInputElement)?.value;
    const offlineMode = (document.getElementById('offlineMode') as HTMLInputElement)?.checked;
    const autoSync = (document.getElementById('autoSync') as HTMLInputElement)?.checked;
    const notificationsEnabled = (document.getElementById('notifications') as HTMLInputElement)?.checked;
    const autoPrint = (document.getElementById('autoPrint') as HTMLInputElement)?.checked;

    try {
      const response = await fetch(`/api/admin/nfc/vendors/${selectedVendor.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          terminalName,
          offlineMode,
          autoSync,
          notificationsEnabled,
          autoPrint,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      toast({
        title: 'Settings Saved',
        description: `Terminal settings for ${selectedVendor.businessName} have been updated.`,
      });

      // Refresh the vendor list
      fetchVendors();
    } catch (err) {
      console.error('Error updating terminal settings:', err);
      toast({
        title: 'Error',
        description: 'Failed to update terminal settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSettingsDialogOpen(false);
    }
  };

  // Get current page vendors - with server-side pagination, we just return the filtered vendors
  const getCurrentPageVendors = () => {
    return filteredVendors;
  };

  // Initial data fetch
  useEffect(() => {
    fetchVendors();
  }, []);

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">NFC Vendor Management</h1>
          <p className="text-gray-600 mt-1">
            Manage vendors and their NFC terminal settings
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={refreshData} disabled={isRefreshing}>
            {isRefreshing ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
          <Button variant="outline" onClick={exportVendors}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button onClick={() => setIsAddVendorDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Vendor
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search vendors..."
            className="pl-8"
            value={searchQuery}
            onChange={handleSearch}
          />
        </div>
        <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
        <Select value={verificationFilter} onValueChange={handleVerificationFilterChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by verification" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Verifications</SelectItem>
            <SelectItem value="APPROVED">Approved</SelectItem>
            <SelectItem value="PENDING">Pending</SelectItem>
            <SelectItem value="REJECTED">Rejected</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>NFC Vendors</CardTitle>
          <CardDescription>
            Manage vendors and their NFC terminal settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex justify-between items-center">
                  <Skeleton className="h-12 w-full" />
                </div>
              ))}
            </div>
          ) : filteredVendors.length === 0 ? (
            <div className="text-center py-8">
              <Store className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-lg font-medium">No vendors found</h3>
              <p className="mt-1 text-gray-500">
                Try adjusting your search or filters to find what you're looking for.
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Vendor</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Terminal ID</TableHead>
                    <TableHead>Last Active</TableHead>
                    <TableHead className="text-right">Transactions</TableHead>
                    <TableHead className="text-right">Revenue</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {getCurrentPageVendors().map((vendor) => (
                    <TableRow key={vendor.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{vendor.businessName}</div>
                          <div className="text-sm text-gray-500">{vendor.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={vendor.status === 'active' ? 'default' : 'secondary'}>
                          {vendor.status === 'active' ? 'Active' : 'Inactive'}
                        </Badge>
                        {vendor.offlineMode && (
                          <Badge variant="outline" className="ml-2 bg-amber-50 text-amber-700 border-amber-200">
                            Offline Mode
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="font-mono text-xs">{vendor.terminalId}</div>
                        <div className="text-xs text-gray-500">Device: {vendor.deviceId}</div>
                      </TableCell>
                      <TableCell>
                        {vendor.lastActive ? formatDate(vendor.lastActive) : 'Never'}
                      </TableCell>
                      <TableCell className="text-right">
                        {vendor.transactionCount.toLocaleString()}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(vendor.totalRevenue)}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleEditVendor(vendor)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Vendor
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleTerminalSettings(vendor)}>
                              <Settings className="mr-2 h-4 w-4" />
                              Terminal Settings
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/nfc/transactions?vendorId=${vendor.id}`} className="flex items-center">
                                <History className="mr-2 h-4 w-4" />
                                View Transactions
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/nfc/analytics?vendorId=${vendor.id}`} className="flex items-center">
                                <BarChart3 className="mr-2 h-4 w-4" />
                                View Analytics
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteVendor(vendor)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Remove Vendor
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-gray-500">
            Showing {filteredVendors.length > 0 ? (currentPage - 1) * pageSize + 1 : 0} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} vendors
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* Edit Vendor Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Vendor</DialogTitle>
            <DialogDescription>
              Update vendor information and settings.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {selectedVendor && (
              <>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="businessName" className="text-right">
                    Business Name
                  </label>
                  <Input
                    id="businessName"
                    defaultValue={selectedVendor.businessName}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="email" className="text-right">
                    Email
                  </label>
                  <Input
                    id="email"
                    defaultValue={selectedVendor.email}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="phone" className="text-right">
                    Phone
                  </label>
                  <Input
                    id="phone"
                    defaultValue={selectedVendor.phone}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="status" className="text-right">
                    Status
                  </label>
                  <Select defaultValue={selectedVendor.status}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={async () => {
              if (!selectedVendor) return;

              // Get form values
              const businessName = (document.getElementById('businessName') as HTMLInputElement)?.value;
              const email = (document.getElementById('email') as HTMLInputElement)?.value;
              const phone = (document.getElementById('phone') as HTMLInputElement)?.value;
              const status = document.querySelector('[data-value]')?.getAttribute('data-value') || 'active';

              try {
                const response = await fetch(`/api/admin/nfc/vendors/${selectedVendor.id}`, {
                  method: 'PATCH',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    businessName,
                    email,
                    phone,
                    status: status === 'active',
                  }),
                });

                if (!response.ok) {
                  throw new Error(`Error: ${response.status}`);
                }

                toast({
                  title: 'Vendor Updated',
                  description: 'The vendor information has been updated successfully.',
                });

                // Refresh the vendor list
                fetchVendors();
              } catch (err) {
                console.error('Error updating vendor:', err);
                toast({
                  title: 'Error',
                  description: 'Failed to update vendor information. Please try again.',
                  variant: 'destructive',
                });
              } finally {
                setIsEditDialogOpen(false);
              }
            }}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Vendor Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Remove Vendor</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove this vendor from the NFC system?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedVendor && (
              <div className="border rounded-md p-4">
                <h4 className="font-medium">{selectedVendor.businessName}</h4>
                <p className="text-sm text-gray-500">{selectedVendor.email}</p>
                <p className="text-sm text-gray-500">{selectedVendor.phone}</p>
                <div className="mt-2">
                  <Badge variant={selectedVendor.status === 'active' ? 'default' : 'secondary'}>
                    {selectedVendor.status === 'active' ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </div>
            )}
            <p className="mt-4 text-sm text-red-500">
              This action cannot be undone. The vendor will lose access to the NFC payment system.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteVendor}>
              Remove Vendor
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Terminal Settings Dialog */}
      <Dialog open={isSettingsDialogOpen} onOpenChange={setIsSettingsDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Terminal Settings</DialogTitle>
            <DialogDescription>
              Configure NFC terminal settings for this vendor.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedVendor && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">{selectedVendor.businessName}</h4>
                  <Badge variant={selectedVendor.status === 'active' ? 'default' : 'secondary'}>
                    {selectedVendor.status === 'active' ? 'Active' : 'Inactive'}
                  </Badge>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="terminalName" className="text-right">
                    Terminal Name
                  </label>
                  <Input
                    id="terminalName"
                    defaultValue={selectedVendor.terminalName || `${selectedVendor.businessName} Terminal`}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="deviceId" className="text-right">
                    Device ID
                  </label>
                  <Input
                    id="deviceId"
                    defaultValue={selectedVendor.deviceId || ''}
                    className="col-span-3"
                    disabled
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="text-right">Offline Mode</div>
                  <div className="flex items-center space-x-2 col-span-3">
                    <input
                      type="checkbox"
                      id="offlineMode"
                      defaultChecked={selectedVendor.offlineMode}
                      className="h-4 w-4 rounded border-gray-300"
                    />
                    <label htmlFor="offlineMode">
                      Enable offline transaction processing
                    </label>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="text-right">Auto Sync</div>
                  <div className="flex items-center space-x-2 col-span-3">
                    <input
                      type="checkbox"
                      id="autoSync"
                      defaultChecked={selectedVendor.autoSync}
                      className="h-4 w-4 rounded border-gray-300"
                    />
                    <label htmlFor="autoSync">
                      Automatically sync offline transactions when online
                    </label>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="text-right">Notifications</div>
                  <div className="flex items-center space-x-2 col-span-3">
                    <input
                      type="checkbox"
                      id="notifications"
                      defaultChecked={selectedVendor.notificationsEnabled}
                      className="h-4 w-4 rounded border-gray-300"
                    />
                    <label htmlFor="notifications">
                      Enable transaction notifications
                    </label>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="text-right">Auto Print</div>
                  <div className="flex items-center space-x-2 col-span-3">
                    <input
                      type="checkbox"
                      id="autoPrint"
                      defaultChecked={selectedVendor.autoPrint}
                      className="h-4 w-4 rounded border-gray-300"
                    />
                    <label htmlFor="autoPrint">
                      Automatically print receipts
                    </label>
                  </div>
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSettingsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveTerminalSettings}>
              Save Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Vendor Dialog */}
      <Dialog open={isAddVendorDialogOpen} onOpenChange={setIsAddVendorDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add Vendor to NFC System</DialogTitle>
            <DialogDescription>
              Add a vendor to the NFC payment system by configuring their terminal settings.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="vendorId" className="text-right">
                Vendor User ID
              </label>
              <Input
                id="vendorId"
                placeholder="Enter vendor user ID"
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="newTerminalName" className="text-right">
                Terminal Name
              </label>
              <Input
                id="newTerminalName"
                placeholder="Main Terminal"
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <div className="text-right">Offline Mode</div>
              <div className="flex items-center space-x-2 col-span-3">
                <input
                  type="checkbox"
                  id="newOfflineMode"
                  className="h-4 w-4 rounded border-gray-300"
                />
                <label htmlFor="newOfflineMode">
                  Enable offline transaction processing
                </label>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <div className="text-right">Auto Sync</div>
              <div className="flex items-center space-x-2 col-span-3">
                <input
                  type="checkbox"
                  id="newAutoSync"
                  defaultChecked={true}
                  className="h-4 w-4 rounded border-gray-300"
                />
                <label htmlFor="newAutoSync">
                  Automatically sync offline transactions when online
                </label>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <div className="text-right">Notifications</div>
              <div className="flex items-center space-x-2 col-span-3">
                <input
                  type="checkbox"
                  id="newNotifications"
                  defaultChecked={true}
                  className="h-4 w-4 rounded border-gray-300"
                />
                <label htmlFor="newNotifications">
                  Enable transaction notifications
                </label>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <div className="text-right">Auto Print</div>
              <div className="flex items-center space-x-2 col-span-3">
                <input
                  type="checkbox"
                  id="newAutoPrint"
                  className="h-4 w-4 rounded border-gray-300"
                />
                <label htmlFor="newAutoPrint">
                  Automatically print receipts
                </label>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddVendorDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={async () => {
              // Get form values
              const userId = (document.getElementById('vendorId') as HTMLInputElement)?.value;
              const terminalName = (document.getElementById('newTerminalName') as HTMLInputElement)?.value;
              const offlineMode = (document.getElementById('newOfflineMode') as HTMLInputElement)?.checked;
              const autoSync = (document.getElementById('newAutoSync') as HTMLInputElement)?.checked;
              const notificationsEnabled = (document.getElementById('newNotifications') as HTMLInputElement)?.checked;
              const autoPrint = (document.getElementById('newAutoPrint') as HTMLInputElement)?.checked;

              if (!userId) {
                toast({
                  title: 'Error',
                  description: 'Vendor User ID is required.',
                  variant: 'destructive',
                });
                return;
              }

              try {
                const response = await fetch('/api/admin/nfc/vendors', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    userId,
                    terminalName,
                    offlineMode,
                    autoSync,
                    notificationsEnabled,
                    autoPrint,
                  }),
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || `Error: ${response.status}`);
                }

                toast({
                  title: 'Vendor Added',
                  description: 'The vendor has been added to the NFC system successfully.',
                });

                // Refresh the vendor list
                fetchVendors();
                setIsAddVendorDialogOpen(false);
              } catch (err) {
                console.error('Error adding vendor:', err);
                toast({
                  title: 'Error',
                  description: err instanceof Error ? err.message : 'Failed to add vendor. Please try again.',
                  variant: 'destructive',
                });
              }
            }}>
              Add Vendor
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
