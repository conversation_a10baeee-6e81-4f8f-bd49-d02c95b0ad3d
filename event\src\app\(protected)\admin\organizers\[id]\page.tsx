'use client';

import React, { useState, useEffect, use } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  Calendar, 
  DollarSign, 
  Ticket, 
  User, 
  Mail, 
  Phone,
  MapPin,
  Building,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  AlertTriangle
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';

interface Organizer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  isVerified: boolean;
  accountBalance: number;
  totalEvents: number;
  totalSales: number;
  totalTicketsSold: number;
  joinedDate: string;
  events: Array<{
    id: string;
    title: string;
    startDate: string;
    status: string;
    imagePath?: string;
    totalOrders: number;
  }>;
  recentTransactions: Array<{
    id: string;
    amount: number;
    type: string;
    description: string;
    date: string;
  }>;
}

export default function OrganizerDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [organizer, setOrganizer] = useState<Organizer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrganizerDetails = async () => {
      try {
        const response = await fetch(`/api/admin/organizers/${params?.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch organizer details');
        }
        const data = await response.json();
        setOrganizer(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (params?.id) {
      fetchOrganizerDetails();
    }
  }, [params?.id]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading organizer details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !organizer) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <AlertTriangle className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold text-red-700 mb-2">
            {error || 'Organizer not found'}
          </h2>
          <p className="text-red-600 mb-6">
            We couldn't find the organizer you're looking for.
          </p>
          <Button asChild>
            <Link href="/admin/organizers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Organizers
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button variant="outline" asChild>
            <Link href="/admin/organizers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Organizers
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{organizer.name}</h1>
            <p className="text-gray-600">Organizer Details</p>
          </div>
        </div>
        <Badge className={organizer.isVerified 
          ? "bg-green-100 text-green-800 hover:bg-green-200" 
          : "bg-amber-100 text-amber-800 hover:bg-amber-200"}>
          {organizer.isVerified ? (
            <>
              <CheckCircle className="w-4 h-4 mr-1" />
              Verified
            </>
          ) : (
            <>
              <XCircle className="w-4 h-4 mr-1" />
              Unverified
            </>
          )}
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Organizer Info */}
        <div className="lg:col-span-1 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-gray-500" />
                <span className="text-sm">{organizer.email}</span>
              </div>
              {organizer.phone && (
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{organizer.phone}</span>
                </div>
              )}
              <div className="flex items-center space-x-3">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-sm">
                  Joined {format(new Date(organizer.joinedDate), 'PPP')}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-blue-500" />
                  <span className="text-sm">Total Events</span>
                </div>
                <span className="font-semibold">{organizer.totalEvents}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Ticket className="h-4 w-4 text-purple-500" />
                  <span className="text-sm">Tickets Sold</span>
                </div>
                <span className="font-semibold">{organizer.totalTicketsSold}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Total Sales</span>
                </div>
                <span className="font-semibold">${organizer.totalSales.toLocaleString()}</span>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Account Balance</span>
                <span className="font-bold text-lg">${organizer.accountBalance.toFixed(2)}</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Events and Transactions */}
        <div className="lg:col-span-2 space-y-6">
          {/* Recent Events */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Events</CardTitle>
              <CardDescription>Latest events created by this organizer</CardDescription>
            </CardHeader>
            <CardContent>
              {organizer.events.length > 0 ? (
                <div className="space-y-4">
                  {organizer.events.map((event) => (
                    <div key={event.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium">{event.title}</h4>
                        <p className="text-sm text-gray-600">
                          {format(new Date(event.startDate), 'PPP')}
                        </p>
                        <div className="flex items-center space-x-4 mt-2">
                          <Badge variant="outline">{event.status}</Badge>
                          <span className="text-xs text-gray-500">
                            {event.totalOrders} orders
                          </span>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/admin/events/${event.id}`}>
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Link>
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">No events found</p>
              )}
            </CardContent>
          </Card>

          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
              <CardDescription>Latest financial transactions</CardDescription>
            </CardHeader>
            <CardContent>
              {organizer.recentTransactions.length > 0 ? (
                <div className="space-y-3">
                  {organizer.recentTransactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex-1">
                        <p className="font-medium text-sm">{transaction.description}</p>
                        <p className="text-xs text-gray-500">
                          {format(new Date(transaction.date), 'PPp')}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className={`font-semibold ${
                          transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {transaction.amount >= 0 ? '+' : ''}${Math.abs(transaction.amount).toFixed(2)}
                        </p>
                        <p className="text-xs text-gray-500 capitalize">{transaction.type}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">No transactions found</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
