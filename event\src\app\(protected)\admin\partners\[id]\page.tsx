'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useCurrentRole } from '@/hooks/use-current-role';
import {
  Hotel, Utensils, Wine, Music, ArrowLeft, CheckCircle, XCircle, 
  Star, MapPin, Phone, Mail, Globe, Save, Trash2, AlertTriangle
} from 'lucide-react';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from '@/components/ui/skeleton';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

// Define Partner interface
interface Partner {
  id: string;
  userId: string;
  businessName: string;
  partnerType: 'HOTEL' | 'RESTAURANT' | 'BAR' | 'NIGHTCLUB' | string;
  tier: 'BASIC' | 'PREMIUM' | 'ELITE';
  description?: string;
  address: string;
  city: string;
  province: string;
  postalCode?: string;
  country: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  website?: string;
  logo?: string;
  bannerImage?: string;
  amenities: string[];
  priceRange?: string;
  rating?: number;
  totalReviews: number;
  isVerified: boolean;
  verifiedAt?: string;
  featured: boolean;
  acceptsNfcPayments: boolean;
  nfcTerminalId?: string;
  commissionRate: number;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
}

export default function AdminPartnerDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const role = useCurrentRole();
  const resolvedParams = use(params);
  const [partner, setPartner] = useState<Partner | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<Partial<Partner>>({});
  const [isEditing, setIsEditing] = useState(false);

  // Fetch partner details
  useEffect(() => {
    const fetchPartner = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/partners/${resolvedParams.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch partner details');
        }
        const data = await response.json();
        setPartner(data);
        setFormData(data);
      } catch (error) {
        console.error('Error fetching partner details:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (resolvedParams.id) {
      fetchPartner();
    }
  }, [resolvedParams.id]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData({ ...formData, [name]: value });
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData({ ...formData, [name]: checked });
  };

  // Save partner changes
  const savePartner = async () => {
    try {
      setIsSubmitting(true);
      const response = await fetch(`/api/admin/partners/${resolvedParams.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update partner');
      }
      
      const updatedPartner = await response.json();
      setPartner(updatedPartner);
      setIsEditing(false);
      
      // Show success message
      alert('Partner updated successfully');
    } catch (error) {
      console.error('Error updating partner:', error);
      alert('Failed to update partner');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Verify or unverify partner
  const toggleVerification = async () => {
    try {
      setIsSubmitting(true);
      const response = await fetch(`/api/admin/partners/${resolvedParams.id}/verify`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isVerified: !partner?.isVerified }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update partner verification status');
      }
      
      const updatedPartner = await response.json();
      setPartner(updatedPartner);
      setFormData(updatedPartner);
      
      // Show success message
      alert(`Partner ${updatedPartner.isVerified ? 'verified' : 'unverified'} successfully`);
    } catch (error) {
      console.error('Error updating partner verification status:', error);
      alert('Failed to update partner verification status');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Toggle featured status
  const toggleFeatured = async () => {
    try {
      setIsSubmitting(true);
      const response = await fetch(`/api/admin/partners/${resolvedParams.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ featured: !partner?.featured }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update partner featured status');
      }
      
      const updatedPartner = await response.json();
      setPartner(updatedPartner);
      setFormData(updatedPartner);
      
      // Show success message
      alert(`Partner ${updatedPartner.featured ? 'featured' : 'unfeatured'} successfully`);
    } catch (error) {
      console.error('Error updating partner featured status:', error);
      alert('Failed to update partner featured status');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get partner type icon
  const getPartnerTypeIcon = (partnerType: string) => {
    switch (partnerType) {
      case 'HOTEL':
        return <Hotel className="h-5 w-5" />;
      case 'RESTAURANT':
        return <Utensils className="h-5 w-5" />;
      case 'BAR':
        return <Wine className="h-5 w-5" />;
      case 'NIGHTCLUB':
        return <Music className="h-5 w-5" />;
      default:
        return <Hotel className="h-5 w-5" />;
    }
  };

  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    router.push('/dashboard');
    return null;
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Skeleton className="h-10 w-40" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <Skeleton className="h-8 w-64 mb-2" />
                <Skeleton className="h-4 w-full" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Skeleton className="h-32 w-full" />
                  <div className="grid grid-cols-2 gap-4">
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          <div>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (!partner) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <AlertTriangle className="mx-auto h-12 w-12 text-yellow-500 mb-4" />
          <h2 className="text-2xl font-bold mb-2">Partner Not Found</h2>
          <p className="text-gray-500 mb-4">The partner you are looking for does not exist or has been removed.</p>
          <Button asChild>
            <Link href="/admin/partners">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Partners
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <Button variant="outline" asChild className="mb-4">
          <Link href="/admin/partners">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Partners
          </Link>
        </Button>
        
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold flex items-center">
              {getPartnerTypeIcon(partner.partnerType)}
              <span className="ml-2">{partner.businessName}</span>
            </h1>
            <div className="flex items-center mt-2">
              {partner.isVerified ? (
                <Badge className="bg-green-500 hover:bg-green-600">Verified</Badge>
              ) : (
                <Badge variant="outline" className="text-amber-500 border-amber-500">Pending Verification</Badge>
              )}
              
              {partner.featured && (
                <Badge className="ml-2 bg-yellow-500 hover:bg-yellow-600">Featured</Badge>
              )}
              
              <Badge className="ml-2" variant="outline">{partner.tier}</Badge>
            </div>
          </div>
          
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            {isEditing ? (
              <>
                <Button variant="outline" onClick={() => setIsEditing(false)} disabled={isSubmitting}>
                  Cancel
                </Button>
                <Button onClick={savePartner} disabled={isSubmitting}>
                  {isSubmitting ? 'Saving...' : 'Save Changes'}
                </Button>
              </>
            ) : (
              <>
                <Button variant="outline" onClick={() => setIsEditing(true)}>
                  Edit Partner
                </Button>
                <Button 
                  variant={partner.isVerified ? "destructive" : "default"}
                  onClick={toggleVerification}
                  disabled={isSubmitting}
                >
                  {partner.isVerified ? (
                    <>
                      <XCircle className="mr-2 h-4 w-4" />
                      Revoke Verification
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Verify Partner
                    </>
                  )}
                </Button>
                <Button 
                  variant={partner.featured ? "secondary" : "outline"}
                  onClick={toggleFeatured}
                  disabled={isSubmitting}
                >
                  {partner.featured ? (
                    <>
                      <Star className="mr-2 h-4 w-4 fill-current" />
                      Featured
                    </>
                  ) : (
                    <>
                      <Star className="mr-2 h-4 w-4" />
                      Make Featured
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Partner Details</CardTitle>
              <CardDescription>
                Basic information about the partner
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isEditing ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="businessName">Business Name</Label>
                      <Input
                        id="businessName"
                        name="businessName"
                        value={formData.businessName || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="partnerType">Partner Type</Label>
                      <Select
                        value={formData.partnerType}
                        onValueChange={(value) => handleSelectChange('partnerType', value)}
                      >
                        <SelectTrigger id="partnerType">
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="HOTEL">Hotel</SelectItem>
                          <SelectItem value="RESTAURANT">Restaurant</SelectItem>
                          <SelectItem value="BAR">Bar</SelectItem>
                          <SelectItem value="NIGHTCLUB">Nightclub</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      name="description"
                      value={formData.description || ''}
                      onChange={handleInputChange}
                      rows={4}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="tier">Tier</Label>
                      <Select
                        value={formData.tier}
                        onValueChange={(value) => handleSelectChange('tier', value)}
                      >
                        <SelectTrigger id="tier">
                          <SelectValue placeholder="Select tier" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="BASIC">Basic</SelectItem>
                          <SelectItem value="PREMIUM">Premium</SelectItem>
                          <SelectItem value="ELITE">Elite</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="priceRange">Price Range</Label>
                      <Input
                        id="priceRange"
                        name="priceRange"
                        value={formData.priceRange || ''}
                        onChange={handleInputChange}
                        placeholder="e.g. $$ or 100-500 ZMW"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="address">Address</Label>
                      <Input
                        id="address"
                        name="address"
                        value={formData.address || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        name="city"
                        value={formData.city || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="province">Province</Label>
                      <Input
                        id="province"
                        name="province"
                        value={formData.province || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <Input
                        id="country"
                        name="country"
                        value={formData.country || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="contactName">Contact Name</Label>
                      <Input
                        id="contactName"
                        name="contactName"
                        value={formData.contactName || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contactEmail">Contact Email</Label>
                      <Input
                        id="contactEmail"
                        name="contactEmail"
                        type="email"
                        value={formData.contactEmail || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="contactPhone">Contact Phone</Label>
                      <Input
                        id="contactPhone"
                        name="contactPhone"
                        value={formData.contactPhone || ''}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <Input
                        id="website"
                        name="website"
                        value={formData.website || ''}
                        onChange={handleInputChange}
                        placeholder="https://example.com"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="commissionRate">Commission Rate (%)</Label>
                      <Input
                        id="commissionRate"
                        name="commissionRate"
                        type="number"
                        value={formData.commissionRate || 5}
                        onChange={handleInputChange}
                        min={0}
                        max={100}
                        step={0.1}
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-4 pt-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="acceptsNfcPayments"
                        checked={formData.acceptsNfcPayments || false}
                        onCheckedChange={(checked) => handleSwitchChange('acceptsNfcPayments', checked)}
                      />
                      <Label htmlFor="acceptsNfcPayments">Accepts NFC Payments</Label>
                    </div>
                    
                    {formData.acceptsNfcPayments && (
                      <div className="space-y-2 pl-6">
                        <Label htmlFor="nfcTerminalId">NFC Terminal ID</Label>
                        <Input
                          id="nfcTerminalId"
                          name="nfcTerminalId"
                          value={formData.nfcTerminalId || ''}
                          onChange={handleInputChange}
                          placeholder="Terminal ID"
                        />
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium">About</h3>
                    <p className="text-gray-600 mt-2">{partner.description || 'No description provided.'}</p>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium">Contact Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 text-gray-500 mr-2" />
                        <span>{partner.address}, {partner.city}, {partner.province}, {partner.country}</span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 text-gray-500 mr-2" />
                        <span>{partner.contactPhone}</span>
                      </div>
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 text-gray-500 mr-2" />
                        <span>{partner.contactEmail}</span>
                      </div>
                      {partner.website && (
                        <div className="flex items-center">
                          <Globe className="h-4 w-4 text-gray-500 mr-2" />
                          <a href={partner.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                            {partner.website}
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-medium">Business Details</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                      <div>
                        <span className="text-gray-500">Contact Person:</span>
                        <span className="ml-2">{partner.contactName}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Price Range:</span>
                        <span className="ml-2">{partner.priceRange || 'Not specified'}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Commission Rate:</span>
                        <span className="ml-2">{partner.commissionRate}%</span>
                      </div>
                      <div>
                        <span className="text-gray-500">NFC Payments:</span>
                        <span className="ml-2">{partner.acceptsNfcPayments ? 'Accepted' : 'Not accepted'}</span>
                      </div>
                      {partner.acceptsNfcPayments && partner.nfcTerminalId && (
                        <div>
                          <span className="text-gray-500">Terminal ID:</span>
                          <span className="ml-2">{partner.nfcTerminalId}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {partner.amenities && partner.amenities.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium">Amenities</h3>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {partner.amenities.map((amenity, index) => (
                          <Badge key={index} variant="secondary">{amenity}</Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Partner Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium">Verification Status</p>
                  <div className="flex items-center mt-1">
                    {partner.isVerified ? (
                      <>
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span>Verified</span>
                        {partner.verifiedAt && (
                          <span className="text-gray-500 text-sm ml-2">
                            on {new Date(partner.verifiedAt).toLocaleDateString()}
                          </span>
                        )}
                      </>
                    ) : (
                      <>
                        <XCircle className="h-4 w-4 text-amber-500 mr-2" />
                        <span>Not Verified</span>
                      </>
                    )}
                  </div>
                </div>
                
                <div>
                  <p className="text-sm font-medium">Featured Status</p>
                  <div className="flex items-center mt-1">
                    {partner.featured ? (
                      <>
                        <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-2" />
                        <span>Featured</span>
                      </>
                    ) : (
                      <>
                        <Star className="h-4 w-4 text-gray-300 mr-2" />
                        <span>Not Featured</span>
                      </>
                    )}
                  </div>
                </div>
                
                <div>
                  <p className="text-sm font-medium">Membership Tier</p>
                  <div className="flex items-center mt-1">
                    <Badge variant="outline">{partner.tier}</Badge>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm font-medium">Rating</p>
                  <div className="flex items-center mt-1">
                    <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-1" />
                    <span>{partner.rating || 'No ratings'}</span>
                    {partner.totalReviews > 0 && (
                      <span className="text-gray-500 text-sm ml-2">
                        ({partner.totalReviews} {partner.totalReviews === 1 ? 'review' : 'reviews'})
                      </span>
                    )}
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <p className="text-sm font-medium">Created</p>
                  <p className="text-gray-500 mt-1">
                    {new Date(partner.createdAt).toLocaleDateString()}
                  </p>
                </div>
                
                <div>
                  <p className="text-sm font-medium">Last Updated</p>
                  <p className="text-gray-500 mt-1">
                    {new Date(partner.updatedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col items-stretch space-y-2">
              <Button asChild variant="outline">
                <Link href={`/partners/${partner.id}`} target="_blank">
                  View Public Profile
                </Link>
              </Button>
              
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Partner
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete the partner
                      and all associated data from our servers.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction className="bg-red-600 hover:bg-red-700">
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
