'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useCurrentRole } from '@/hooks/use-current-role';
import { Loader2, Upload, UserPlus, Shield, Users, User, Settings, Key } from 'lucide-react';

interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: string;
  image?: string;
  managedBy?: {
    id: string;
    name: string;
    email: string;
  } | null;
  managedUsers?: {
    id: string;
    name: string;
    email: string;
  }[];
}

export default function AdminSettingsPage() {
  const router = useRouter();
  const user = useCurrentUser();
  const role = useCurrentRole();

  const [loading, setLoading] = useState(true);
  const [profileData, setProfileData] = useState<AdminUser | null>(null);
  const [adminUsers, setAdminUsers] = useState<AdminUser[]>([]);
  const [uploadingImage, setUploadingImage] = useState(false);

  // Form states
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // New admin form states
  const [newAdminName, setNewAdminName] = useState('');
  const [newAdminEmail, setNewAdminEmail] = useState('');
  const [newAdminPassword, setNewAdminPassword] = useState('');
  const [newAdminRole, setNewAdminRole] = useState('ADMIN');
  const [newAdminManager, setNewAdminManager] = useState('');
  const [creatingAdmin, setCreatingAdmin] = useState(false);

  // Fetch profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/profile');

        if (!response.ok) {
          throw new Error('Failed to fetch profile data');
        }

        const data = await response.json();
        setProfileData(data);
        setName(data.name || '');
        setEmail(data.email || '');

        setLoading(false);
      } catch (error) {
        console.error('Error fetching profile:', error);
        toast({
          title: 'Error',
          description: 'Failed to load profile data. Please try again.',
          variant: 'destructive',
        });
        setLoading(false);
      }
    };

    fetchProfileData();
  }, []);

  // Fetch admin users (only for SUPERADMIN)
  useEffect(() => {
    const fetchAdminUsers = async () => {
      if (role !== 'SUPERADMIN') return;

      try {
        const response = await fetch('/api/admin/manage');

        if (!response.ok) {
          throw new Error('Failed to fetch admin users');
        }

        const data = await response.json();
        setAdminUsers(data);
      } catch (error) {
        console.error('Error fetching admin users:', error);
        toast({
          title: 'Error',
          description: 'Failed to load admin users. Please try again.',
          variant: 'destructive',
        });
      }
    };

    fetchAdminUsers();
  }, [role]);

  // Handle profile update
  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);

      const response = await fetch('/api/admin/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          email,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update profile');
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: 'Profile updated successfully',
      });

      // Update profile data
      if (profileData) {
        setProfileData({
          ...profileData,
          name: data.user.name,
          email: data.user.email,
        });
      }

      setLoading(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update profile',
        variant: 'destructive',
      });
      setLoading(false);
    }
  };

  // Handle password change
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();

    if (newPassword !== confirmPassword) {
      toast({
        title: 'Error',
        description: 'New passwords do not match',
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);

      // Implement password change API call here

      toast({
        title: 'Success',
        description: 'Password changed successfully',
      });

      // Reset form
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');

      setLoading(false);
    } catch (error) {
      console.error('Error changing password:', error);
      toast({
        title: 'Error',
        description: 'Failed to change password',
        variant: 'destructive',
      });
      setLoading(false);
    }
  };

  // Handle profile picture upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setUploadingImage(true);

      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/admin/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload image');
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: 'Profile picture uploaded successfully',
      });

      // Update profile data with new image
      if (profileData) {
        setProfileData({
          ...profileData,
          image: data.user.image,
        });
      }

      setUploadingImage(false);
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to upload image',
        variant: 'destructive',
      });
      setUploadingImage(false);
    }
  };

  // Handle creating new admin
  const handleCreateAdmin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newAdminName || !newAdminEmail || !newAdminPassword) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    try {
      setCreatingAdmin(true);

      const response = await fetch('/api/admin/manage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newAdminName,
          email: newAdminEmail,
          password: newAdminPassword,
          role: newAdminRole,
          managedById: newAdminManager || undefined,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create admin user');
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: 'Admin user created successfully',
      });

      // Reset form
      setNewAdminName('');
      setNewAdminEmail('');
      setNewAdminPassword('');
      setNewAdminRole('ADMIN');
      setNewAdminManager('');

      // Refresh admin users list
      const refreshResponse = await fetch('/api/admin/manage');
      if (refreshResponse.ok) {
        const refreshData = await refreshResponse.json();
        setAdminUsers(refreshData);
      }

      setCreatingAdmin(false);
    } catch (error) {
      console.error('Error creating admin:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create admin user',
        variant: 'destructive',
      });
      setCreatingAdmin(false);
    }
  };

  // Access control - only ADMIN and SUPERADMIN can access
  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin privileges to view this page.</p>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Admin Settings</h1>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid grid-cols-3 w-full max-w-md">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          {role === 'SUPERADMIN' && (
            <TabsTrigger value="admins">Manage Admins</TabsTrigger>
          )}
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Profile Picture Card */}
            <Card>
              <CardHeader>
                <CardTitle>Profile Picture</CardTitle>
                <CardDescription>Update your profile picture</CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center">
                {loading ? (
                  <div className="w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                  </div>
                ) : (
                  <div className="relative">
                    <Avatar className="w-32 h-32">
                      <AvatarImage src={profileData?.image || ''} alt={profileData?.name || 'Profile'} />
                      <AvatarFallback className="text-2xl">
                        {profileData?.name?.charAt(0) || 'A'}
                      </AvatarFallback>
                    </Avatar>
                    {uploadingImage && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                        <Loader2 className="h-8 w-8 animate-spin text-white" />
                      </div>
                    )}
                  </div>
                )}

                <div className="mt-6">
                  <Label htmlFor="profile-picture" className="cursor-pointer">
                    <div className="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                      <Upload className="mr-2 h-4 w-4" />
                      Upload new picture
                    </div>
                    <Input
                      id="profile-picture"
                      type="file"
                      accept="image/*"
                      className="sr-only"
                      onChange={handleImageUpload}
                      disabled={uploadingImage}
                    />
                  </Label>
                </div>

                <p className="text-xs text-gray-500 mt-4">
                  Recommended: Square image, at least 300x300 pixels.
                </p>
              </CardContent>
            </Card>

            {/* Profile Information Card */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>Update your account details</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleProfileUpdate} className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder="Your name"
                        disabled={loading}
                      />
                    </div>

                    <div>
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Your email"
                        disabled={loading}
                      />
                    </div>

                    <div>
                      <Label htmlFor="role">Role</Label>
                      <Input
                        id="role"
                        value={profileData?.role || ''}
                        disabled
                        className="bg-gray-50"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Your role cannot be changed here.
                      </p>
                    </div>
                  </div>

                  <Button type="submit" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>Update your password and security preferences</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordChange} className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="current-password">Current Password</Label>
                    <Input
                      id="current-password"
                      type="password"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      placeholder="Enter current password"
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <Label htmlFor="new-password">New Password</Label>
                    <Input
                      id="new-password"
                      type="password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      placeholder="Enter new password"
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <Label htmlFor="confirm-password">Confirm New Password</Label>
                    <Input
                      id="confirm-password"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      placeholder="Confirm new password"
                      disabled={loading}
                    />
                  </div>
                </div>

                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    'Change Password'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Manage Admins Tab (SUPERADMIN only) */}
        {role === 'SUPERADMIN' && (
          <TabsContent value="admins">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Create Admin Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Create Admin</CardTitle>
                  <CardDescription>Add a new admin user</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleCreateAdmin} className="space-y-4">
                    <div>
                      <Label htmlFor="new-admin-name">Name</Label>
                      <Input
                        id="new-admin-name"
                        value={newAdminName}
                        onChange={(e) => setNewAdminName(e.target.value)}
                        placeholder="Admin name"
                        disabled={creatingAdmin}
                      />
                    </div>

                    <div>
                      <Label htmlFor="new-admin-email">Email</Label>
                      <Input
                        id="new-admin-email"
                        type="email"
                        value={newAdminEmail}
                        onChange={(e) => setNewAdminEmail(e.target.value)}
                        placeholder="Admin email"
                        disabled={creatingAdmin}
                      />
                    </div>

                    <div>
                      <Label htmlFor="new-admin-password">Password</Label>
                      <Input
                        id="new-admin-password"
                        type="password"
                        value={newAdminPassword}
                        onChange={(e) => setNewAdminPassword(e.target.value)}
                        placeholder="Set password"
                        disabled={creatingAdmin}
                      />
                    </div>

                    <div>
                      <Label htmlFor="new-admin-role">Role</Label>
                      <select
                        id="new-admin-role"
                        value={newAdminRole}
                        onChange={(e) => setNewAdminRole(e.target.value)}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        disabled={creatingAdmin}
                      >
                        <option value="ADMIN">Admin</option>
                        <option value="SUPERADMIN">Super Admin</option>
                      </select>
                    </div>

                    <div>
                      <Label htmlFor="new-admin-manager">Manager (Optional)</Label>
                      <select
                        id="new-admin-manager"
                        value={newAdminManager}
                        onChange={(e) => setNewAdminManager(e.target.value)}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        disabled={creatingAdmin}
                      >
                        <option value="">No Manager</option>
                        {adminUsers
                          .filter(admin => admin.role === 'SUPERADMIN')
                          .map(admin => (
                            <option key={admin.id} value={admin.id}>
                              {admin.name} ({admin.email})
                            </option>
                          ))}
                      </select>
                    </div>

                    <Button type="submit" className="w-full" disabled={creatingAdmin}>
                      {creatingAdmin ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <UserPlus className="mr-2 h-4 w-4" />
                          Create Admin
                        </>
                      )}
                    </Button>
                  </form>
                </CardContent>
              </Card>

              {/* Admin Users List */}
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle>Admin Users</CardTitle>
                  <CardDescription>Manage existing admin accounts</CardDescription>
                </CardHeader>
                <CardContent>
                  {adminUsers.length === 0 ? (
                    <div className="text-center py-6">
                      <Users className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">No admin users found</h3>
                      <p className="text-gray-500 max-w-md mx-auto">
                        Create your first admin user using the form on the left.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {adminUsers.map(admin => (
                        <div key={admin.id} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <Avatar className="h-10 w-10 mr-4">
                                <AvatarImage src={admin.image || ''} alt={admin.name} />
                                <AvatarFallback>{admin.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <div>
                                <h3 className="font-medium">{admin.name}</h3>
                                <p className="text-sm text-gray-500">{admin.email}</p>
                              </div>
                            </div>
                            <div className="flex items-center">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                admin.role === 'SUPERADMIN'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-blue-100 text-blue-800'
                              }`}>
                                {admin.role}
                              </span>
                            </div>
                          </div>

                          {admin.managedBy && (
                            <div className="mt-2 text-sm text-gray-500">
                              <span className="font-medium">Manager:</span> {admin.managedBy.name}
                            </div>
                          )}

                          {admin.managedUsers && admin.managedUsers.length > 0 && (
                            <div className="mt-2 text-sm text-gray-500">
                              <span className="font-medium">Managing:</span> {admin.managedUsers.length} users
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
