'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useCurrentRole } from '@/hooks/use-current-role';
import { CreditCard, ArrowLeft, Download, Calendar, RefreshCw, AlertTriangle, BarChart3 } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import Link from 'next/link';
import { TransactionsList } from '@/components/admin/transactions-list';

interface TransactionStats {
  totalTransactions: number;
  successfulTransactions: number;
  successRate: number;
  failedTransactions: number;
  failureRate: number;
  transactionGrowth: number;
  averageTransactionAmount: number;
  averageAmountGrowth: number;
}

export default function AdminTransactionsPage() {
  const router = useRouter();
  const user = useCurrentUser();
  const role = useCurrentRole();
  const [activeTab, setActiveTab] = useState('all');
  const [stats, setStats] = useState<TransactionStats | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // Fetch transaction statistics
  const fetchTransactionStats = async () => {
    setIsLoadingStats(true);
    setStatsError(null);

    try {
      const response = await fetch('/api/admin/finance/statistics');

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error('Error fetching transaction statistics:', err);
      setStatsError('Failed to load transaction statistics');
      toast({
        title: 'Error',
        description: 'Failed to load transaction statistics',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingStats(false);
    }
  };

  // Fetch statistics on component mount
  useEffect(() => {
    fetchTransactionStats();
  }, []);

  // Access control - only ADMIN and SUPERADMIN can access
  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin privileges to view this page.</p>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <div className="mb-2">
            <Link href="/admin/finance" className="inline-flex items-center text-blue-600 hover:underline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Financial Dashboard
            </Link>
          </div>
          <h1 className="text-3xl font-bold">Transaction Management</h1>
          <p className="text-gray-500 mt-1">
            View and manage all financial transactions on the platform
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-3">
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            Date Range
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
          <Button asChild>
            <Link href="/admin/finance/reports">
              <BarChart3 className="mr-2 h-4 w-4" />
              Revenue Reports
            </Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Transactions</TabsTrigger>
          <TabsTrigger value="purchases">Purchases</TabsTrigger>
          <TabsTrigger value="payouts">Payouts</TabsTrigger>
          <TabsTrigger value="refunds">Refunds</TabsTrigger>
          <TabsTrigger value="fees">Platform Fees</TabsTrigger>
        </TabsList>

        <Card>
          <CardHeader>
            <CardTitle>
              {activeTab === 'all' && 'All Transactions'}
              {activeTab === 'purchases' && 'Ticket Purchases'}
              {activeTab === 'payouts' && 'Organizer Payouts'}
              {activeTab === 'refunds' && 'Refunds'}
              {activeTab === 'fees' && 'Platform Fees'}
            </CardTitle>
            <CardDescription>
              {activeTab === 'all' && 'Complete transaction history across all categories'}
              {activeTab === 'purchases' && 'All ticket purchase transactions'}
              {activeTab === 'payouts' && 'All payout transactions to organizers'}
              {activeTab === 'refunds' && 'All refund transactions'}
              {activeTab === 'fees' && 'All platform fee transactions'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <TransactionsList
              typeFilter={
                activeTab === 'purchases' ? 'TICKET_PURCHASE' :
                activeTab === 'payouts' ? 'PAYOUT' :
                activeTab === 'refunds' ? 'REFUND' :
                activeTab === 'fees' ? 'PLATFORM_FEE' :
                undefined
              }
            />
          </CardContent>
        </Card>
      </Tabs>

      {/* Transaction Statistics */}
      <div className="mt-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Transaction Statistics</h2>
          {statsError ? (
            <Button
              variant="outline"
              size="sm"
              onClick={fetchTransactionStats}
              disabled={isLoadingStats}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingStats ? 'animate-spin' : ''}`} />
              Retry
            </Button>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={fetchTransactionStats}
              disabled={isLoadingStats}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingStats ? 'animate-spin' : ''}`} />
              {isLoadingStats ? 'Loading...' : 'Refresh'}
            </Button>
          )}
        </div>

        {statsError && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6 flex items-center">
            <AlertTriangle className="h-4 w-4 mr-2" />
            {statsError}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-100">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-blue-600">Total Transactions</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-24 mt-1" />
                  ) : (
                    <h3 className="text-3xl font-bold mt-1">
                      {stats?.totalTransactions.toLocaleString() || '0'}
                    </h3>
                  )}
                  {!isLoadingStats && stats && (
                    <p className={`text-sm ${stats.transactionGrowth >= 0 ? 'text-green-600' : 'text-red-600'} mt-1`}>
                      {stats.transactionGrowth >= 0 ? '+' : ''}{stats.transactionGrowth.toFixed(1)}% from last month
                    </p>
                  )}
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <CreditCard className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-100">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-green-600">Successful Transactions</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-24 mt-1" />
                  ) : (
                    <h3 className="text-3xl font-bold mt-1">
                      {stats?.successfulTransactions.toLocaleString() || '0'}
                    </h3>
                  )}
                  {!isLoadingStats && stats && (
                    <p className="text-sm text-green-600 mt-1">
                      {stats.successRate.toFixed(1)}% success rate
                    </p>
                  )}
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <CreditCard className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-50 to-rose-50 border-red-100">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-red-600">Failed Transactions</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-24 mt-1" />
                  ) : (
                    <h3 className="text-3xl font-bold mt-1">
                      {stats?.failedTransactions.toLocaleString() || '0'}
                    </h3>
                  )}
                  {!isLoadingStats && stats && (
                    <p className="text-sm text-red-600 mt-1">
                      {stats.failureRate.toFixed(1)}% failure rate
                    </p>
                  )}
                </div>
                <div className="bg-red-100 p-3 rounded-full">
                  <CreditCard className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-100">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-purple-600">Average Transaction</p>
                  {isLoadingStats ? (
                    <Skeleton className="h-8 w-24 mt-1" />
                  ) : (
                    <h3 className="text-3xl font-bold mt-1">
                      ${stats?.averageTransactionAmount.toFixed(2) || '0.00'}
                    </h3>
                  )}
                  {!isLoadingStats && stats && (
                    <p className={`text-sm ${stats.averageAmountGrowth >= 0 ? 'text-green-600' : 'text-red-600'} mt-1`}>
                      {stats.averageAmountGrowth >= 0 ? '+' : ''}{stats.averageAmountGrowth.toFixed(1)}% from last month
                    </p>
                  )}
                </div>
                <div className="bg-purple-100 p-3 rounded-full">
                  <CreditCard className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
