'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useCurrentRole } from '@/hooks/use-current-role';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { toast } from 'sonner';
import {
  UserPlus,
  Mail,
  Users,
  Activity,
  Search,
  Filter,
  Download,
} from 'lucide-react';

import UserTable from '@/components/admin/user-management/UserTable';
import UserForm, { InviteUserForm } from '@/components/admin/user-management/UserForm';
import ActivityLogs from '@/components/admin/user-management/ActivityLogs';

import {
  User,
  UserRole,
  UserFilters,
  ActivityLogFilters,
  UsersResponse,
  ActivityLogsResponse,
  InvitationsResponse,
  CreateUserResponse,
  InviteUserResponse,
  FormErrors,
  USER_ROLES,
} from '@/types/user-management';

export default function UserManagementPage() {
  const router = useRouter();
  const user = useCurrentUser();
  const role = useCurrentRole();

  // State management
  const [users, setUsers] = useState<User[]>([]);
  const [activityLogs, setActivityLogs] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  // Dialog states
  const [showUserForm, setShowUserForm] = useState(false);
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);

  // Filter states
  const [userFilters, setUserFilters] = useState<UserFilters>({
    page: 1,
    limit: 10,
    search: '',
    role: 'all',
    verification: 'all',
    sortField: 'createdAt',
    sortDirection: 'desc',
  });

  const [activityFilters, setActivityFilters] = useState<ActivityLogFilters>({
    page: 1,
    limit: 20,
    sortBy: 'timestamp',
    sortOrder: 'desc',
  });

  // Pagination states
  const [usersPagination, setUsersPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  });

  const [activityPagination, setActivityPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0,
  });

  // Statistics
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    pendingInvitations: 0,
    recentActivity: 0,
  });

  // Check permissions
  useEffect(() => {
    if (!user?.id) {
      router.push('/auth/login');
      return;
    }

    if (role !== 'ADMIN' && role !== 'SUPERADMIN' && role !== 'DEVELOPER') {
      router.push('/dashboard');
      toast.error('You do not have permission to access this page');
      return;
    }
  }, [user, role, router]);

  // Fetch users
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      Object.entries(userFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/admin/users?${params}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch users');
      }

      const data: UsersResponse = await response.json();

      setUsers(data.users);
      setUsersPagination(data.pagination);
      
      // Update stats
      setStats(prev => ({
        ...prev,
        totalUsers: data.pagination.total,
        activeUsers: data.users.filter(u => u.emailVerified).length,
      }));

    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  }, [userFilters]);

  // Fetch activity logs
  const fetchActivityLogs = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      
      Object.entries(activityFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/admin/activity-logs?${params}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch activity logs');
      }

      const data: ActivityLogsResponse = await response.json();

      setActivityLogs(data.logs);
      setActivityPagination(data.pagination);

    } catch (error) {
      console.error('Error fetching activity logs:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to fetch activity logs');
    }
  }, [activityFilters]);

  // Initial data fetch
  useEffect(() => {
    if (user?.id && (role === 'ADMIN' || role === 'SUPERADMIN' || role === 'DEVELOPER')) {
      fetchUsers();
      fetchActivityLogs();
    }
  }, [user, role, fetchUsers, fetchActivityLogs]);

  // Handle user creation
  const handleCreateUser = async (userData: any) => {
    try {
      setActionLoading(true);
      setErrors({});

      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 400 && errorData.details) {
          // Handle validation errors
          const validationErrors: FormErrors = {};
          errorData.details.split(', ').forEach((error: string) => {
            if (error.includes('Name')) validationErrors.name = error;
            if (error.includes('email')) validationErrors.email = error;
            if (error.includes('role')) validationErrors.role = error;
          });
          setErrors(validationErrors);
          return;
        }
        throw new Error(errorData.error || 'Failed to create user');
      }

      const data: CreateUserResponse = await response.json();

      toast.success(`User created successfully! Temporary password: ${data.temporaryPassword}`);
      setShowUserForm(false);
      setEditingUser(null);
      fetchUsers();

    } catch (error) {
      console.error('Error creating user:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create user');
    } finally {
      setActionLoading(false);
    }
  };

  // Handle user update
  const handleUpdateUser = async (userData: any) => {
    if (!editingUser) return;

    try {
      setActionLoading(true);
      setErrors({});

      const response = await fetch('/api/admin/users', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: editingUser.id, ...userData }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 400 && errorData.details) {
          // Handle validation errors
          const validationErrors: FormErrors = {};
          errorData.details.split(', ').forEach((error: string) => {
            if (error.includes('Name')) validationErrors.name = error;
            if (error.includes('role')) validationErrors.role = error;
          });
          setErrors(validationErrors);
          return;
        }
        throw new Error(errorData.error || 'Failed to update user');
      }

      const data = await response.json();

      toast.success('User updated successfully!');
      setShowUserForm(false);
      setEditingUser(null);
      fetchUsers();

    } catch (error) {
      console.error('Error updating user:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update user');
    } finally {
      setActionLoading(false);
    }
  };

  // Handle user deletion
  const handleDeleteUser = async (userToDelete: User) => {
    try {
      setActionLoading(true);

      const response = await fetch('/api/admin/users', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: userToDelete.id }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete user');
      }

      const data = await response.json();

      toast.success('User deleted successfully!');
      fetchUsers();

    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete user');
    } finally {
      setActionLoading(false);
    }
  };

  // Handle role change
  const handleRoleChange = async (userId: string, newRole: UserRole) => {
    try {
      setActionLoading(true);

      const response = await fetch('/api/admin/users', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, role: newRole }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update user role');
      }

      const data = await response.json();

      toast.success(`User role updated to ${newRole}!`);
      fetchUsers();

    } catch (error) {
      console.error('Error updating user role:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update user role');
    } finally {
      setActionLoading(false);
    }
  };

  // Handle user invitation
  const handleInviteUser = async (inviteData: any) => {
    try {
      setActionLoading(true);
      setErrors({});

      const response = await fetch('/api/admin/users/invite', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(inviteData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 400 && errorData.details) {
          // Handle validation errors
          const validationErrors: FormErrors = {};
          errorData.details.split(', ').forEach((error: string) => {
            if (error.includes('email')) validationErrors.email = error;
            if (error.includes('role')) validationErrors.role = error;
          });
          setErrors(validationErrors);
          return;
        }
        throw new Error(errorData.error || 'Failed to send invitation');
      }

      const data: InviteUserResponse = await response.json();

      toast.success('User invitation sent successfully!');
      setShowInviteForm(false);

    } catch (error) {
      console.error('Error sending invitation:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to send invitation');
    } finally {
      setActionLoading(false);
    }
  };

  // Handle user form submission
  const handleUserFormSubmit = (userData: any) => {
    if (editingUser) {
      handleUpdateUser(userData);
    } else {
      handleCreateUser(userData);
    }
  };

  // Handle edit user
  const handleEditUser = (userToEdit: User) => {
    setEditingUser(userToEdit);
    setShowUserForm(true);
    setErrors({});
  };

  // Handle view user details
  const handleViewUserDetails = (userToView: User) => {
    // TODO: Implement user details modal or navigate to user detail page
    toast.info('User details view coming soon!');
  };

  // Handle filter changes
  const handleUserFiltersChange = (newFilters: Partial<UserFilters>) => {
    setUserFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleActivityFiltersChange = (newFilters: ActivityLogFilters) => {
    setActivityFilters(newFilters);
  };

  if (!user?.id || (role !== 'ADMIN' && role !== 'SUPERADMIN' && role !== 'DEVELOPER')) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
        <div>
          <h1 className="text-3xl font-bold">User Management</h1>
          <p className="text-gray-500 mt-1">
            Manage users, roles, and monitor administrative activities
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-3">
          <Button onClick={() => setShowInviteForm(true)}>
            <Mail className="mr-2 h-4 w-4" />
            Invite User
          </Button>
          <Button onClick={() => setShowUserForm(true)}>
            <UserPlus className="mr-2 h-4 w-4" />
            Add User
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalUsers > 0 
                ? `${((stats.activeUsers / stats.totalUsers) * 100).toFixed(1)}%`
                : '0%'
              } verified
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Invitations</CardTitle>
            <Mail className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.pendingInvitations}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
            <Activity className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.recentActivity}</div>
            <p className="text-xs text-muted-foreground">Last 24 hours</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="users" className="space-y-6">
        <TabsList>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="activity">Activity Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          {/* User Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Filter Users</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="search">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="search"
                      placeholder="Search users..."
                      value={userFilters.search || ''}
                      onChange={(e) => handleUserFiltersChange({ search: e.target.value, page: 1 })}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role-filter">Role</Label>
                  <Select
                    value={userFilters.role || 'all'}
                    onValueChange={(value) => handleUserFiltersChange({ 
                      role: value === 'all' ? 'all' : value as UserRole, 
                      page: 1 
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All roles" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Roles</SelectItem>
                      {USER_ROLES.map((role) => (
                        <SelectItem key={role.value} value={role.value}>
                          {role.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="verification-filter">Verification</Label>
                  <Select
                    value={userFilters.verification || 'all'}
                    onValueChange={(value) => handleUserFiltersChange({ 
                      verification: value as any, 
                      page: 1 
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="verified">Verified</SelectItem>
                      <SelectItem value="unverified">Unverified</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="limit">Per Page</Label>
                  <Select
                    value={userFilters.limit?.toString() || '10'}
                    onValueChange={(value) => handleUserFiltersChange({ 
                      limit: parseInt(value), 
                      page: 1 
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Users Table */}
          <UserTable
            users={users}
            loading={loading}
            onEdit={handleEditUser}
            onDelete={handleDeleteUser}
            onViewDetails={handleViewUserDetails}
            onRoleChange={handleRoleChange}
          />

          {/* Pagination */}
          {usersPagination.totalPages > 1 && (
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-500">
                Showing {((usersPagination.page - 1) * usersPagination.limit) + 1} to{' '}
                {Math.min(usersPagination.page * usersPagination.limit, usersPagination.total)} of{' '}
                {usersPagination.total} users
              </p>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleUserFiltersChange({ page: usersPagination.page - 1 })}
                  disabled={usersPagination.page <= 1 || loading}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleUserFiltersChange({ page: usersPagination.page + 1 })}
                  disabled={usersPagination.page >= usersPagination.totalPages || loading}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="activity">
          <ActivityLogs
            logs={activityLogs}
            loading={loading}
            filters={activityFilters}
            onFiltersChange={handleActivityFiltersChange}
            onRefresh={fetchActivityLogs}
          />
        </TabsContent>
      </Tabs>

      {/* User Form Dialog */}
      <UserForm
        user={editingUser || undefined}
        open={showUserForm}
        onOpenChange={(open) => {
          setShowUserForm(open);
          if (!open) {
            setEditingUser(null);
            setErrors({});
          }
        }}
        onSubmit={handleUserFormSubmit}
        loading={actionLoading}
        errors={errors}
      />

      {/* Invite User Dialog */}
      <InviteUserForm
        open={showInviteForm}
        onOpenChange={(open) => {
          setShowInviteForm(open);
          if (!open) {
            setErrors({});
          }
        }}
        onSubmit={handleInviteUser}
        loading={actionLoading}
        errors={errors}
      />
    </div>
  );
}
