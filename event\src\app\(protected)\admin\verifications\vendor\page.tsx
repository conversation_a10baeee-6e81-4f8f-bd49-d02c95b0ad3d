'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/use-toast';
import {
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Search,
  Loader2,
  FileText,
  Building,
  Phone,
  Mail,
  MapPin,
  RefreshCw
} from 'lucide-react';
import Image from 'next/image';

interface Verification {
  id: string;
  userId: string;
  businessName: string;
  registrationNumber: string;
  taxPayerIdNumber: string;
  phoneNumber: string;
  alternativeEmail: string | null;
  physicalAddress: string;
  city: string;
  province: string;
  postalCode: string | null;
  idDocumentPath: string;
  idDocumentType: string;
  businessLicensePath: string;
  taxCertificatePath: string | null;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  createdAt: string;
  updatedAt: string;
  user: {
    name: string | null;
    email: string | null;
  };
}

export default function AdminVendorVerificationsPage() {
  const router = useRouter();
  const [verifications, setVerifications] = useState<Verification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedVerification, setSelectedVerification] = useState<Verification | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [statusFilter, setStatusFilter] = useState<'ALL' | 'PENDING' | 'APPROVED' | 'REJECTED'>('ALL');

  useEffect(() => {
    async function fetchVerifications() {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/verifications/vendor');

        if (!response.ok) {
          throw new Error('Failed to fetch verifications');
        }

        const data = await response.json();
        console.log('Vendor verifications data:', data);
        setVerifications(data);
      } catch (err) {
        console.error('Error fetching verifications:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load vendor verifications',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchVerifications();
  }, []);

  const filteredVerifications = verifications.filter(verification => {
    // Apply status filter
    if (statusFilter !== 'ALL' && verification.status !== statusFilter) {
      return false;
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        verification.businessName.toLowerCase().includes(query) ||
        verification.user.name?.toLowerCase().includes(query) ||
        verification.user.email?.toLowerCase().includes(query) ||
        verification.registrationNumber.toLowerCase().includes(query)
      );
    }

    return true;
  });

  const handleViewDetails = (verification: Verification) => {
    setSelectedVerification(verification);
    setIsDialogOpen(true);
  };

  const handleApprove = async () => {
    if (!selectedVerification) return;

    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/admin/verifications/vendor/${selectedVerification.id}/approve`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to approve verification');
      }

      // Update local state
      setVerifications(prev =>
        prev.map(v =>
          v.id === selectedVerification.id
            ? { ...v, status: 'APPROVED' }
            : v
        )
      );

      toast({
        title: 'Success',
        description: 'Vendor verification approved successfully',
        variant: 'default',
      });

      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error approving verification:', error);
      toast({
        title: 'Error',
        description: 'Failed to approve verification',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReject = async () => {
    if (!selectedVerification || !rejectionReason.trim()) return;

    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/admin/verifications/vendor/${selectedVerification.id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: rejectionReason }),
      });

      if (!response.ok) {
        throw new Error('Failed to reject verification');
      }

      // Update local state
      setVerifications(prev =>
        prev.map(v =>
          v.id === selectedVerification.id
            ? { ...v, status: 'REJECTED' }
            : v
        )
      );

      toast({
        title: 'Success',
        description: 'Vendor verification rejected',
        variant: 'default',
      });

      setIsDialogOpen(false);
      setRejectionReason('');
    } catch (error) {
      console.error('Error rejecting verification:', error);
      toast({
        title: 'Error',
        description: 'Failed to reject verification',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const refreshVerifications = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/verifications/vendor');

      if (!response.ok) {
        throw new Error('Failed to fetch verifications');
      }

      const data = await response.json();
      console.log('Refreshed vendor verifications data:', data);
      setVerifications(data);
      toast({
        title: 'Success',
        description: `Loaded ${data.length} vendor verifications`,
        variant: 'default',
      });
    } catch (err) {
      console.error('Error refreshing verifications:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      toast({
        title: 'Error',
        description: 'Failed to refresh vendor verifications',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Debug function to approve a specific verification by ID
  const approveSpecificVerification = async (id: string) => {
    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/admin/verifications/vendor/${id}/approve`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to approve verification');
      }

      toast({
        title: 'Success',
        description: `Verification ${id} approved successfully`,
        variant: 'default',
      });

      // Refresh the list
      refreshVerifications();
    } catch (error) {
      console.error('Error approving verification:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to approve verification',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Vendor Verifications</CardTitle>
            <CardDescription>
              Review and manage vendor verification requests
            </CardDescription>
          </div>
          <Button
            variant="outline"
            onClick={refreshVerifications}
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
            <div className="relative w-full md:w-1/3">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search by name, email, or business..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>

            <div className="flex gap-2">
              <Button
                variant={statusFilter === 'ALL' ? 'default' : 'outline'}
                onClick={() => setStatusFilter('ALL')}
                size="sm"
              >
                All
              </Button>
              <Button
                variant={statusFilter === 'PENDING' ? 'default' : 'outline'}
                onClick={() => setStatusFilter('PENDING')}
                size="sm"
              >
                <Clock className="mr-1 h-4 w-4" />
                Pending
              </Button>
              <Button
                variant={statusFilter === 'APPROVED' ? 'default' : 'outline'}
                onClick={() => setStatusFilter('APPROVED')}
                size="sm"
              >
                <CheckCircle className="mr-1 h-4 w-4" />
                Approved
              </Button>
              <Button
                variant={statusFilter === 'REJECTED' ? 'default' : 'outline'}
                onClick={() => setStatusFilter('REJECTED')}
                size="sm"
              >
                <XCircle className="mr-1 h-4 w-4" />
                Rejected
              </Button>

              {/* Debug button to approve a specific verification */}
              <Button
                variant="outline"
                size="sm"
                className="ml-4 bg-green-50"
                onClick={() => {
                  const id = prompt('Enter verification ID to approve:');
                  if (id) {
                    approveSpecificVerification(id);
                  }
                }}
                disabled={isSubmitting}
              >
                <CheckCircle className="mr-1 h-4 w-4 text-green-600" />
                Approve by ID
              </Button>
            </div>
          </div>

          {/* Debug info section */}
          <div className="mb-6 p-4 border border-dashed border-gray-300 rounded-md bg-gray-50">
            <h3 className="text-sm font-medium mb-2">Debug Information</h3>
            <p className="text-xs text-gray-600 mb-2">
              Total verifications: {verifications.length}
            </p>
            <p className="text-xs text-gray-600 mb-2">
              Pending verifications: {verifications.filter(v => v.status === 'PENDING').length}
            </p>
            {verifications.filter(v => v.status === 'PENDING').map((v, i) => (
              <div key={i} className="text-xs text-gray-600 mb-1">
                <span className="font-medium">Pending ID {i+1}:</span> {v.id}
                <Button
                  variant="link"
                  size="sm"
                  className="h-auto p-0 ml-2 text-green-600"
                  onClick={() => approveSpecificVerification(v.id)}
                >
                  Approve
                </Button>
              </div>
            ))}
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading verifications...</span>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              <p>{error}</p>
            </div>
          ) : filteredVerifications.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No verification requests found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Business Name</TableHead>
                    <TableHead>Vendor</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredVerifications.map((verification) => (
                    <TableRow key={verification.id}>
                      <TableCell className="font-medium">{verification.businessName}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{verification.user.name}</span>
                          <span className="text-sm text-gray-500">{verification.user.email}</span>
                        </div>
                      </TableCell>
                      <TableCell>{new Date(verification.createdAt).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <StatusBadge status={verification.status} />
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewDetails(verification)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {selectedVerification && (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Verification Details</DialogTitle>
              <DialogDescription>
                Review vendor verification information and documents
              </DialogDescription>
            </DialogHeader>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
              <div>
                <h3 className="text-lg font-medium mb-4">Business Information</h3>

                <div className="space-y-4">
                  <div className="flex items-start">
                    <Building className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <p className="font-medium">{selectedVerification.businessName}</p>
                      <p className="text-sm text-gray-500">Registration: {selectedVerification.registrationNumber}</p>
                      <p className="text-sm text-gray-500">TPIN: {selectedVerification.taxPayerIdNumber}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Phone className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Contact Information</p>
                      <p className="text-sm">{selectedVerification.phoneNumber}</p>
                      {selectedVerification.alternativeEmail && (
                        <p className="text-sm">{selectedVerification.alternativeEmail}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-start">
                    <MapPin className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Address</p>
                      <p className="text-sm">{selectedVerification.physicalAddress}</p>
                      <p className="text-sm">
                        {selectedVerification.city}, {selectedVerification.province}
                        {selectedVerification.postalCode && `, ${selectedVerification.postalCode}`}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Mail className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <p className="font-medium">Vendor Account</p>
                      <p className="text-sm">{selectedVerification.user.name}</p>
                      <p className="text-sm">{selectedVerification.user.email}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">Documents</h3>

                <div className="space-y-4">
                  <div>
                    <p className="font-medium flex items-center">
                      <FileText className="h-4 w-4 mr-1" />
                      ID Document ({selectedVerification.idDocumentType})
                    </p>
                    <div className="mt-2 border rounded-md overflow-hidden">
                      {selectedVerification.idDocumentPath.endsWith('.pdf') ? (
                        <div className="p-4 bg-gray-100 text-center">
                          <a
                            href={selectedVerification.idDocumentPath}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline flex items-center justify-center"
                          >
                            <FileText className="h-5 w-5 mr-1" />
                            View PDF Document
                          </a>
                        </div>
                      ) : (
                        <div className="relative h-40 w-full">
                          <Image
                            src={selectedVerification.idDocumentPath}
                            alt="ID Document"
                            fill
                            style={{ objectFit: 'contain' }}
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <p className="font-medium flex items-center">
                      <FileText className="h-4 w-4 mr-1" />
                      Business License
                    </p>
                    <div className="mt-2 border rounded-md overflow-hidden">
                      {selectedVerification.businessLicensePath.endsWith('.pdf') ? (
                        <div className="p-4 bg-gray-100 text-center">
                          <a
                            href={selectedVerification.businessLicensePath}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline flex items-center justify-center"
                          >
                            <FileText className="h-5 w-5 mr-1" />
                            View PDF Document
                          </a>
                        </div>
                      ) : (
                        <div className="relative h-40 w-full">
                          <Image
                            src={selectedVerification.businessLicensePath}
                            alt="Business License"
                            fill
                            style={{ objectFit: 'contain' }}
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  {selectedVerification.taxCertificatePath && (
                    <div>
                      <p className="font-medium flex items-center">
                        <FileText className="h-4 w-4 mr-1" />
                        Tax Certificate
                      </p>
                      <div className="mt-2 border rounded-md overflow-hidden">
                        {selectedVerification.taxCertificatePath.endsWith('.pdf') ? (
                          <div className="p-4 bg-gray-100 text-center">
                            <a
                              href={selectedVerification.taxCertificatePath}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline flex items-center justify-center"
                            >
                              <FileText className="h-5 w-5 mr-1" />
                              View PDF Document
                            </a>
                          </div>
                        ) : (
                          <div className="relative h-40 w-full">
                            <Image
                              src={selectedVerification.taxCertificatePath}
                              alt="Tax Certificate"
                              fill
                              style={{ objectFit: 'contain' }}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {selectedVerification.status === 'PENDING' && (
              <>
                <div className="mt-4">
                  <h3 className="text-lg font-medium mb-2">Review Decision</h3>
                  <Textarea
                    placeholder="Enter reason for rejection (required for rejection)"
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>

                <DialogFooter className="mt-6 flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <div className="flex gap-2">
                    <Button
                      variant="destructive"
                      onClick={handleReject}
                      disabled={isSubmitting || !rejectionReason.trim()}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Rejecting...
                        </>
                      ) : (
                        <>
                          <XCircle className="mr-2 h-4 w-4" />
                          Reject
                        </>
                      )}
                    </Button>
                    <Button
                      variant="default"
                      onClick={handleApprove}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Approving...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Approve
                        </>
                      )}
                    </Button>
                  </div>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

function StatusBadge({ status }: { status: 'PENDING' | 'APPROVED' | 'REJECTED' }) {
  switch (status) {
    case 'PENDING':
      return (
        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
          <Clock className="h-3 w-3 mr-1" />
          Pending
        </Badge>
      );
    case 'APPROVED':
      return (
        <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
          <CheckCircle className="h-3 w-3 mr-1" />
          Approved
        </Badge>
      );
    case 'REJECTED':
      return (
        <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
          <XCircle className="h-3 w-3 mr-1" />
          Rejected
        </Badge>
      );
    default:
      return null;
  }
}
