'use client'

import UserInfo from "@/components/user-info"
import { useCurrentUser } from "@/hooks/use-current-user";
import { User } from "@prisma/client";

export default function ServerPage() {
  const user = useCurrentUser() as ({ role: "ADMIN" | "USER" | "ORGANIZER" | "VENDOR"; isTwoFactorEnabled: boolean; isOAuth: boolean; } & User & { subscriptionTier: string; accountBalance: number; }) | undefined

  return (
    <div>
      <UserInfo user={user} label="Client component" />
    </div>
  )
}
