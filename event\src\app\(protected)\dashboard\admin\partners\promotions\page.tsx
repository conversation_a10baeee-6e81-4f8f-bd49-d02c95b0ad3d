'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useCurrentRole } from '@/hooks/use-current-role';
import {
  Hotel, Utensils, Wine, Music, CheckCircle, XCircle,
  ArrowLeft, Eye, Calendar, Clock, AlertTriangle, Tag, MoreHorizontal
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Define Partner interface
interface Partner {
  id: string;
  businessName: string;
  partnerType: 'HOTEL' | 'RESTAURANT' | 'BAR' | 'NIGHTCLUB' | string;
  logo?: string;
}

// Define Promotion interface
interface Promotion {
  id: string;
  partnerId: string;
  eventId?: string;
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  discountValue?: number;
  discountType?: string;
  promoCode?: string;
  isActive: boolean;
  maxUses?: number;
  currentUses: number;
  imageUrl?: string;
  createdAt: string;
  updatedAt: string;
  partner: Partner;
  event?: {
    id: string;
    title: string;
  };
}

export default function AdminPartnerPromotionsPage() {
  const router = useRouter();
  const role = useCurrentRole();
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [filteredPromotions, setFilteredPromotions] = useState<Promotion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('active');

  // Fetch promotions
  useEffect(() => {
    const fetchPromotions = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/admin/partners/promotions');

        if (!response.ok) {
          throw new Error('Failed to fetch promotions');
        }

        const data = await response.json();
        setPromotions(data.promotions || []);
      } catch (error) {
        console.error('Error fetching promotions:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPromotions();
  }, []);

  // Filter promotions based on active tab
  useEffect(() => {
    const now = new Date();

    if (activeTab === 'active') {
      setFilteredPromotions(promotions.filter(promo =>
        promo.isActive &&
        new Date(promo.endDate) >= now
      ));
    } else if (activeTab === 'upcoming') {
      setFilteredPromotions(promotions.filter(promo =>
        promo.isActive &&
        new Date(promo.startDate) > now
      ));
    } else if (activeTab === 'expired') {
      setFilteredPromotions(promotions.filter(promo =>
        !promo.isActive ||
        new Date(promo.endDate) < now
      ));
    } else {
      setFilteredPromotions(promotions);
    }
  }, [promotions, activeTab]);

  // Get partner type icon
  const getPartnerTypeIcon = (partnerType: string) => {
    switch (partnerType) {
      case 'HOTEL':
        return <Hotel className="h-4 w-4" />;
      case 'RESTAURANT':
        return <Utensils className="h-4 w-4" />;
      case 'BAR':
        return <Wine className="h-4 w-4" />;
      case 'NIGHTCLUB':
        return <Music className="h-4 w-4" />;
      default:
        return <Hotel className="h-4 w-4" />;
    }
  };

  // Format date range
  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`;
  };

  // Check if promotion is active
  const isPromotionActive = (promotion: Promotion) => {
    const now = new Date();
    const startDate = new Date(promotion.startDate);
    const endDate = new Date(promotion.endDate);

    return promotion.isActive && startDate <= now && endDate >= now;
  };

  // Get promotion status badge
  const getPromotionStatusBadge = (promotion: Promotion) => {
    const now = new Date();
    const startDate = new Date(promotion.startDate);
    const endDate = new Date(promotion.endDate);

    if (!promotion.isActive) {
      return <Badge variant="outline" className="text-gray-500 border-gray-500">Inactive</Badge>;
    }

    if (startDate > now) {
      return <Badge variant="outline" className="text-blue-500 border-blue-500">Upcoming</Badge>;
    }

    if (endDate < now) {
      return <Badge variant="outline" className="text-red-500 border-red-500">Expired</Badge>;
    }

    return <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>;
  };

  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    router.push('/dashboard');
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <Button variant="outline" asChild className="mb-4">
          <Link href="/dashboard/admin/partners">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Partners
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Partner Promotions</h1>
        <p className="text-gray-500 mt-1">
          Manage promotions offered by partners
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
        <TabsList className="mb-4">
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
          <TabsTrigger value="expired">Expired</TabsTrigger>
          <TabsTrigger value="all">All</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>
                {activeTab === 'active' ? 'Active Promotions' :
                 activeTab === 'upcoming' ? 'Upcoming Promotions' :
                 activeTab === 'expired' ? 'Expired Promotions' : 'All Promotions'}
              </CardTitle>
              <CardDescription>
                {filteredPromotions.length} {filteredPromotions.length === 1 ? 'promotion' : 'promotions'} found
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center space-x-4 p-4 border rounded-md">
                      <Skeleton className="h-12 w-12 rounded-full" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-1/3" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : filteredPromotions.length === 0 ? (
                <div className="text-center py-12">
                  <AlertTriangle className="mx-auto h-12 w-12 text-amber-500 mb-4" />
                  <h2 className="text-2xl font-bold mb-2">No Promotions Found</h2>
                  <p className="text-gray-500 mb-4">
                    {activeTab === 'active'
                      ? 'There are no active promotions at the moment.'
                      : activeTab === 'upcoming'
                        ? 'There are no upcoming promotions scheduled.'
                        : activeTab === 'expired'
                          ? 'There are no expired promotions.'
                          : 'There are no promotions in the system.'}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredPromotions.map((promotion) => (
                    <div key={promotion.id} className="p-4 border rounded-md hover:bg-gray-50">
                      <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                        <div className="flex items-center space-x-4">
                          <Avatar>
                            {promotion.partner.logo ? (
                              <AvatarImage src={promotion.partner.logo} alt={promotion.partner.businessName} />
                            ) : (
                              <AvatarFallback>
                                {promotion.partner.businessName.charAt(0)}
                              </AvatarFallback>
                            )}
                          </Avatar>
                          <div>
                            <div className="font-medium">{promotion.title}</div>
                            <div className="text-sm text-gray-500 flex items-center">
                              {getPartnerTypeIcon(promotion.partner.partnerType)}
                              <span className="ml-1">{promotion.partner.businessName}</span>
                              {promotion.event && (
                                <>
                                  <span className="mx-2">•</span>
                                  <span>Event: {promotion.event.title}</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 mt-4 md:mt-0">
                          {getPromotionStatusBadge(promotion)}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem asChild>
                                <Link href={`/dashboard/admin/partners/${promotion.partnerId}`}>
                                  <Eye className="h-4 w-4 mr-2" />
                                  View Partner
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link href={`/dashboard/admin/partners/promotions/${promotion.id}`}>
                                  <Tag className="h-4 w-4 mr-2" />
                                  View Promotion
                                </Link>
                              </DropdownMenuItem>
                              {promotion.event && (
                                <DropdownMenuItem asChild>
                                  <Link href={`/dashboard/admin/events/${promotion.eventId}`}>
                                    <Calendar className="h-4 w-4 mr-2" />
                                    View Event
                                  </Link>
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">{promotion.description}</p>
                      <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          <span>{formatDateRange(promotion.startDate, promotion.endDate)}</span>
                        </div>
                        {promotion.promoCode && (
                          <div className="flex items-center">
                            <Tag className="h-4 w-4 mr-1" />
                            <span>Code: {promotion.promoCode}</span>
                          </div>
                        )}
                        {promotion.discountValue && promotion.discountType && (
                          <div className="flex items-center">
                            <span>Discount: {promotion.discountValue}{promotion.discountType === 'percentage' ? '%' : ' ZMW'}</span>
                          </div>
                        )}
                        <div className="flex items-center">
                          <span>Uses: {promotion.currentUses}{promotion.maxUses ? `/${promotion.maxUses}` : ''}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
