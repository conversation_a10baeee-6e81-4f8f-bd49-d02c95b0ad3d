'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Calendar, Clock, MapPin, Star, AlertCircle, Edit, Trash, Share, BarChart3, Ticket } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { SimpleFeatureButton } from '@/components/organizer/simple-feature-button';
import { FeaturingAnalytics } from '@/components/organizer/featuring-analytics';

export default function EventDetailPage() {
  const params = useParams();
  const router = useRouter();
  const eventId = params?.id as string;

  const [event, setEvent] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEvent = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/events/${eventId}`);

        if (!response.ok) {
          throw new Error('Failed to fetch event details');
        }

        const data = await response.json();
        setEvent(data.event);
      } catch (err) {
        console.error('Error fetching event:', err);
        setError('Unable to load event details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchEvent();
  }, [eventId]);

  if (loading) {
    return (
      <div className="container mx-auto py-4">
        <Skeleton className="h-10 w-3/4 max-w-md mb-4" />
        <Skeleton className="h-6 w-1/2 max-w-sm mb-8" />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Skeleton className="h-96 w-full mb-6" />
            <Skeleton className="h-64 w-full" />
          </div>
          <div>
            <Skeleton className="h-64 w-full mb-6" />
            <Skeleton className="h-40 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="container mx-auto py-4">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Event Not Found</AlertTitle>
          <AlertDescription>The event you&apos;re looking for doesn&apos;t exist or has been removed.</AlertDescription>
        </Alert>
      </div>
    );
  }

  // Check if the event is featured
  const isFeatured = event.metadata && event.metadata.isFeatured === 'true';
  const featuredUntil = event.metadata && event.metadata.featuredUntil ? new Date(event.metadata.featuredUntil) : null;

  return (
    <div className="container mx-auto py-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold mb-2">{event.title}</h1>
            <div className="flex items-center gap-2">
              <Badge>{event.category}</Badge>
              <Badge variant="outline">{event.eventType}</Badge>
              {isFeatured && (
                <Badge className="bg-yellow-500 text-white flex items-center gap-1">
                  <Star className="h-3 w-3 fill-white" />
                  Featured
                </Badge>
              )}
            </div>
          </div>

          <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
            <Link href={`/dashboard/events/${eventId}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </Link>

            <SimpleFeatureButton
              eventId={eventId}
              size="sm"
              className={isFeatured ? "bg-yellow-600 hover:bg-yellow-700 text-white" : "bg-yellow-500 hover:bg-yellow-600 text-white"}
            />
          </div>
        </div>

        <Tabs defaultValue="overview" className="mb-6">
          <TabsList className="grid w-full max-w-md grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="tickets">Tickets</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="featuring">Featuring</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
            {/* Event Image */}
            <Card className="overflow-hidden">
              <div className="relative h-80">
                {event.imageUrl ? (
                  <Image
                    src={event.imageUrl}
                    alt={event.title}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gray-100">
                    <Calendar className="h-16 w-16 text-gray-400" />
                  </div>
                )}

                {isFeatured && (
                  <div className="absolute top-4 right-4">
                    <div className="bg-yellow-500 text-white px-4 py-2 rounded-md shadow-md flex items-center gap-2">
                      <Star className="h-5 w-5 fill-white" />
                      <div>
                        <div className="text-sm font-medium">Featured Event</div>
                        {featuredUntil && (
                          <div className="text-xs">Until {formatDate(featuredUntil)}</div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </Card>

            {/* Event Details */}
            <Card>
              <CardHeader>
                <CardTitle>Event Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center text-gray-700">
                      <Calendar className="h-5 w-5 mr-2 text-primary" />
                      <span className="font-medium">Date:</span>
                      <span className="ml-2">{formatDate(event.startDate)}</span>
                    </div>

                    <div className="flex items-center text-gray-700">
                      <Clock className="h-5 w-5 mr-2 text-primary" />
                      <span className="font-medium">Time:</span>
                      <span className="ml-2">{event.startTime} - {event.endTime}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center text-gray-700">
                      <MapPin className="h-5 w-5 mr-2 text-primary" />
                      <span className="font-medium">Venue:</span>
                      <span className="ml-2">{event.venue}</span>
                    </div>

                    <div className="flex items-center text-gray-700">
                      <MapPin className="h-5 w-5 mr-2 text-primary" />
                      <span className="font-medium">Location:</span>
                      <span className="ml-2">{event.location}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Description</h3>
                  <p className="text-gray-700">{event.description}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            {/* Event Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Event Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href={`/dashboard/events/${eventId}/tickets`}>
                  <Button variant="outline" className="w-full justify-start">
                    <Ticket className="mr-2 h-4 w-4" />
                    Manage Tickets
                  </Button>
                </Link>

                <Link href={`/dashboard/events/${eventId}/analytics`}>
                  <Button variant="outline" className="w-full justify-start">
                    <BarChart3 className="mr-2 h-4 w-4" />
                    View Analytics
                  </Button>
                </Link>

                <Link href={`/events/${eventId}`}>
                  <Button variant="outline" className="w-full justify-start">
                    <Share className="mr-2 h-4 w-4" />
                    View Public Page
                  </Button>
                </Link>

                <Button variant="destructive" className="w-full justify-start">
                  <Trash className="mr-2 h-4 w-4" />
                  Delete Event
                </Button>
              </CardContent>
            </Card>

            {/* Promotion Card */}
            <Card className={isFeatured ? "border-yellow-200 bg-yellow-50" : ""}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className={`h-5 w-5 ${isFeatured ? "text-yellow-500 fill-yellow-500" : ""}`} />
                  Event Promotion
                </CardTitle>
                <CardDescription>
                  {isFeatured
                    ? "Your event is currently featured and receiving extra visibility"
                    : "Promote your event to increase visibility and attract more attendees"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isFeatured ? (
                  <div className="space-y-4">
                    <div className="bg-white p-3 rounded-md border border-yellow-100">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs font-medium text-gray-700">Featured Status</span>
                        <span className="text-xs font-medium text-yellow-700">
                          {featuredUntil ? `Until ${formatDate(featuredUntil)}` : 'Active'}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Promotion Type:</span>
                      <span className="font-medium">Featured Event</span>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Visibility Boost:</span>
                      <span className="font-medium">High</span>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <div className="bg-yellow-50 p-3 rounded-full inline-flex mb-4">
                      <Star className="h-8 w-8 text-yellow-500" />
                    </div>
                    <p className="text-gray-600 mb-4">
                      Your event is not currently featured. Promote it to increase visibility!
                    </p>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <SimpleFeatureButton
                  eventId={eventId}
                  className={`w-full ${isFeatured ? "bg-yellow-600 hover:bg-yellow-700" : "bg-yellow-500 hover:bg-yellow-600"} text-white`}
                />
              </CardFooter>
            </Card>
          </div>
        </div>
          </TabsContent>

          <TabsContent value="tickets" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Ticket Management</CardTitle>
                <CardDescription>Manage your event tickets and sales</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Ticket className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">Ticket Management</h3>
                  <p className="text-gray-500 mb-4">Create and manage tickets for your event</p>
                  <Link href={`/dashboard/events/${eventId}/tickets`}>
                    <Button>
                      <Ticket className="mr-2 h-4 w-4" />
                      Manage Tickets
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Event Analytics</CardTitle>
                <CardDescription>Track your event performance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">Event Analytics</h3>
                  <p className="text-gray-500 mb-4">View detailed analytics for your event</p>
                  <Link href={`/dashboard/events/${eventId}/analytics`}>
                    <Button>
                      <BarChart3 className="mr-2 h-4 w-4" />
                      View Analytics
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="featuring" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Featuring Analytics</CardTitle>
                <CardDescription>Track the performance of your featured event</CardDescription>
              </CardHeader>
              <CardContent>
                {isFeatured ? (
                  <FeaturingAnalytics eventId={eventId} />
                ) : (
                  <div className="text-center py-8">
                    <Star className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">Event Not Featured</h3>
                    <p className="text-gray-500 mb-4">Feature your event to access analytics and increase visibility</p>
                    <SimpleFeatureButton
                      eventId={eventId}
                      className="bg-yellow-500 hover:bg-yellow-600 text-white"
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
  );
}
