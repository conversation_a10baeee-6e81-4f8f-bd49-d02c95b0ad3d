'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function EditEventRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the events page
    router.push('/dashboard/organizer/events/myEvents');
  }, [router]);

  return (
    <div className="flex justify-center items-center h-screen">
      <p className="text-gray-500">Redirecting to events page...</p>
    </div>
  );
}
