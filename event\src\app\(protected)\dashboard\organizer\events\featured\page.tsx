'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Star, ArrowRight, Calendar, Users, DollarSign, Clock, MapPin } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { formatDate } from '@/lib/utils';

interface FeaturedEvent {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  venue: string;
  location: string;
  imageUrl?: string;
  featuredUntil: string;
  promotionId?: string;
  promotionType: string;
}

export default function FeaturedEventsPage() {
  const [featuredEvents, setFeaturedEvents] = useState<FeaturedEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeaturedEvents = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/dashboard/featured-events');

        if (!response.ok) {
          throw new Error('Failed to fetch featured events');
        }

        const data = await response.json();
        setFeaturedEvents(data.events || []);
      } catch (err) {
        console.error('Error fetching featured events:', err);
        setError('Unable to load featured events');
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedEvents();
  }, []);

  // Helper function to calculate time remaining
  const timeRemaining = (endDate: string) => {
    const now = new Date();
    const end = new Date(endDate);
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return 'Expired';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) return `${days} day${days > 1 ? 's' : ''} left`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} left`;
    return 'Less than 1 hour left';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Featured Events</h1>
        <p className="text-gray-600">Promote your events to reach a wider audience and increase ticket sales.</p>
      </div>

      {/* Feature Your Events Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-500" />
              Feature Your Events
            </CardTitle>
            <CardDescription>
              Boost your event visibility and reach more potential attendees
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Increased visibility in search results</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Featured placement on homepage</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Priority in category listings</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">Social media promotion</span>
              </div>
            </div>
            <Button asChild className="w-full">
              <Link href="/dashboard/featured">
                Promote Your Events
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Promotion Benefits</CardTitle>
            <CardDescription>
              See how featuring your events can impact your success
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="flex items-center justify-center mb-2">
                  <Users className="h-8 w-8 text-blue-500" />
                </div>
                <div className="text-2xl font-bold text-blue-600">3x</div>
                <div className="text-sm text-gray-600">More Views</div>
              </div>
              <div>
                <div className="flex items-center justify-center mb-2">
                  <Calendar className="h-8 w-8 text-green-500" />
                </div>
                <div className="text-2xl font-bold text-green-600">2.5x</div>
                <div className="text-sm text-gray-600">Ticket Sales</div>
              </div>
              <div>
                <div className="flex items-center justify-center mb-2">
                  <DollarSign className="h-8 w-8 text-purple-500" />
                </div>
                <div className="text-2xl font-bold text-purple-600">40%</div>
                <div className="text-sm text-gray-600">More Revenue</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Current Featured Events */}
      <Card>
        <CardHeader>
          <CardTitle>Your Featured Events</CardTitle>
          <CardDescription>
            Events you have currently promoted
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 w-full" />
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <Star className="h-12 w-12 text-red-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Events</h3>
              <p className="text-gray-500 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          ) : featuredEvents.length === 0 ? (
            <div className="text-center py-8">
              <Star className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Featured Events</h3>
              <p className="text-gray-500 mb-4">
                You haven't featured any events yet. Start promoting your events to reach more attendees.
              </p>
              <Button asChild>
                <Link href="/dashboard/featured">
                  Feature an Event
                </Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {featuredEvents.map((event) => {
                const now = new Date();
                const featuredUntil = new Date(event.featuredUntil);
                const startDate = new Date(event.startDate);

                // Calculate percentage of promotion time remaining
                const totalDuration = featuredUntil.getTime() - startDate.getTime();
                const elapsed = now.getTime() - startDate.getTime();
                const percentComplete = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));

                // Calculate time remaining
                const remaining = timeRemaining(event.featuredUntil);

                return (
                  <Card key={event.id} className="overflow-hidden border-yellow-100 bg-yellow-50/30">
                    <div className="relative h-40">
                      {event.imageUrl ? (
                        <Image
                          src={event.imageUrl}
                          alt={event.title}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-yellow-100">
                          <Calendar className="h-12 w-12 text-yellow-500" />
                        </div>
                      )}
                      <div className="absolute top-2 right-2">
                        <Badge className="bg-yellow-500 text-white">
                          {event.promotionType}
                        </Badge>
                      </div>
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-semibold text-lg mb-2 line-clamp-1">{event.title}</h3>
                      <div className="space-y-2 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(event.startDate)}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          <span className="line-clamp-1">{event.venue}, {event.location}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          <span className={remaining === 'Expired' ? 'text-red-500' : 'text-green-600'}>
                            {remaining}
                          </span>
                        </div>
                      </div>

                      {/* Progress bar */}
                      <div className="mt-3">
                        <div className="flex justify-between text-xs text-gray-500 mb-1">
                          <span>Promotion Progress</span>
                          <span>{Math.round(percentComplete)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${percentComplete}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="mt-4 flex gap-2">
                        <Button asChild size="sm" variant="outline" className="flex-1">
                          <Link href={`/dashboard/events/${event.id}`}>
                            View Details
                          </Link>
                        </Button>
                        <Button asChild size="sm" className="flex-1 bg-yellow-500 hover:bg-yellow-600">
                          <Link href={`/events/${event.id}`}>
                            View Public
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
