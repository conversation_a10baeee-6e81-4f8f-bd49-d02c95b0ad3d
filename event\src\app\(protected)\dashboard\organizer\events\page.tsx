'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function EventsPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the My Events page since that's the main events listing
    router.replace('/dashboard/organizer/events/myEvents');
  }, [router]);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    </div>
  );
}
