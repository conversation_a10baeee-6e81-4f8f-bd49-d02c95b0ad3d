'use client';

import React, { useState, useEffect } from 'react';
import { RoleGate } from '@/components/auth/role-gate';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DollarSign, CreditCard, TrendingUp, Download, Calendar, Filter, ArrowUpRight, Wallet, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { TransactionsList } from '@/components/finance/transactions-list';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';

export default function OrganizerFinancePage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [period, setPeriod] = useState('monthly');
  const [year, setYear] = useState('2023');
  const [loading, setLoading] = useState(true);
  const [financeData, setFinanceData] = useState({
    totalRevenue: 0,
    platformFees: 0,
    netEarnings: 0,
    availableBalance: 0,
    pendingBalance: 0,
    transactionCount: 0,
    averageTicketPrice: 0,
    conversionRate: 0,
    monthlyRevenue: [],
    topEvents: [],
    recentWithdrawals: []
  });
  const { toast } = useToast();

  useEffect(() => {
    const fetchFinanceData = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/organizer/finance/dashboard');

        if (!response.ok) {
          throw new Error('Failed to fetch finance data');
        }

        const data = await response.json();
        setFinanceData(data);
      } catch (error) {
        console.error('Error fetching finance data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load financial data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchFinanceData();
  }, [toast]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'FAILED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold mb-2">Financial Management</h1>
            <p className="text-gray-500 dark:text-gray-400">
              Monitor your revenue, transactions, and withdrawals
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex gap-3">
            <Button variant="outline">
              <Calendar className="mr-2 h-4 w-4" />
              Select Date Range
            </Button>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export Data
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-t-blue-500 border-b-blue-700 border-l-blue-500 border-r-blue-700 rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-500 dark:text-gray-400">Loading financial data...</p>
            </div>
          </div>
        ) : (

        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="withdrawals">Withdrawals</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Balance Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="md:col-span-2 bg-gradient-to-br from-blue-500 to-blue-600 text-white">
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row justify-between gap-6">
                    <div>
                      <h3 className="text-lg font-medium text-blue-100 mb-1">Available Balance</h3>
                      <p className="text-3xl font-bold">{formatCurrency(financeData.availableBalance)}</p>
                      <Button variant="secondary" className="mt-4 bg-white text-blue-600 hover:bg-blue-50" asChild>
                        <Link href="/dashboard/organizer/finance/withdrawals/new">
                          Withdraw Funds
                          <Wallet className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-blue-100 mb-1">Pending Balance</h3>
                      <p className="text-3xl font-bold">{formatCurrency(financeData.pendingBalance)}</p>
                      <p className="text-sm text-blue-100 mt-1">Available in 7-14 days</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col h-full justify-between">
                    <div>
                      <h3 className="text-lg font-medium text-gray-500 dark:text-gray-400 mb-1">Recent Withdrawals</h3>
                      <div className="space-y-4 mt-4">
                        {(financeData.recentWithdrawals as any[]).map((withdrawal: any, index) => (
                          <div key={index} className="flex justify-between items-center">
                            <div>
                              <p className="font-medium">{formatCurrency(withdrawal.amount)}</p>
                              <p className="text-sm text-gray-500 dark:text-gray-400">{formatDate(withdrawal.date)}</p>
                            </div>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(withdrawal.status)}`}>
                              {withdrawal.status}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="space-y-2 mt-4">
                      <Button variant="outline" className="w-full" asChild>
                        <Link href="/dashboard/organizer/finance/withdrawals">
                          View All Withdrawals
                          <ChevronRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="outline" className="w-full" asChild>
                        <Link href="/dashboard/organizer/finance/payouts">
                          View Event Payouts
                          <ChevronRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Revenue Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Revenue</p>
                      <h3 className="text-2xl font-bold mt-1">{formatCurrency(financeData.totalRevenue)}</h3>
                      <p className="text-sm text-green-600 mt-1">+12.5% from last month</p>
                    </div>
                    <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-full">
                      <DollarSign className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Platform Fees</p>
                      <h3 className="text-2xl font-bold mt-1">{formatCurrency(financeData.platformFees)}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">10% of total revenue</p>
                    </div>
                    <div className="bg-purple-100 dark:bg-purple-900 p-3 rounded-full">
                      <CreditCard className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Net Earnings</p>
                      <h3 className="text-2xl font-bold mt-1">{formatCurrency(financeData.netEarnings)}</h3>
                      <p className="text-sm text-green-600 mt-1">90% of total revenue</p>
                    </div>
                    <div className="bg-green-100 dark:bg-green-900 p-3 rounded-full">
                      <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Revenue Chart */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Revenue Trends</CardTitle>
                    <CardDescription>
                      {period === 'monthly' ? 'Monthly' : period === 'quarterly' ? 'Quarterly' : 'Yearly'} revenue breakdown for {year}
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Select value={period} onValueChange={setPeriod}>
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="Select period" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select value={year} onValueChange={setYear}>
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="Select year" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="2023">2023</SelectItem>
                        <SelectItem value="2022">2022</SelectItem>
                        <SelectItem value="2021">2021</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  {period === 'monthly' && (
                    <div className="h-full flex items-end justify-between gap-2">
                      {(financeData.monthlyRevenue as any[]).map((item: any, index) => (
                        <div key={index} className="flex flex-col items-center">
                          <div className="flex flex-col items-center">
                            <div className="relative w-16">
                              <div
                                className="absolute bottom-0 w-full bg-blue-500 rounded-t-sm"
                                style={{ height: `${(item.revenue / 16000) * 100}%` }}
                              ></div>
                              <div
                                className="absolute bottom-0 w-full bg-blue-200 dark:bg-blue-700 rounded-t-sm"
                                style={{ height: `${(item.fees / 16000) * 100}%` }}
                              ></div>
                            </div>
                            <span className="text-xs mt-2">{item.month}</span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">${(item.revenue / 1000).toFixed(1)}k</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Top Events */}
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Events</CardTitle>
                <CardDescription>Events with highest revenue</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {(financeData.topEvents as any[]).slice(0, 5).map((event: any, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full text-blue-600 dark:text-blue-400 font-medium">
                        {index + 1}
                      </div>
                      <div className="ml-3 flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{event.title}</p>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium">{formatCurrency(event.revenue)}</p>
                      </div>
                      <div className="ml-6 w-1/3">
                        <Progress value={(event.revenue / financeData.totalRevenue) * 100} className="h-2" />
                      </div>
                      <div className="ml-3 w-12 text-right">
                        <p className="text-sm text-gray-500">{Math.round((event.revenue / financeData.totalRevenue) * 100)}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Transactions */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>Latest financial activity</CardDescription>
              </CardHeader>
              <CardContent>
                <TransactionsList limit={5} showViewAll={true} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="transactions">
            <Card>
              <CardHeader>
                <CardTitle>All Transactions</CardTitle>
                <CardDescription>Complete transaction history</CardDescription>
              </CardHeader>
              <CardContent>
                <TransactionsList />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="withdrawals">
            <Card>
              <CardHeader>
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                  <div>
                    <CardTitle>Withdrawal History</CardTitle>
                    <CardDescription>Manage your withdrawals</CardDescription>
                  </div>
                  <Button className="mt-4 md:mt-0" asChild>
                    <Link href="/dashboard/organizer/finance/withdrawals/new">
                      <Wallet className="mr-2 h-4 w-4" />
                      Withdraw Funds
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b dark:border-gray-700">
                        <th className="text-left py-3 px-4">Date</th>
                        <th className="text-left py-3 px-4">Amount</th>
                        <th className="text-left py-3 px-4">Status</th>
                        <th className="text-left py-3 px-4">Reference</th>
                      </tr>
                    </thead>
                    <tbody>
                      {(financeData.recentWithdrawals as any[]).map((withdrawal: any, index) => (
                        <tr key={index} className="border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="py-3 px-4">{formatDate(withdrawal.date)}</td>
                          <td className="py-3 px-4 font-medium">{formatCurrency(withdrawal.amount)}</td>
                          <td className="py-3 px-4">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(withdrawal.status)}`}>
                              {withdrawal.status}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-gray-500 dark:text-gray-400">#{withdrawal.id}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="mt-6 text-center">
                  <p className="text-gray-500 dark:text-gray-400">
                    Withdrawals are processed within 3-5 business days.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        )}
      </div>
    </RoleGate>
  );
}
