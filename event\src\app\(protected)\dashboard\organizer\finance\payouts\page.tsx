import { redirect } from 'next/navigation';
import Link from 'next/link';
import { getSession } from '@/auth';
import { db } from '@/lib/prisma';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, DollarSign, Calendar, ArrowUpRight } from 'lucide-react';
import { formatCurrency } from '@/lib/utils/format';
import { formatDistanceToNow } from 'date-fns';
import { UserSession } from '@/types/session';

export default async function OrganizerPayoutsPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only organizers can access this page
  if (session.user.role !== 'ORGANIZER') {
    redirect('/dashboard');
  }

  // Get user's payouts
  const payouts = await db.eventPayout.findMany({
    where: { userId: session.user.id },
    include: {
      event: {
        select: {
          id: true,
          title: true,
          endDate: true,
        },
      },
    },
    orderBy: { requestDate: 'desc' },
  });

  // Get user's account balance
  const user = await db.user.findUnique({
    where: { id: session.user.id },
    select: { accountBalance: true },
  });

  // Calculate summary statistics
  const pendingAmount = payouts
    .filter(p => p.status === 'Pending' || p.status === 'Processing')
    .reduce((sum, p) => sum + p.amount, 0);
  
  const completedAmount = payouts
    .filter(p => p.status === 'Completed')
    .reduce((sum, p) => sum + p.amount, 0);

  // Group payouts by status
  const pendingPayouts = payouts.filter(p => p.status === 'Pending');
  const processingPayouts = payouts.filter(p => p.status === 'Processing');
  const completedPayouts = payouts.filter(p => p.status === 'Completed');
  const failedPayouts = payouts.filter(p => p.status === 'Failed' || p.status === 'Cancelled');

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Pending':
        return 'outline';
      case 'Processing':
        return 'default';
      case 'Completed':
        return 'success';
      case 'Failed':
        return 'destructive';
      case 'Cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild>
          <Link href="/dashboard/organizer/finance">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Finance
          </Link>
        </Button>
      </div>

      <div className="mb-6">
        <h1 className="text-3xl font-bold">Event Payouts</h1>
        <p className="text-gray-500 mt-1">
          Manage and track your event payouts
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Available Balance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 text-primary mr-2" />
              <span className="text-2xl font-bold">{formatCurrency(user?.accountBalance || 0)}</span>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-4 w-full"
              asChild
            >
              <Link href="/dashboard/organizer/wallet/withdraw">
                Withdraw Funds
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Pending Payouts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Calendar className="h-5 w-5 text-yellow-500 mr-2" />
              <span className="text-2xl font-bold">{formatCurrency(pendingAmount)}</span>
            </div>
            <p className="text-sm text-gray-500 mt-2">
              {pendingPayouts.length + processingPayouts.length} pending payout requests
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Total Paid Out</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <DollarSign className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-2xl font-bold">{formatCurrency(completedAmount)}</span>
            </div>
            <p className="text-sm text-gray-500 mt-2">
              {completedPayouts.length} completed payouts
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Payouts</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="processing">Processing</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
          <TabsTrigger value="failed">Failed/Cancelled</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <PayoutsList payouts={payouts} getStatusBadgeVariant={getStatusBadgeVariant} />
        </TabsContent>

        <TabsContent value="pending">
          <PayoutsList payouts={pendingPayouts} getStatusBadgeVariant={getStatusBadgeVariant} />
        </TabsContent>

        <TabsContent value="processing">
          <PayoutsList payouts={processingPayouts} getStatusBadgeVariant={getStatusBadgeVariant} />
        </TabsContent>

        <TabsContent value="completed">
          <PayoutsList payouts={completedPayouts} getStatusBadgeVariant={getStatusBadgeVariant} />
        </TabsContent>

        <TabsContent value="failed">
          <PayoutsList payouts={failedPayouts} getStatusBadgeVariant={getStatusBadgeVariant} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

function PayoutsList({ 
  payouts, 
  getStatusBadgeVariant 
}: { 
  payouts: any[],
  getStatusBadgeVariant: (status: string) => string
}) {
  if (payouts.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center p-6">
          <DollarSign className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium mb-2">No Payouts Found</h3>
          <p className="text-gray-500 text-center mb-4">
            You don't have any payouts in this category.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {payouts.map((payout) => (
        <Card key={payout.id}>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="font-medium">{payout.event.title}</h3>
                  <Badge variant={getStatusBadgeVariant(payout.status) as "default" | "secondary" | "destructive" | "success" | "outline" | null | undefined}>
                    {payout.status}
                  </Badge>
                </div>
                <p className="text-sm text-gray-500">
                  Requested {formatDistanceToNow(new Date(payout.requestDate), { addSuffix: true })}
                </p>
                {payout.processedDate && (
                  <p className="text-sm text-gray-500">
                    Processed {formatDistanceToNow(new Date(payout.processedDate), { addSuffix: true })}
                  </p>
                )}
              </div>
              <div className="flex flex-col md:items-end">
                <span className="text-xl font-bold">{formatCurrency(payout.amount)}</span>
                <div className="flex items-center mt-2">
                  <Button variant="ghost" size="sm" className="h-8 px-2" asChild>
                    <Link href={`/dashboard/organizer/events/payouts/${payout.eventId}`}>
                      View Details
                      <ArrowUpRight className="ml-1 h-3 w-3" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
