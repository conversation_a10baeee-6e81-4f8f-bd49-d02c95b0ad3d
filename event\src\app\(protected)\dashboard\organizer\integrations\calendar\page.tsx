'use client';

import { useState } from 'react';

// Define types for our data
type CalendarIntegration = {
  id: string;
  name: string;
  type: string;
  isConnected: boolean;
  status: string;
  lastSynced: string | null;
  accountEmail: string | null;
  syncDirection: string;
  autoSync: boolean;
};

type CalendarEvent = {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  isSynced: boolean;
  lastSynced: string | null;
};

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Calendar, Check, Clock, Info, RefreshCw, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { RoleGate } from '@/components/auth/role-gate';
import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Mock calendar integration data
const mockCalendarIntegrations = [
  {
    id: '1',
    name: 'Google Calendar',
    type: 'google',
    isConnected: true,
    status: 'active',
    lastSynced: '2023-10-20T14:22:10Z',
    accountEmail: '<EMAIL>',
    syncDirection: 'both',
    autoSync: true
  },
  {
    id: '2',
    name: 'Microsoft Outlook',
    type: 'outlook',
    isConnected: false,
    status: 'inactive',
    lastSynced: null,
    accountEmail: null,
    syncDirection: 'both',
    autoSync: true
  },
  {
    id: '3',
    name: 'Apple Calendar',
    type: 'apple',
    isConnected: false,
    status: 'inactive',
    lastSynced: null,
    accountEmail: null,
    syncDirection: 'both',
    autoSync: true
  }
];

// Mock events that can be synced
const mockEvents = [
  {
    id: '1',
    title: 'Tech Conference 2023',
    startDate: '2023-11-15T09:00:00Z',
    endDate: '2023-11-17T18:00:00Z',
    isSynced: true,
    lastSynced: '2023-10-18T10:30:00Z'
  },
  {
    id: '2',
    title: 'Music Festival',
    startDate: '2023-12-05T16:00:00Z',
    endDate: '2023-12-07T23:00:00Z',
    isSynced: true,
    lastSynced: '2023-10-19T14:45:00Z'
  },
  {
    id: '3',
    title: 'Product Launch',
    startDate: '2023-11-25T13:00:00Z',
    endDate: '2023-11-25T16:00:00Z',
    isSynced: false,
    lastSynced: null
  }
];

export default function CalendarSyncPage() {
  const [calendarIntegrations, setCalendarIntegrations] = useState<CalendarIntegration[]>(mockCalendarIntegrations);
  const [events, setEvents] = useState<CalendarEvent[]>(mockEvents);
  const [syncSettings, setSyncSettings] = useState({
    syncDirection: 'both',
    autoSync: true,
    syncFrequency: 'hourly',
    includeAttendees: true,
    includeDescription: true,
    includeLocation: true
  });

  const handleConnectGoogle = () => {
    // In a real app, this would redirect to Google OAuth flow
    // For demo purposes, we'll just update the state
    setCalendarIntegrations(calendarIntegrations.map(cal =>
      cal.type === 'google'
        ? {
            ...cal,
            isConnected: true,
            status: 'active',
            lastSynced: new Date().toISOString(),
            accountEmail: '<EMAIL>'
          }
        : cal
    ));
  };

  const handleDisconnect = (calendarId: string) => {
    setCalendarIntegrations(calendarIntegrations.map(cal =>
      cal.id === calendarId
        ? { ...cal, isConnected: false, status: 'inactive', lastSynced: null, accountEmail: null }
        : cal
    ));
  };

  const handleSyncEvent = (eventId: string) => {
    setEvents(events.map(event =>
      event.id === eventId
        ? { ...event, isSynced: !event.isSynced, lastSynced: event.isSynced ? null : new Date().toISOString() }
        : event
    ));
  };

  const handleSyncAll = () => {
    setEvents(events.map(event => ({
      ...event,
      isSynced: true,
      lastSynced: new Date().toISOString()
    })));
  };

  const handleToggleAutoSync = (calendarId: string) => {
    setCalendarIntegrations(calendarIntegrations.map(cal =>
      cal.id === calendarId
        ? { ...cal, autoSync: !cal.autoSync }
        : cal
    ));
  };

  const handleChangeSyncDirection = (calendarId: string, direction: string) => {
    setCalendarIntegrations(calendarIntegrations.map(cal =>
      cal.id === calendarId
        ? { ...cal, syncDirection: direction }
        : cal
    ));
  };

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Calendar Sync</h1>
          <p className="text-gray-500 mt-1">Sync your events with external calendar services</p>
        </div>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Calendar Integration</AlertTitle>
          <AlertDescription>
            Connect your calendar to automatically sync event details. Changes made to events will be reflected in your connected calendars.
          </AlertDescription>
        </Alert>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {calendarIntegrations.map(calendar => (
            <Card key={calendar.id}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-center">
                    <div className="w-10 h-10 mr-3 bg-blue-50 rounded-md flex items-center justify-center">
                      <Calendar className="h-6 w-6 text-blue-500" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{calendar.name}</CardTitle>
                      {calendar.accountEmail && (
                        <CardDescription className="mt-1">
                          {calendar.accountEmail}
                        </CardDescription>
                      )}
                    </div>
                  </div>
                  <Badge variant={calendar.status === 'active' ? "success" : "outline"}>
                    {calendar.status === 'active' ? "Connected" : "Not Connected"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                {calendar.isConnected ? (
                  <div className="text-sm space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Last Synced</span>
                      <span>{calendar.lastSynced ? new Date(calendar.lastSynced).toLocaleString() : 'Never'}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-500">Auto Sync</span>
                      <Switch
                        checked={calendar.autoSync}
                        onCheckedChange={() => handleToggleAutoSync(calendar.id)}
                      />
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-500">Sync Direction</span>
                      <Select
                        value={calendar.syncDirection}
                        onValueChange={(value) => handleChangeSyncDirection(calendar.id, value)}
                      >
                        <SelectTrigger className="w-[140px]">
                          <SelectValue placeholder="Select direction" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="both">Two-way</SelectItem>
                          <SelectItem value="to_calendar">To Calendar Only</SelectItem>
                          <SelectItem value="from_calendar">From Calendar Only</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-2">
                    <p className="text-gray-500 text-sm mb-2">Not connected</p>
                    {calendar.type === 'google' && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={handleConnectGoogle}
                      >
                        Connect {calendar.name}
                      </Button>
                    )}
                    {calendar.type !== 'google' && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        disabled
                      >
                        Coming Soon
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
              {calendar.isConnected && (
                <CardFooter className="flex justify-between border-t pt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Simulate sync
                      setCalendarIntegrations(calendarIntegrations.map(cal =>
                        cal.id === calendar.id
                          ? { ...cal, lastSynced: new Date().toISOString() }
                          : cal
                      ));
                    }}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Sync Now
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                    onClick={() => handleDisconnect(calendar.id)}
                  >
                    Disconnect
                  </Button>
                </CardFooter>
              )}
            </Card>
          ))}
        </div>

        <Tabs defaultValue="events">
          <TabsList>
            <TabsTrigger value="events">Events to Sync</TabsTrigger>
            <TabsTrigger value="settings">Sync Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="events">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Your Events</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSyncAll}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Sync All Events
                  </Button>
                </div>
                <CardDescription>
                  Select which events to sync with your connected calendars
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {events.map(event => (
                    <div key={event.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h3 className="font-medium">{event.title}</h3>
                        <div className="flex items-center text-sm text-gray-500 mt-1">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>
                            {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}
                          </span>
                        </div>
                        {event.lastSynced && (
                          <div className="text-xs text-gray-400 mt-1">
                            Last synced: {new Date(event.lastSynced).toLocaleString()}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center">
                        <Switch
                          checked={event.isSynced}
                          onCheckedChange={() => handleSyncEvent(event.id)}
                        />
                        <span className="ml-2 text-sm">
                          {event.isSynced ? 'Synced' : 'Not Synced'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Sync Settings</CardTitle>
                <CardDescription>
                  Configure how your events are synced with external calendars
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Sync Direction</Label>
                    <Select
                      value={syncSettings.syncDirection}
                      onValueChange={(value) => setSyncSettings({...syncSettings, syncDirection: value})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select direction" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="both">Two-way Sync</SelectItem>
                        <SelectItem value="to_calendar">To Calendar Only</SelectItem>
                        <SelectItem value="from_calendar">From Calendar Only</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500">
                      Controls whether changes are pushed to calendars, pulled from calendars, or both
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>Sync Frequency</Label>
                    <Select
                      value={syncSettings.syncFrequency}
                      onValueChange={(value) => setSyncSettings({...syncSettings, syncFrequency: value})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="realtime">Real-time</SelectItem>
                        <SelectItem value="hourly">Hourly</SelectItem>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="manual">Manual Only</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-3 pt-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="auto-sync">Automatic Sync</Label>
                      <p className="text-xs text-gray-500">
                        Automatically sync events based on the frequency above
                      </p>
                    </div>
                    <Switch
                      id="auto-sync"
                      checked={syncSettings.autoSync}
                      onCheckedChange={(checked) => setSyncSettings({...syncSettings, autoSync: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="include-attendees">Include Attendees</Label>
                      <p className="text-xs text-gray-500">
                        Sync attendee information to calendar events
                      </p>
                    </div>
                    <Switch
                      id="include-attendees"
                      checked={syncSettings.includeAttendees}
                      onCheckedChange={(checked) => setSyncSettings({...syncSettings, includeAttendees: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="include-description">Include Description</Label>
                      <p className="text-xs text-gray-500">
                        Sync event descriptions to calendar events
                      </p>
                    </div>
                    <Switch
                      id="include-description"
                      checked={syncSettings.includeDescription}
                      onCheckedChange={(checked) => setSyncSettings({...syncSettings, includeDescription: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="include-location">Include Location</Label>
                      <p className="text-xs text-gray-500">
                        Sync venue and location information to calendar events
                      </p>
                    </div>
                    <Switch
                      id="include-location"
                      checked={syncSettings.includeLocation}
                      onCheckedChange={(checked) => setSyncSettings({...syncSettings, includeLocation: checked})}
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4">
                <Button className="ml-auto">
                  Save Settings
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
