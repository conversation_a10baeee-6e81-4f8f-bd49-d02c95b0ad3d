'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertCircle, Calendar, Check, Facebook, Globe, Info, Instagram, Linkedin, RefreshCw, Send, Share2, Twitter, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { RoleGate } from '@/components/auth/role-gate';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

// Mock social media integration data
const mockSocialAccounts = [
  {
    id: '1',
    name: 'Twitter',
    type: 'twitter',
    isConnected: true,
    status: 'active',
    username: '@eventorganizer',
    avatar: '/images/twitter-avatar.png',
    autoPost: true
  },
  {
    id: '2',
    name: 'Facebook',
    type: 'facebook',
    isConnected: true,
    status: 'active',
    username: 'Event Organizer',
    avatar: '/images/facebook-avatar.png',
    autoPost: true
  },
  {
    id: '3',
    name: 'Instagram',
    type: 'instagram',
    isConnected: false,
    status: 'inactive',
    username: null,
    avatar: null,
    autoPost: true
  },
  {
    id: '4',
    name: 'LinkedIn',
    type: 'linkedin',
    isConnected: false,
    status: 'inactive',
    username: null,
    avatar: null,
    autoPost: true
  }
];

// Mock events for social sharing
const mockEvents = [
  {
    id: '1',
    title: 'Tech Conference 2023',
    startDate: '2023-11-15T09:00:00Z',
    endDate: '2023-11-17T18:00:00Z',
    image: '/images/event1.jpg',
    description: 'Join us for the biggest tech conference of the year! Featuring keynotes from industry leaders, workshops, and networking opportunities.',
    venue: 'Tech Convention Center',
    city: 'San Francisco',
    isPublished: true
  },
  {
    id: '2',
    title: 'Music Festival',
    startDate: '2023-12-05T16:00:00Z',
    endDate: '2023-12-07T23:00:00Z',
    image: '/images/event2.jpg',
    description: 'Three days of amazing music across 5 stages with over 50 artists. Food, camping, and unforgettable experiences!',
    venue: 'Riverside Park',
    city: 'Austin',
    isPublished: true
  },
  {
    id: '3',
    title: 'Product Launch',
    startDate: '2023-11-25T13:00:00Z',
    endDate: '2023-11-25T16:00:00Z',
    image: '/images/event3.jpg',
    description: 'Be the first to see our revolutionary new product. Exclusive demos and special launch day offers available.',
    venue: 'Innovation Hub',
    city: 'New York',
    isPublished: false
  }
];

export default function SocialMediaPage() {
  const [socialAccounts, setSocialAccounts] = useState(mockSocialAccounts);
  const [events, setEvents] = useState(mockEvents);
  const [selectedEvent, setSelectedEvent] = useState(events[0]);
  const [postContent, setPostContent] = useState('');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(['twitter', 'facebook']);
  const [autoPostSettings, setAutoPostSettings] = useState({
    newEvents: true,
    eventUpdates: true,
    reminders: true,
    reminderDays: 7
  });

  const handleConnectTwitter = () => {
    // In a real app, this would redirect to Twitter OAuth flow
    // For demo purposes, we'll just update the state
    setSocialAccounts(socialAccounts.map(account =>
      account.type === 'twitter'
        ? {
            ...account,
            isConnected: true,
            status: 'active',
            username: '@eventorganizer',
            avatar: '/images/twitter-avatar.png'
          }
        : account
    ));
  };

  const handleConnectFacebook = () => {
    // In a real app, this would redirect to Facebook OAuth flow
    setSocialAccounts(socialAccounts.map(account =>
      account.type === 'facebook'
        ? {
            ...account,
            isConnected: true,
            status: 'active',
            username: 'Event Organizer',
            avatar: '/images/facebook-avatar.png'
          }
        : account
    ));
  };

  const handleDisconnect = (accountId: string) => {
    setSocialAccounts(socialAccounts.map(account =>
      account.id === accountId
        ? { ...account, isConnected: false, status: 'inactive', username: null, avatar: null }
        : account
    ));

    // Also remove from selected platforms if disconnected
    if (socialAccounts.find(a => a.id === accountId)?.type) {
      const accountType = socialAccounts.find(a => a.id === accountId)?.type;
      setSelectedPlatforms(selectedPlatforms.filter(p => p !== accountType));
    }
  };

  const handleToggleAutoPost = (accountId: string) => {
    setSocialAccounts(socialAccounts.map(account =>
      account.id === accountId
        ? { ...account, autoPost: !account.autoPost }
        : account
    ));
  };

  const handleSelectEvent = (eventId: string) => {
    const event = events.find(e => e.id === eventId);
    if (event) {
      setSelectedEvent(event);
      // Generate default post content based on the event
      setPostContent(`Join us at ${event.title} on ${new Date(event.startDate).toLocaleDateString()}! ${event.description.substring(0, 100)}... #event #${event.city.replace(/\s+/g, '')}`);
    }
  };

  const handleTogglePlatform = (platform: string) => {
    if (selectedPlatforms.includes(platform)) {
      setSelectedPlatforms(selectedPlatforms.filter(p => p !== platform));
    } else {
      setSelectedPlatforms([...selectedPlatforms, platform]);
    }
  };

  const handlePost = () => {
    // In a real app, this would post to the selected social media platforms
    // For demo purposes, we'll just show a success message
    alert(`Post successfully shared to ${selectedPlatforms.map(p => p.charAt(0).toUpperCase() + p.slice(1)).join(', ')}`);
    setPostContent('');
  };

  const getSocialIcon = (type: string) => {
    switch (type) {
      case 'twitter':
        return <Twitter className="h-5 w-5" />;
      case 'facebook':
        return <Facebook className="h-5 w-5" />;
      case 'instagram':
        return <Instagram className="h-5 w-5" />;
      case 'linkedin':
        return <Linkedin className="h-5 w-5" />;
      default:
        return <Globe className="h-5 w-5" />;
    }
  };

  const getConnectedAccounts = () => {
    return socialAccounts.filter(account => account.isConnected);
  };

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Social Media</h1>
          <p className="text-gray-500 mt-1">Connect and share your events on social media platforms</p>
        </div>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Social Media Integration</AlertTitle>
          <AlertDescription>
            Connect your social media accounts to automatically share events and updates with your followers.
          </AlertDescription>
        </Alert>

        <Tabs defaultValue="accounts">
          <TabsList>
            <TabsTrigger value="accounts">Connected Accounts</TabsTrigger>
            <TabsTrigger value="share">Share Events</TabsTrigger>
            <TabsTrigger value="settings">Auto-Post Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="accounts">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {socialAccounts.map(account => (
                <Card key={account.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div className="flex items-center">
                        <div className={`w-10 h-10 mr-3 rounded-md flex items-center justify-center ${
                          account.type === 'twitter' ? 'bg-blue-50 text-blue-500' :
                          account.type === 'facebook' ? 'bg-indigo-50 text-indigo-500' :
                          account.type === 'instagram' ? 'bg-pink-50 text-pink-500' :
                          account.type === 'linkedin' ? 'bg-blue-50 text-blue-700' :
                          'bg-gray-50 text-gray-500'
                        }`}>
                          {getSocialIcon(account.type)}
                        </div>
                        <div>
                          <CardTitle className="text-lg">{account.name}</CardTitle>
                          {account.username && (
                            <CardDescription className="mt-1">
                              {account.username}
                            </CardDescription>
                          )}
                        </div>
                      </div>
                      <Badge variant={account.status === 'active' ? "success" : "outline"}>
                        {account.status === 'active' ? "Connected" : "Not Connected"}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {account.isConnected ? (
                      <div className="text-sm space-y-3">
                        <div className="flex items-center">
                          <Avatar className="h-8 w-8 mr-2">
                            <AvatarImage src={account.avatar || ''} />
                            <AvatarFallback>{account.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <span>Connected as {account.username}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-500">Auto-Post New Events</span>
                          <Switch
                            checked={account.autoPost}
                            onCheckedChange={() => handleToggleAutoPost(account.id)}
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-2">
                        <p className="text-gray-500 text-sm mb-2">Not connected</p>
                        {account.type === 'twitter' && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={handleConnectTwitter}
                          >
                            <Twitter className="h-4 w-4 mr-2" />
                            Connect {account.name}
                          </Button>
                        )}
                        {account.type === 'facebook' && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={handleConnectFacebook}
                          >
                            <Facebook className="h-4 w-4 mr-2" />
                            Connect {account.name}
                          </Button>
                        )}
                        {(account.type === 'instagram' || account.type === 'linkedin') && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            disabled
                          >
                            {getSocialIcon(account.type)}
                            <span className="ml-2">Coming Soon</span>
                          </Button>
                        )}
                      </div>
                    )}
                  </CardContent>
                  {account.isConnected && (
                    <CardFooter className="flex justify-between border-t pt-4">
                      <Button
                        variant="outline"
                        size="sm"
                      >
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh Token
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        onClick={() => handleDisconnect(account.id)}
                      >
                        Disconnect
                      </Button>
                    </CardFooter>
                  )}
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="share">
            <Card>
              <CardHeader>
                <CardTitle>Share Event on Social Media</CardTitle>
                <CardDescription>
                  Create and share posts about your events on connected social platforms
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="md:col-span-1">
                    <Label className="mb-2 block">Select Event</Label>
                    <div className="space-y-2">
                      {events.map(event => (
                        <div
                          key={event.id}
                          className={`p-3 border rounded-lg cursor-pointer ${selectedEvent?.id === event.id ? 'border-blue-500 bg-blue-50' : ''}`}
                          onClick={() => handleSelectEvent(event.id)}
                        >
                          <div className="flex justify-between items-start">
                            <h3 className="font-medium">{event.title}</h3>
                            {!event.isPublished && (
                              <Badge variant="outline" className="bg-yellow-50 text-yellow-700">Draft</Badge>
                            )}
                          </div>
                          <div className="flex items-center text-sm text-gray-500 mt-1">
                            <Calendar className="h-3 w-3 mr-1" />
                            <span>
                              {new Date(event.startDate).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <div className="space-y-4">
                      <div>
                        <Label className="mb-2 block">Select Platforms</Label>
                        <div className="flex flex-wrap gap-2">
                          {socialAccounts.filter(account => account.isConnected).map(account => (
                            <div
                              key={account.id}
                              className={`flex items-center p-2 border rounded-lg cursor-pointer ${
                                selectedPlatforms.includes(account.type) ?
                                  account.type === 'twitter' ? 'border-blue-500 bg-blue-50' :
                                  account.type === 'facebook' ? 'border-indigo-500 bg-indigo-50' :
                                  account.type === 'instagram' ? 'border-pink-500 bg-pink-50' :
                                  account.type === 'linkedin' ? 'border-blue-700 bg-blue-50' :
                                  'border-gray-500 bg-gray-50'
                                : ''
                              }`}
                              onClick={() => handleTogglePlatform(account.type)}
                            >
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 ${
                                account.type === 'twitter' ? 'bg-blue-500 text-white' :
                                account.type === 'facebook' ? 'bg-indigo-500 text-white' :
                                account.type === 'instagram' ? 'bg-pink-500 text-white' :
                                account.type === 'linkedin' ? 'bg-blue-700 text-white' :
                                'bg-gray-500 text-white'
                              }`}>
                                {getSocialIcon(account.type)}
                              </div>
                              <span>{account.name}</span>
                              {selectedPlatforms.includes(account.type) && (
                                <Check className="h-4 w-4 ml-2 text-green-500" />
                              )}
                            </div>
                          ))}
                        </div>
                        {getConnectedAccounts().length === 0 && (
                          <div className="text-center p-4 border rounded-lg mt-2">
                            <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                            <p className="text-gray-600">No social accounts connected</p>
                            <Button
                              variant="outline"
                              size="sm"
                              className="mt-2"
                              onClick={() => (document.querySelector('[data-value="accounts"]') as HTMLElement)?.click()}
                            >
                              Connect Accounts
                            </Button>
                          </div>
                        )}
                      </div>

                      {selectedEvent && getConnectedAccounts().length > 0 && (
                        <>
                          <div>
                            <Label htmlFor="post-content">Post Content</Label>
                            <Textarea
                              id="post-content"
                              value={postContent}
                              onChange={(e) => setPostContent(e.target.value)}
                              placeholder="Write your post content here..."
                              className="h-32"
                            />
                            <div className="flex justify-between mt-1">
                              <p className="text-xs text-gray-500">
                                {postContent.length} characters
                              </p>
                              {postContent.length > 280 && (
                                <p className="text-xs text-red-500">
                                  Too long for Twitter (max 280 characters)
                                </p>
                              )}
                            </div>
                          </div>

                          <div className="border rounded-lg p-4">
                            <h3 className="font-medium mb-2">Preview</h3>
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <div className="flex items-start">
                                <Avatar className="h-10 w-10 mr-3">
                                  <AvatarFallback>U</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">Event Organizer</div>
                                  <p className="text-sm mt-1">{postContent}</p>
                                  <div className="mt-2 border rounded-md overflow-hidden">
                                    <div className="h-32 bg-gray-200 flex items-center justify-center">
                                      <Calendar className="h-8 w-8 text-gray-400" />
                                    </div>
                                    <div className="p-2">
                                      <h4 className="font-medium">{selectedEvent.title}</h4>
                                      <p className="text-xs text-gray-500">{selectedEvent.venue}, {selectedEvent.city}</p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4">
                <Button
                  className="ml-auto"
                  disabled={!selectedEvent || selectedPlatforms.length === 0 || !postContent}
                  onClick={handlePost}
                >
                  <Send className="h-4 w-4 mr-2" />
                  Share Now
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Auto-Post Settings</CardTitle>
                <CardDescription>
                  Configure when and how your events are automatically shared on social media
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="auto-post-new">New Event Publications</Label>
                      <p className="text-xs text-gray-500">
                        Automatically post when a new event is published
                      </p>
                    </div>
                    <Switch
                      id="auto-post-new"
                      checked={autoPostSettings.newEvents}
                      onCheckedChange={(checked) => setAutoPostSettings({...autoPostSettings, newEvents: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="auto-post-updates">Event Updates</Label>
                      <p className="text-xs text-gray-500">
                        Automatically post when an event is significantly updated
                      </p>
                    </div>
                    <Switch
                      id="auto-post-updates"
                      checked={autoPostSettings.eventUpdates}
                      onCheckedChange={(checked) => setAutoPostSettings({...autoPostSettings, eventUpdates: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="auto-post-reminders">Event Reminders</Label>
                      <p className="text-xs text-gray-500">
                        Automatically post reminders before events
                      </p>
                    </div>
                    <Switch
                      id="auto-post-reminders"
                      checked={autoPostSettings.reminders}
                      onCheckedChange={(checked) => setAutoPostSettings({...autoPostSettings, reminders: checked})}
                    />
                  </div>

                  {autoPostSettings.reminders && (
                    <div className="ml-6 border-l-2 pl-4 pt-2">
                      <Label htmlFor="reminder-days">Days Before Event</Label>
                      <Select
                        value={autoPostSettings.reminderDays.toString()}
                        onValueChange={(value) => setAutoPostSettings({...autoPostSettings, reminderDays: parseInt(value)})}
                      >
                        <SelectTrigger id="reminder-days" className="w-full">
                          <SelectValue placeholder="Select days" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 day before</SelectItem>
                          <SelectItem value="3">3 days before</SelectItem>
                          <SelectItem value="7">1 week before</SelectItem>
                          <SelectItem value="14">2 weeks before</SelectItem>
                          <SelectItem value="30">1 month before</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                <div className="pt-4">
                  <h3 className="font-medium mb-2">Default Post Templates</h3>
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="new-event-template">New Event Template</Label>
                      <Textarea
                        id="new-event-template"
                        defaultValue="Excited to announce our new event: {event_title}! Join us on {event_date} at {event_venue}. Get your tickets now: {event_url} #event #{event_city}"
                        className="h-20 mt-1"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Available variables: {'{event_title}'}, {'{event_date}'}, {'{event_venue}'}, {'{event_city}'}, {'{event_url}'}
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="reminder-template">Reminder Template</Label>
                      <Textarea
                        id="reminder-template"
                        defaultValue="Only {days_left} days until {event_title}! Have you got your tickets yet? {event_url} #event #{event_city}"
                        className="h-20 mt-1"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Available variables: {'{event_title}'}, {'{event_date}'}, {'{days_left}'}, {'{event_venue}'}, {'{event_city}'}, {'{event_url}'}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4">
                <Button className="ml-auto">
                  Save Settings
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
