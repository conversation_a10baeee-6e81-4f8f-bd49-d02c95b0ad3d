'use client';

import { use } from 'react';

import React from 'react';
import { CampaignView } from '@/components/marketing/campaign-view';
import { RoleGate } from '@/components/auth/role-gate';

export default function CampaignViewPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);

  return (
    <RoleGate allowedRole="ORGANIZER">
      <CampaignView id={id} />
    </RoleGate>
  );
}
