import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { NewsletterSignup } from '@/components/marketing/newsletter-signup';
import { RoleGate } from '@/components/auth/role-gate';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import SubscriberManagement from '@/components/marketing/subscriber-management';

export default function NewsletterPage() {
  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div className="mb-6">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/dashboard/organizer/marketing">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Marketing
            </Link>
          </Button>
        </div>

        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Newsletter Subscription</h1>
          <p className="text-gray-600">
            Customize and manage your newsletter subscription forms.
          </p>
        </div>

        <Tabs defaultValue="forms" className="space-y-6">
          <TabsList>
            <TabsTrigger value="forms">Form Variants</TabsTrigger>
            <TabsTrigger value="subscribers">Subscribers</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="forms" className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Default Form</CardTitle>
                  <CardDescription>
                    Standard newsletter signup form with all options.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <NewsletterSignup
                    variant="default"
                    showName={true}
                    showInterests={true}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Card Form</CardTitle>
                  <CardDescription>
                    Boxed newsletter signup form with a card-like appearance.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <NewsletterSignup
                    variant="card"
                    title="Join Our Newsletter"
                    description="Get the latest updates on events and promotions."
                    showName={true}
                    showInterests={true}
                  />
                </CardContent>
              </Card>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Inline Form</CardTitle>
                  <CardDescription>
                    Compact inline newsletter signup form.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <NewsletterSignup
                    variant="inline"
                    title="Subscribe to Updates"
                    description="Stay in the loop with our latest news and events."
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Minimal Form</CardTitle>
                  <CardDescription>
                    Ultra-minimal newsletter signup form.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <NewsletterSignup
                    variant="minimal"
                    buttonText="Subscribe"
                  />
                </CardContent>
              </Card>
            </div>

            <Separator />

            <Card>
              <CardHeader>
                <CardTitle>Hero Form</CardTitle>
                <CardDescription>
                  Eye-catching hero section with newsletter signup.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <NewsletterSignup
                  variant="hero"
                  title="Stay Updated"
                  description="Subscribe to our newsletter for the latest events, promotions, and updates. Be the first to know about new features and upcoming events."
                  buttonText="Join Now"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="subscribers">
            <SubscriberManagement />
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Newsletter Settings</CardTitle>
                <CardDescription>
                  Configure your newsletter preferences and integrations.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <h3 className="text-lg font-medium mb-2">Newsletter settings would appear here</h3>
                  <p className="text-gray-500">
                    This section would typically include settings for email service providers, default templates, and subscription preferences.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
