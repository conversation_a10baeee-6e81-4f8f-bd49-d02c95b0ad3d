'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { RoleGate } from '@/components/auth/role-gate';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { NewsletterSignup } from '@/components/marketing/newsletter-signup';
import { Mail, Send, Users, FileText, ArrowRight, BarChart3, Calendar, LayoutDashboard, RefreshCw, Plus } from 'lucide-react';
import { useCurrentUser } from '@/hooks/use-current-user';
import dynamic from 'next/dynamic';

// Use dynamic import with SSR disabled to avoid hydration issues
const MarketingAnalyticsDashboard = dynamic(
  () => import('@/components/analytics/marketing-analytics-dashboard'),
  { ssr: false }
);

// Define campaign types
type CampaignStatus = 'SENT' | 'SCHEDULED' | 'ACTIVE' | 'DRAFT';

interface Campaign {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: CampaignStatus;
  content?: string;
  subject?: string;
  previewText?: string;
  audienceType?: string;
  audienceSegment?: string;
  sentAt?: string;
  scheduledDate?: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  eventId?: string | null;
  event?: {
    id: string;
    title: string;
    startDate: string;
    endDate: string;
    imagePath?: string;
  };
  recipients: {
    id: string;
    sentAt?: string;
    openedAt?: string;
    clickedAt?: string;
  }[];
  metrics?: {
    totalRecipients: number;
    openCount: number;
    clickCount: number;
    openRate: number;
    clickRate: number;
  };
}

// Define API response types
interface CampaignResponse {
  campaigns: Campaign[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

interface SubscriberStats {
  total: number;
  active: number;
  growth: number;
  openRate: number;
  clickRate: number;
}

export default function MarketingDashboardPage() {
  const user = useCurrentUser();
  const [activeTab, setActiveTab] = useState('campaigns');
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [subscriberStats, setSubscriberStats] = useState<SubscriberStats>({
    total: 0,
    active: 0,
    growth: 0,
    openRate: 0,
    clickRate: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch campaigns and subscriber stats
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch campaigns
        const campaignsResponse = await fetch('/api/emails/campaigns?limit=3');

        if (!campaignsResponse.ok) {
          throw new Error(`Error fetching campaigns: ${campaignsResponse.status}`);
        }

        const campaignsData: CampaignResponse = await campaignsResponse.json();
        setCampaigns(campaignsData.campaigns);

        // Fetch subscriber stats
        const statsResponse = await fetch('/api/audience/stats');

        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setSubscriberStats(statsData);
        }
      } catch (err) {
        console.error('Error fetching marketing data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    // Check URL for tab parameter
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');
    if (tabParam && ['campaigns', 'subscribers', 'analytics', 'quickActions'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, []);

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Marketing Dashboard</h1>
          <p className="text-gray-500 mt-1">Manage your email campaigns and marketing activities</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Subscribers</p>
                  <h3 className="text-3xl font-bold mt-1">{loading ? '...' : subscriberStats.total.toLocaleString()}</h3>
                  <p className="text-sm text-green-600 mt-1 flex items-center">
                    <ArrowRight className="h-4 w-4 mr-1" />
                    <span>+{loading ? '...' : subscriberStats.growth}% this month</span>
                  </p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500">Average Open Rate</p>
                  <h3 className="text-3xl font-bold mt-1">{loading ? '...' : subscriberStats.openRate}%</h3>
                  <p className="text-sm text-gray-500 mt-1">Industry avg: 21.5%</p>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <Mail className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500">Recent Campaigns</p>
                  <h3 className="text-3xl font-bold mt-1">{loading ? '...' : campaigns.length}</h3>
                  <p className="text-sm text-gray-500 mt-1">Last 30 days</p>
                </div>
                <div className="bg-orange-100 p-3 rounded-full">
                  <Send className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList>
            <TabsTrigger value="campaigns">Recent Campaigns</TabsTrigger>
            <TabsTrigger value="subscribers">Subscribers</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="quickActions">Quick Actions</TabsTrigger>
          </TabsList>

          <TabsContent value="campaigns" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Recent Campaigns</h2>
              <Button asChild>
                <Link href="/dashboard/organizer/marketing/campaigns">View All</Link>
              </Button>
            </div>

            <div className="grid grid-cols-1 gap-4">
              {loading ? (
                <div className="py-8 text-center">
                  <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
                  <p className="mt-2 text-gray-500">Loading campaigns...</p>
                </div>
              ) : error ? (
                <div className="py-8 text-center">
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                  </div>
                </div>
              ) : campaigns.length === 0 ? (
                <div className="py-8 text-center">
                  <p className="text-gray-500">No campaigns found. Create your first campaign to get started!</p>
                  <Button className="mt-4" asChild>
                    <Link href="/dashboard/organizer/marketing/campaigns/create">
                      <Plus className="mr-2 h-4 w-4" />
                      Create Campaign
                    </Link>
                  </Button>
                </div>
              ) : (
                campaigns.map((campaign) => (
                  <Card key={campaign.id}>
                    <CardContent className="p-6">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                        <div className="flex items-center gap-3">
                          <div className="bg-orange-100 p-2 rounded-full">
                            <Mail className="h-5 w-5 text-orange-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold">{campaign.name}</h3>
                            <p className="text-sm text-gray-500">
                              {campaign.status === 'SENT' && campaign.sentAt && `Sent on ${new Date(campaign.sentAt).toLocaleDateString()}`}
                              {campaign.status === 'SCHEDULED' && campaign.scheduledDate && `Scheduled for ${new Date(campaign.scheduledDate).toLocaleDateString()}`}
                              {campaign.status === 'ACTIVE' && campaign.createdAt && `Active since ${new Date(campaign.createdAt).toLocaleDateString()}`}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="text-sm">
                            <span className="text-gray-500">Recipients:</span>{' '}
                            <span className="font-medium">{campaign.metrics?.totalRecipients || campaign.recipients.length}</span>
                          </div>
                          {campaign.metrics?.openRate !== undefined && (
                            <div className="text-sm">
                              <span className="text-gray-500">Open Rate:</span>{' '}
                              <span className="font-medium">{campaign.metrics.openRate.toFixed(1)}%</span>
                            </div>
                          )}
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/dashboard/organizer/marketing/campaigns/${campaign.id}`}>
                              View
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="subscribers" className="space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Subscriber Growth</h2>
              <Button variant="outline" asChild>
                <Link href="/dashboard/organizer/marketing/newsletter">Manage Subscribers</Link>
              </Button>
            </div>

            <Card>
              <CardContent className="p-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <p className="text-sm text-gray-500">Total</p>
                    <p className="text-xl font-bold">{loading ? '...' : subscriberStats.total.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Active</p>
                    <p className="text-xl font-bold">{loading ? '...' : subscriberStats.active.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Open Rate</p>
                    <p className="text-xl font-bold">{loading ? '...' : subscriberStats.openRate}%</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Click Rate</p>
                    <p className="text-xl font-bold">{loading ? '...' : subscriberStats.clickRate}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Newsletter Signup Form</CardTitle>
                <CardDescription>
                  Preview your newsletter signup form
                </CardDescription>
              </CardHeader>
              <CardContent>
                <NewsletterSignup
                  variant="inline"
                  title="Subscribe to Updates"
                  description="Stay in the loop with our latest news and events."
                  organizerId={user?.id}
                />
              </CardContent>
              <CardFooter>
                <Button variant="outline" asChild className="w-full">
                  <Link href="/dashboard/organizer/marketing/newsletter">
                    Customize Signup Forms
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <MarketingAnalyticsDashboard />
          </TabsContent>

          <TabsContent value="quickActions" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col h-full justify-between">
                    <div>
                      <Send className="h-8 w-8 text-blue-500 mb-4" />
                      <h3 className="text-xl font-bold mb-2">Create Campaign</h3>
                      <p className="text-gray-500 mb-4">Create a new email campaign to engage with your audience</p>
                    </div>
                    <Button className="mt-2" asChild>
                      <Link href="/dashboard/organizer/marketing/campaigns/create">
                        Get Started
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col h-full justify-between">
                    <div>
                      <Users className="h-8 w-8 text-purple-500 mb-4" />
                      <h3 className="text-xl font-bold mb-2">Manage Subscribers</h3>
                      <p className="text-gray-500 mb-4">View and manage your newsletter subscribers</p>
                    </div>
                    <Button variant="outline" className="mt-2" asChild>
                      <Link href="/dashboard/organizer/marketing/newsletter">
                        Manage Subscribers
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col h-full justify-between">
                    <div>
                      <BarChart3 className="h-8 w-8 text-green-500 mb-4" />
                      <h3 className="text-xl font-bold mb-2">View Analytics</h3>
                      <p className="text-gray-500 mb-4">Get insights about your marketing performance</p>
                    </div>
                    <Button variant="outline" className="mt-2" asChild>
                      <Link href="?tab=analytics">
                        View Analytics
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}

