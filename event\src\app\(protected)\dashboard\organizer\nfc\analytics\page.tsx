'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Download,
  RefreshCw,
  Calendar,
  ArrowUpRight,
  TrendingUp,
  TrendingDown,
  DollarSign,
  CreditCard,
  ShoppingCart,
  Users,
  Loader2
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

// Define interfaces for our data
interface VendorData {
  id: string;
  name: string;
  count: number;
  volume: number;
  avgTransaction: number;
}

interface ProductData {
  id: string;
  name: string;
  count: number;
  volume: number;
}

interface TimeData {
  date?: string;
  hour?: string;
  count: number;
  volume: number;
}

interface SummaryData {
  totalTransactions: number;
  totalVolume: number;
  avgTransactionValue: number;
  activeCards: number;
  transactionChange: number;
  volumeChange: number;
  avgTransactionChange: number;
  activeCardsChange: number;
}

interface Event {
  id: string;
  title: string;
}

// Mock data for demonstration
const mockTransactionData = {
  daily: [
    { date: '2023-06-01', count: 120, volume: 3250.75 },
    { date: '2023-06-02', count: 145, volume: 4120.50 },
    { date: '2023-06-03', count: 210, volume: 5680.25 },
    { date: '2023-06-04', count: 198, volume: 5340.00 },
    { date: '2023-06-05', count: 156, volume: 4325.75 },
    { date: '2023-06-06', count: 178, volume: 4890.50 },
    { date: '2023-06-07', count: 202, volume: 5520.25 }
  ],
  hourly: [
    { hour: '08:00', count: 12, volume: 325.50 },
    { hour: '09:00', count: 18, volume: 480.75 },
    { hour: '10:00', count: 25, volume: 675.25 },
    { hour: '11:00', count: 32, volume: 860.00 },
    { hour: '12:00', count: 45, volume: 1215.50 },
    { hour: '13:00', count: 38, volume: 1025.75 },
    { hour: '14:00', count: 30, volume: 810.25 },
    { hour: '15:00', count: 28, volume: 756.00 },
    { hour: '16:00', count: 35, volume: 945.50 },
    { hour: '17:00', count: 42, volume: 1134.00 },
    { hour: '18:00', count: 48, volume: 1296.00 },
    { hour: '19:00', count: 40, volume: 1080.00 },
    { hour: '20:00', count: 35, volume: 945.50 },
    { hour: '21:00', count: 28, volume: 756.00 },
    { hour: '22:00', count: 20, volume: 540.00 }
  ]
};

const mockVendorData = [
  { id: 'V-123', name: 'Food Stall A', count: 156, volume: 4325.75, avgTransaction: 27.73 },
  { id: 'V-456', name: 'Merchandise Booth', count: 98, volume: 3920.50, avgTransaction: 40.01 },
  { id: 'V-789', name: 'Beverage Stand', count: 210, volume: 2520.00, avgTransaction: 12.00 },
  { id: 'V-234', name: 'Snack Cart', count: 175, volume: 1750.00, avgTransaction: 10.00 },
  { id: 'V-567', name: 'Souvenir Shop', count: 85, volume: 2975.00, avgTransaction: 35.00 }
];

const mockProductData = [
  { name: 'Beer', count: 320, volume: 1920.00 },
  { name: 'T-Shirt', count: 85, volume: 2125.00 },
  { name: 'Burger', count: 145, volume: 2317.55 },
  { name: 'Soda', count: 210, volume: 1050.00 },
  { name: 'Poster', count: 65, volume: 975.00 },
  { name: 'Fries', count: 130, volume: 650.00 },
  { name: 'Cap', count: 75, volume: 1125.00 },
  { name: 'Cocktail', count: 95, volume: 1140.00 }
];

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('7days');
  const [eventFilter, setEventFilter] = useState('all_events');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [events, setEvents] = useState<Event[]>([]);

  // State for analytics data
  const [summary, setSummary] = useState<SummaryData>({
    totalTransactions: 0,
    totalVolume: 0,
    avgTransactionValue: 0,
    activeCards: 0,
    transactionChange: 0,
    volumeChange: 0,
    avgTransactionChange: 0,
    activeCardsChange: 0
  });
  const [vendorData, setVendorData] = useState<VendorData[]>([]);
  const [productData, setProductData] = useState<ProductData[]>([]);
  const [dailyData, setDailyData] = useState<TimeData[]>([]);
  const [hourlyData, setHourlyData] = useState<TimeData[]>([]);

  // Fetch analytics data
  const fetchAnalytics = async () => {
    setIsLoading(true);
    try {
      // Build query parameters
      const queryParams = new URLSearchParams({
        timeRange
      });

      if (eventFilter !== 'all_events') {
        queryParams.append('eventId', eventFilter);
      }

      const response = await fetch(`/api/organizer/nfc/analytics?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();

      // Update state with fetched data
      setSummary(data.summary);
      setVendorData(data.vendorData);
      setProductData(data.productData);
      setDailyData(data.timeData.daily);
      setHourlyData(data.timeData.hourly);

      if (data.events) {
        setEvents(data.events);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast({
        title: 'Error',
        description: 'Failed to load analytics data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchAnalytics();
  }, []);

  // Fetch data when filters change
  useEffect(() => {
    fetchAnalytics();
  }, [timeRange, eventFilter]);

  // Refresh data
  const refreshData = () => {
    setIsRefreshing(true);
    fetchAnalytics();
  };

  // Export data
  const exportData = (format: string) => {
    toast({
      title: 'Export Started',
      description: `Exporting analytics as ${format.toUpperCase()}...`,
    });

    // Simulate export delay
    setTimeout(() => {
      toast({
        title: 'Export Complete',
        description: `Analytics have been exported as ${format.toUpperCase()}.`,
      });
    }, 1500);
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Payment Analytics</h1>
          <p className="text-gray-600 mt-1">
            Analyze NFC payment data across your event
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange} disabled={isLoading}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="yesterday">Yesterday</SelectItem>
              <SelectItem value="7days">Last 7 Days</SelectItem>
              <SelectItem value="30days">Last 30 Days</SelectItem>
              <SelectItem value="thisWeek">This Week</SelectItem>
              <SelectItem value="thisMonth">This Month</SelectItem>
            </SelectContent>
          </Select>

          <Select value={eventFilter} onValueChange={setEventFilter} disabled={isLoading}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select event" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all_events">All Events</SelectItem>
              {events.map(event => (
                <SelectItem key={event.id} value={event.id}>
                  {event.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button variant="outline" onClick={refreshData} disabled={isLoading || isRefreshing}>
            {isRefreshing ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>

          <Button variant="outline" onClick={() => exportData('csv')} disabled={isLoading}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
          <span>Loading analytics data...</span>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <ShoppingCart className="h-5 w-5 mr-2 text-blue-500" />
                Total Transactions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">
                {summary.totalTransactions.toLocaleString()}
              </div>
              <div className="flex items-center mt-2 text-sm">
                {summary.transactionChange > 0 ? (
                  <>
                    <TrendingUp className="h-4 w-4 mr-1 text-green-500" />
                    <span className="text-green-500 font-medium">+{summary.transactionChange.toFixed(1)}%</span>
                  </>
                ) : summary.transactionChange < 0 ? (
                  <>
                    <TrendingDown className="h-4 w-4 mr-1 text-red-500" />
                    <span className="text-red-500 font-medium">{summary.transactionChange.toFixed(1)}%</span>
                  </>
                ) : (
                  <>
                    <span className="text-gray-500 font-medium">0%</span>
                  </>
                )}
                <span className="text-gray-500 ml-1">vs. previous period</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <DollarSign className="h-5 w-5 mr-2 text-green-500" />
                Total Volume
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">
                K{summary.totalVolume.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </div>
              <div className="flex items-center mt-2 text-sm">
                {summary.volumeChange > 0 ? (
                  <>
                    <TrendingUp className="h-4 w-4 mr-1 text-green-500" />
                    <span className="text-green-500 font-medium">+{summary.volumeChange.toFixed(1)}%</span>
                  </>
                ) : summary.volumeChange < 0 ? (
                  <>
                    <TrendingDown className="h-4 w-4 mr-1 text-red-500" />
                    <span className="text-red-500 font-medium">{summary.volumeChange.toFixed(1)}%</span>
                  </>
                ) : (
                  <>
                    <span className="text-gray-500 font-medium">0%</span>
                  </>
                )}
                <span className="text-gray-500 ml-1">vs. previous period</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <CreditCard className="h-5 w-5 mr-2 text-purple-500" />
                Avg. Transaction
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">
                K{summary.avgTransactionValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </div>
              <div className="flex items-center mt-2 text-sm">
                {summary.avgTransactionChange > 0 ? (
                  <>
                    <TrendingUp className="h-4 w-4 mr-1 text-green-500" />
                    <span className="text-green-500 font-medium">+{summary.avgTransactionChange.toFixed(1)}%</span>
                  </>
                ) : summary.avgTransactionChange < 0 ? (
                  <>
                    <TrendingDown className="h-4 w-4 mr-1 text-red-500" />
                    <span className="text-red-500 font-medium">{summary.avgTransactionChange.toFixed(1)}%</span>
                  </>
                ) : (
                  <>
                    <span className="text-gray-500 font-medium">0%</span>
                  </>
                )}
                <span className="text-gray-500 ml-1">vs. previous period</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Users className="h-5 w-5 mr-2 text-amber-500" />
                Active Cards
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">
                {summary.activeCards.toLocaleString()}
              </div>
              <div className="flex items-center mt-2 text-sm">
                {summary.activeCardsChange > 0 ? (
                  <>
                    <TrendingUp className="h-4 w-4 mr-1 text-green-500" />
                    <span className="text-green-500 font-medium">+{summary.activeCardsChange.toFixed(1)}%</span>
                  </>
                ) : summary.activeCardsChange < 0 ? (
                  <>
                    <TrendingDown className="h-4 w-4 mr-1 text-red-500" />
                    <span className="text-red-500 font-medium">{summary.activeCardsChange.toFixed(1)}%</span>
                  </>
                ) : (
                  <>
                    <span className="text-gray-500 font-medium">0%</span>
                  </>
                )}
                <span className="text-gray-500 ml-1">vs. previous period</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Analytics Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">
            <BarChart className="mr-2 h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="vendors">
            <Users className="mr-2 h-4 w-4" />
            Vendors
          </TabsTrigger>
          <TabsTrigger value="products">
            <ShoppingCart className="mr-2 h-4 w-4" />
            Products
          </TabsTrigger>
          <TabsTrigger value="time">
            <Calendar className="mr-2 h-4 w-4" />
            Time Analysis
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Transaction Volume Over Time</CardTitle>
                <CardDescription>
                  Daily transaction volume for the selected period
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {isLoading ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
                    <span>Loading chart data...</span>
                  </div>
                ) : dailyData.length > 0 ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <LineChart className="h-12 w-12 text-gray-300" />
                    <div className="ml-2">
                      <span className="text-gray-500">Chart visualization would appear here</span>
                      <div className="text-sm text-gray-400 mt-1">
                        {dailyData.length} days of data from {new Date(dailyData[0].date || '').toLocaleDateString()} to {new Date(dailyData[dailyData.length - 1].date || '').toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-500">
                    No transaction data available for this period
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Transaction Count Over Time</CardTitle>
                <CardDescription>
                  Daily transaction count for the selected period
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {isLoading ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
                    <span>Loading chart data...</span>
                  </div>
                ) : dailyData.length > 0 ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <BarChart className="h-12 w-12 text-gray-300" />
                    <div className="ml-2">
                      <span className="text-gray-500">Chart visualization would appear here</span>
                      <div className="text-sm text-gray-400 mt-1">
                        Total: {dailyData.reduce((sum, day) => sum + day.count, 0)} transactions
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-500">
                    No transaction data available for this period
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Vendors</CardTitle>
                <CardDescription>
                  Vendors with the highest transaction volume
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  </div>
                ) : vendorData.length > 0 ? (
                  <div className="space-y-4">
                    {vendorData.slice(0, 5).sort((a, b) => b.volume - a.volume).map((vendor, index) => (
                      <div key={vendor.id} className="flex items-center">
                        <div className="w-8 text-gray-500">{index + 1}</div>
                        <div className="flex-1">
                          <div className="font-medium">{vendor.name}</div>
                          <div className="text-sm text-gray-500">{vendor.count} transactions</div>
                        </div>
                        <div className="font-medium">K{vendor.volume.toFixed(2)}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No vendor data available
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Products</CardTitle>
                <CardDescription>
                  Products with the highest sales volume
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  </div>
                ) : productData.length > 0 ? (
                  <div className="space-y-4">
                    {productData.slice(0, 5).sort((a, b) => b.volume - a.volume).map((product, index) => (
                      <div key={product.id} className="flex items-center">
                        <div className="w-8 text-gray-500">{index + 1}</div>
                        <div className="flex-1">
                          <div className="font-medium">{product.name}</div>
                          <div className="text-sm text-gray-500">{product.count} sold</div>
                        </div>
                        <div className="font-medium">K{product.volume.toFixed(2)}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No product data available
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="vendors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Vendor Performance</CardTitle>
              <CardDescription>
                Transaction volume and count by vendor
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3">Vendor</th>
                      <th className="text-right p-3">Transactions</th>
                      <th className="text-right p-3">Volume</th>
                      <th className="text-right p-3">Avg. Transaction</th>
                    </tr>
                  </thead>
                  <tbody>
                    {isLoading ? (
                      <tr>
                        <td colSpan={4} className="text-center py-8">
                          <div className="flex justify-center items-center">
                            <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
                            <span>Loading vendor data...</span>
                          </div>
                        </td>
                      </tr>
                    ) : vendorData.length > 0 ? (
                      vendorData.sort((a, b) => b.volume - a.volume).map(vendor => (
                        <tr key={vendor.id} className="border-b">
                          <td className="p-3">
                            <div className="font-medium">{vendor.name}</div>
                            <div className="text-sm text-gray-500">{vendor.id}</div>
                          </td>
                          <td className="text-right p-3">{vendor.count}</td>
                          <td className="text-right p-3">K{vendor.volume.toFixed(2)}</td>
                          <td className="text-right p-3">K{vendor.avgTransaction.toFixed(2)}</td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={4} className="text-center py-8 text-gray-500">
                          No vendor data available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Vendor Transaction Volume</CardTitle>
                <CardDescription>
                  Distribution of transaction volume by vendor
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <div className="w-full h-full flex items-center justify-center">
                  <PieChart className="h-12 w-12 text-gray-300" />
                  <span className="ml-2 text-gray-500">Chart visualization would appear here</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Vendor Transaction Count</CardTitle>
                <CardDescription>
                  Distribution of transaction count by vendor
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <div className="w-full h-full flex items-center justify-center">
                  <PieChart className="h-12 w-12 text-gray-300" />
                  <span className="ml-2 text-gray-500">Chart visualization would appear here</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Product Performance</CardTitle>
              <CardDescription>
                Sales volume and count by product
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3">Product</th>
                      <th className="text-right p-3">Quantity Sold</th>
                      <th className="text-right p-3">Volume</th>
                      <th className="text-right p-3">Avg. Price</th>
                    </tr>
                  </thead>
                  <tbody>
                    {isLoading ? (
                      <tr>
                        <td colSpan={4} className="text-center py-8">
                          <div className="flex justify-center items-center">
                            <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
                            <span>Loading product data...</span>
                          </div>
                        </td>
                      </tr>
                    ) : productData.length > 0 ? (
                      productData.sort((a, b) => b.volume - a.volume).map(product => (
                        <tr key={product.id} className="border-b">
                          <td className="p-3">
                            <div className="font-medium">{product.name}</div>
                          </td>
                          <td className="text-right p-3">{product.count}</td>
                          <td className="text-right p-3">K{product.volume.toFixed(2)}</td>
                          <td className="text-right p-3">K{(product.volume / product.count).toFixed(2)}</td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={4} className="text-center py-8 text-gray-500">
                          No product data available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Product Sales Volume</CardTitle>
                <CardDescription>
                  Distribution of sales volume by product
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <div className="w-full h-full flex items-center justify-center">
                  <PieChart className="h-12 w-12 text-gray-300" />
                  <span className="ml-2 text-gray-500">Chart visualization would appear here</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Product Sales Count</CardTitle>
                <CardDescription>
                  Distribution of sales count by product
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <div className="w-full h-full flex items-center justify-center">
                  <PieChart className="h-12 w-12 text-gray-300" />
                  <span className="ml-2 text-gray-500">Chart visualization would appear here</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="time" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Daily Transaction Pattern</CardTitle>
                <CardDescription>
                  Transaction volume and count by day
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {isLoading ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
                    <span>Loading chart data...</span>
                  </div>
                ) : dailyData.length > 0 ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <BarChart className="h-12 w-12 text-gray-300" />
                    <div className="ml-2">
                      <span className="text-gray-500">Chart visualization would appear here</span>
                      <div className="text-sm text-gray-400 mt-1">
                        {dailyData.length} days of data
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-500">
                    No transaction data available for this period
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Hourly Transaction Pattern</CardTitle>
                <CardDescription>
                  Transaction volume and count by hour
                </CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                {isLoading ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
                    <span>Loading chart data...</span>
                  </div>
                ) : hourlyData.some(hour => hour.count > 0) ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <BarChart className="h-12 w-12 text-gray-300" />
                    <div className="ml-2">
                      <span className="text-gray-500">Chart visualization would appear here</span>
                      <div className="text-sm text-gray-400 mt-1">
                        {hourlyData.filter(h => h.count > 0).length} hours with activity
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-500">
                    No transaction data available for this period
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Hourly Transaction Data</CardTitle>
              <CardDescription>
                Detailed breakdown of transactions by hour
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3">Hour</th>
                      <th className="text-right p-3">Transactions</th>
                      <th className="text-right p-3">Volume</th>
                      <th className="text-right p-3">Avg. Transaction</th>
                    </tr>
                  </thead>
                  <tbody>
                    {isLoading ? (
                      <tr>
                        <td colSpan={4} className="text-center py-8">
                          <div className="flex justify-center items-center">
                            <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
                            <span>Loading hourly data...</span>
                          </div>
                        </td>
                      </tr>
                    ) : hourlyData.some(hour => hour.count > 0) ? (
                      hourlyData.map(hour => (
                        <tr key={hour.hour} className="border-b">
                          <td className="p-3">{hour.hour}</td>
                          <td className="text-right p-3">{hour.count}</td>
                          <td className="text-right p-3">K{hour.volume.toFixed(2)}</td>
                          <td className="text-right p-3">
                            {hour.count > 0 ? `K${(hour.volume / hour.count).toFixed(2)}` : '-'}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={4} className="text-center py-8 text-gray-500">
                          No hourly data available for this period
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
