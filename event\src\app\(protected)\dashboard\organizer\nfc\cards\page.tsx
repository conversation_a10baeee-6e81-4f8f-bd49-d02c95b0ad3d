'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  CreditCard,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  RefreshCw,
  Download,
  Upload,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  Filter
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import DeviceScanner from '@/components/vendor/device-scanner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Define interfaces for our data
interface NFCCard {
  id: string;
  uid: string;
  status: string;
  balance: number;
  isActive: boolean;
  assignedTo?: string | null;
  lastUsed?: string | null;
  createdAt: string;
  event: {
    id: string;
    title: string;
  };
  user?: {
    id: string;
    name?: string | null;
    email?: string | null;
  } | null;
  transactionCount?: number;
  lastTransaction?: {
    id: string;
    amount: number;
    status: string;
    createdAt: string;
  } | null;
}

interface Event {
  id: string;
  title: string;
}

export default function NFCCardManagementPage() {
  const [cards, setCards] = useState<NFCCard[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const [isRegisterDialogOpen, setIsRegisterDialogOpen] = useState(false);
  const [newCardData, setNewCardData] = useState({ uid: '', assignedTo: '', initialBalance: '0.00', eventId: '' });
  const [isScannerOpen, setIsScannerOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalBalance, setTotalBalance] = useState(0);
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<string>('all_events');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  // Fetch NFC cards
  const fetchCards = async (page = 1, search = searchQuery, status = selectedStatus, eventId = selectedEvent) => {
    setIsLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        pageSize: '10',
      });

      if (search) queryParams.append('search', search);
      if (status !== 'all') queryParams.append('status', status);
      if (eventId) queryParams.append('eventId', eventId);

      const response = await fetch(`/api/organizer/nfc/cards?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();

      setCards(data.cards);
      setTotalCount(data.totalCount);
      setTotalPages(data.totalPages);
      setCurrentPage(data.currentPage);
      setTotalBalance(data.totalBalance || 0);

      if (data.events && data.events.length > 0) {
        setEvents(data.events);
      }
    } catch (error) {
      console.error('Error fetching NFC cards:', error);
      toast({
        title: 'Error',
        description: 'Failed to load NFC cards. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchCards();
  }, []);

  // Handle search
  const handleSearch = () => {
    fetchCards(1, searchQuery, selectedStatus, selectedEvent);
  };

  // Handle status filter change
  const handleStatusChange = (status: string) => {
    setSelectedStatus(status);
    fetchCards(1, searchQuery, status, selectedEvent);
  };

  // Handle event filter change
  const handleEventChange = (eventId: string) => {
    // If "All Events" is selected, use empty string for the API
    const actualEventId = eventId === 'all_events' ? '' : eventId;
    setSelectedEvent(eventId);
    fetchCards(1, searchQuery, selectedStatus, actualEventId);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    fetchCards(newPage, searchQuery, selectedStatus, selectedEvent);
  };

  // Handle card selection
  const toggleCardSelection = (cardId: string) => {
    setSelectedCards(prev =>
      prev.includes(cardId)
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  };

  // Handle select all
  const toggleSelectAll = () => {
    if (selectedCards.length === cards.length) {
      setSelectedCards([]);
    } else {
      setSelectedCards(cards.map(card => card.id));
    }
  };

  // Handle NFC card detection
  const handleNfcCardDetected = (cardId: string) => {
    setNewCardData({ ...newCardData, uid: cardId });
    setIsScannerOpen(false);

    toast({
      title: 'NFC Card Detected',
      description: `Card UID: ${cardId}`,
    });
  };

  // Handle register new card
  const handleRegisterCard = async () => {
    if (!newCardData.uid || !newCardData.eventId) {
      toast({
        title: 'Missing Information',
        description: 'Card UID and Event are required.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/organizer/nfc/cards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uid: newCardData.uid,
          assignedTo: newCardData.assignedTo,
          initialBalance: parseFloat(newCardData.initialBalance),
          eventId: newCardData.eventId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      const newCard = await response.json();

      // Refresh the card list
      fetchCards();

      // Reset form and close dialog
      setNewCardData({ uid: '', assignedTo: '', initialBalance: '0.00', eventId: '' });
      setIsRegisterDialogOpen(false);

      toast({
        title: 'Card Registered',
        description: `Card ${newCard.id} has been successfully registered.`,
      });
    } catch (error) {
      console.error('Error registering card:', error);
      toast({
        title: 'Registration Failed',
        description: error instanceof Error ? error.message : 'Failed to register card. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle card status change
  const handleCardStatusChange = async (cardId: string, newStatus: string) => {
    setIsLoading(true);

    try {
      const response = await fetch(`/api/organizer/nfc/cards/${cardId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      // Update the card in the local state
      setCards(prev =>
        prev.map(card =>
          card.id === cardId
            ? { ...card, status: newStatus }
            : card
        )
      );

      toast({
        title: 'Card Status Updated',
        description: `Card status changed to ${newStatus}.`,
      });
    } catch (error) {
      console.error('Error updating card status:', error);
      toast({
        title: 'Update Failed',
        description: error instanceof Error ? error.message : 'Failed to update card status. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action: string) => {
    if (selectedCards.length === 0) {
      toast({
        title: 'No Cards Selected',
        description: 'Please select at least one card to perform this action.',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);

    try {
      if (action === 'activate' || action === 'deactivate' || action === 'lost') {
        // Update status for all selected cards
        await Promise.all(
          selectedCards.map(cardId =>
            fetch(`/api/organizer/nfc/cards/${cardId}`, {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                status: action === 'activate' ? 'active' : action === 'deactivate' ? 'inactive' : 'lost',
              }),
            })
          )
        );

        // Update cards in the local state
        setCards(prev =>
          prev.map(card =>
            selectedCards.includes(card.id)
              ? { ...card, status: action === 'activate' ? 'active' : action === 'deactivate' ? 'inactive' : 'lost' }
              : card
          )
        );

        toast({
          title: `Cards ${action === 'activate' ? 'Activated' : action === 'deactivate' ? 'Deactivated' : 'Marked as Lost'}`,
          description: `${selectedCards.length} cards have been updated.`,
        });
      } else if (action === 'delete') {
        // Delete all selected cards
        await Promise.all(
          selectedCards.map(cardId =>
            fetch(`/api/organizer/nfc/cards/${cardId}`, {
              method: 'DELETE',
            })
          )
        );

        // Remove cards from the local state
        setCards(prev => prev.filter(card => !selectedCards.includes(card.id)));

        toast({
          title: 'Cards Deleted',
          description: `${selectedCards.length} cards have been deleted.`,
        });
      }
    } catch (error) {
      console.error(`Error performing bulk action (${action}):`, error);
      toast({
        title: 'Action Failed',
        description: error instanceof Error ? error.message : `Failed to ${action} cards. Please try again.`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setSelectedCards([]);
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">NFC Card Management</h1>
          <p className="text-gray-600 mt-1">
            Register, manage, and track NFC cards for your event
          </p>
        </div>
        <Dialog open={isRegisterDialogOpen} onOpenChange={setIsRegisterDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Register New Card
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Register New NFC Card</DialogTitle>
              <DialogDescription>
                Register a new NFC card to be used at your event.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="event">Event</Label>
                <Select
                  value={newCardData.eventId}
                  onValueChange={(value) => setNewCardData({ ...newCardData, eventId: value })}
                >
                  <SelectTrigger id="event">
                    <SelectValue placeholder="Select an event" />
                  </SelectTrigger>
                  <SelectContent>
                    {events.length > 0 ? (
                      events.map(event => (
                        <SelectItem key={event.id} value={event.id}>
                          {event.title}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="no_events" disabled>
                        No events available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {events.length === 0 && (
                  <p className="text-xs text-amber-600">
                    You need to create an event with NFC system settings first.
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="cardUid">Card UID</Label>
                <div className="flex gap-2">
                  <Input
                    id="cardUid"
                    placeholder="Enter card UID or scan card"
                    value={newCardData.uid}
                    onChange={(e) => setNewCardData({ ...newCardData, uid: e.target.value })}
                    className="flex-1"
                  />
                  <Dialog open={isScannerOpen} onOpenChange={setIsScannerOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" type="button">
                        Scan
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-md">
                      <DialogHeader>
                        <DialogTitle>Scan NFC Card</DialogTitle>
                      </DialogHeader>
                      <DeviceScanner
                        onNfcCardDetected={handleNfcCardDetected}
                      />
                    </DialogContent>
                  </Dialog>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="assignedTo">Assigned To</Label>
                <Input
                  id="assignedTo"
                  placeholder="Enter attendee name (optional)"
                  value={newCardData.assignedTo}
                  onChange={(e) => setNewCardData({ ...newCardData, assignedTo: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="initialBalance">Initial Balance (K)</Label>
                <Input
                  id="initialBalance"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  value={newCardData.initialBalance}
                  onChange={(e) => setNewCardData({ ...newCardData, initialBalance: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsRegisterDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleRegisterCard} disabled={!newCardData.uid || isLoading}>
                {isLoading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Registering...
                  </>
                ) : (
                  'Register Card'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Card Inventory</CardTitle>
          <CardDescription>
            Manage all NFC cards registered for your event
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="flex flex-col md:flex-row gap-4 w-full">
              <div className="flex gap-2 w-full md:w-auto">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    placeholder="Search cards..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
                <Button variant="outline" onClick={handleSearch}>
                  Search
                </Button>
              </div>

              <div className="flex gap-2 w-full md:w-auto">
                <Select value={selectedStatus} onValueChange={handleStatusChange}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="lost">Lost</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={selectedEvent} onValueChange={handleEventChange}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Select Event" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all_events">All Events</SelectItem>
                    {events.map(event => (
                      <SelectItem key={event.id} value={event.id}>
                        {event.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-2 ml-auto">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" disabled={selectedCards.length === 0}>
                      Bulk Actions
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuLabel>Actions for {selectedCards.length} cards</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleBulkAction('activate')}>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Activate Cards
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkAction('deactivate')}>
                      <XCircle className="mr-2 h-4 w-4" />
                      Deactivate Cards
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkAction('lost')}>
                      <AlertCircle className="mr-2 h-4 w-4" />
                      Mark as Lost
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleBulkAction('delete')} className="text-red-600">
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Cards
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
                <Button variant="outline">
                  <Upload className="mr-2 h-4 w-4" />
                  Import
                </Button>
              </div>
            </div>
          </div>

          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedCards.length > 0 && selectedCards.length === cards.length && cards.length > 0}
                      onCheckedChange={toggleSelectAll}
                      disabled={isLoading || cards.length === 0}
                    />
                  </TableHead>
                  <TableHead>Card ID</TableHead>
                  <TableHead>UID</TableHead>
                  <TableHead>Event</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Balance</TableHead>
                  <TableHead>Assigned To</TableHead>
                  <TableHead>Last Used</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-10">
                      <div className="flex flex-col items-center justify-center">
                        <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                        <p className="text-sm text-gray-500">Loading cards...</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : cards.length > 0 ? (
                  cards.map(card => (
                    <TableRow key={card.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedCards.includes(card.id)}
                          onCheckedChange={() => toggleCardSelection(card.id)}
                        />
                      </TableCell>
                      <TableCell className="font-medium">{card.id}</TableCell>
                      <TableCell>{card.uid}</TableCell>
                      <TableCell>{card.event.title}</TableCell>
                      <TableCell>
                        <Badge variant={
                          card.status === 'active' ? 'default' :
                          card.status === 'inactive' ? 'secondary' :
                          'destructive'
                        }>
                          {card.status}
                        </Badge>
                      </TableCell>
                      <TableCell>K{card.balance.toFixed(2)}</TableCell>
                      <TableCell>{card.assignedTo || 'Unassigned'}</TableCell>
                      <TableCell>{card.lastUsed ? new Date(card.lastUsed).toLocaleString() : 'Never'}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Details
                            </DropdownMenuItem>
                            {card.status !== 'active' && (
                              <DropdownMenuItem onClick={() => handleCardStatusChange(card.id, 'active')}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Activate Card
                              </DropdownMenuItem>
                            )}
                            {card.status !== 'inactive' && (
                              <DropdownMenuItem onClick={() => handleCardStatusChange(card.id, 'inactive')}>
                                <XCircle className="mr-2 h-4 w-4" />
                                Deactivate Card
                              </DropdownMenuItem>
                            )}
                            {card.status !== 'lost' && (
                              <DropdownMenuItem onClick={() => handleCardStatusChange(card.id, 'lost')}>
                                <AlertCircle className="mr-2 h-4 w-4" />
                                Mark as Lost
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Card
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-6 text-gray-500">
                      No cards found matching your search criteria
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <div className="text-sm text-gray-500">
            {isLoading ? (
              <span>Loading...</span>
            ) : (
              <span>
                Showing {cards.length} of {totalCount} cards
                {totalBalance > 0 && ` • Total Balance: K${totalBalance.toFixed(2)}`}
              </span>
            )}
          </div>

          {totalPages > 1 && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1 || isLoading}
              >
                Previous
              </Button>
              <span className="text-sm text-gray-500">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages || isLoading}
              >
                Next
              </Button>
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
