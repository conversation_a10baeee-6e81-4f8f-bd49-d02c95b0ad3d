'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { 
  BarChart3, 
  CreditCard, 
  DollarSign, 
  Users, 
  AlertCircle, 
  CheckCircle, 
  RefreshCw,
  ArrowUpRight
} from 'lucide-react';
import Link from 'next/link';
import { toast } from '@/components/ui/use-toast';

// Mock data for demonstration
const mockSystemStatus = {
  status: 'operational',
  lastUpdated: new Date().toISOString(),
  activeTerminals: 12,
  offlineTerminals: 2,
  totalTransactionsToday: 156,
  totalVolumeToday: 4325.75,
  activeCards: 342,
  systemAlerts: [
    { id: 1, type: 'info', message: 'System maintenance scheduled for tonight at 2 AM', timestamp: new Date().toISOString() },
    { id: 2, type: 'warning', message: 'Terminal #T-4582 has been offline for more than 30 minutes', timestamp: new Date(Date.now() - 45 * 60000).toISOString() }
  ]
};

const mockRecentTransactions = [
  { id: 'tx-1', cardId: 'C-7845', vendorId: 'V-123', amount: 25.99, status: 'completed', timestamp: new Date(Date.now() - 5 * 60000).toISOString() },
  { id: 'tx-2', cardId: 'C-2341', vendorId: 'V-456', amount: 12.50, status: 'completed', timestamp: new Date(Date.now() - 12 * 60000).toISOString() },
  { id: 'tx-3', cardId: 'C-9023', vendorId: 'V-789', amount: 8.75, status: 'completed', timestamp: new Date(Date.now() - 18 * 60000).toISOString() },
  { id: 'tx-4', cardId: 'C-5672', vendorId: 'V-123', amount: 15.00, status: 'failed', timestamp: new Date(Date.now() - 25 * 60000).toISOString() },
  { id: 'tx-5', cardId: 'C-3390', vendorId: 'V-456', amount: 32.25, status: 'completed', timestamp: new Date(Date.now() - 35 * 60000).toISOString() }
];

export default function NFCDashboardPage() {
  const [systemStatus, setSystemStatus] = useState(mockSystemStatus);
  const [recentTransactions, setRecentTransactions] = useState(mockRecentTransactions);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Simulate refreshing data
  const refreshData = () => {
    setIsRefreshing(true);
    
    // Simulate API call delay
    setTimeout(() => {
      // Update with new mock data
      setSystemStatus({
        ...systemStatus,
        lastUpdated: new Date().toISOString(),
        totalTransactionsToday: systemStatus.totalTransactionsToday + Math.floor(Math.random() * 5),
        totalVolumeToday: systemStatus.totalVolumeToday + Math.floor(Math.random() * 100) / 4
      });
      
      setIsRefreshing(false);
      
      toast({
        title: "Dashboard Refreshed",
        description: "NFC system data has been updated.",
      });
    }, 1200);
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">NFC System Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Monitor your event's NFC payment system in real-time
          </p>
        </div>
        <Button onClick={refreshData} disabled={isRefreshing}>
          {isRefreshing ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>

      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className={systemStatus.status === 'operational' ? 'border-green-200' : 'border-red-200'}>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              {systemStatus.status === 'operational' ? (
                <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
              ) : (
                <AlertCircle className="h-5 w-5 mr-2 text-red-500" />
              )}
              System Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold capitalize">
              {systemStatus.status}
            </div>
            <p className="text-sm text-gray-500 mt-1">
              Last updated: {new Date(systemStatus.lastUpdated).toLocaleTimeString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <CreditCard className="h-5 w-5 mr-2 text-blue-500" />
              Terminals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemStatus.activeTerminals} / {systemStatus.activeTerminals + systemStatus.offlineTerminals}
            </div>
            <p className="text-sm text-gray-500 mt-1">
              Active terminals / Total
            </p>
            {systemStatus.offlineTerminals > 0 && (
              <div className="mt-2 text-amber-600 text-sm flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {systemStatus.offlineTerminals} terminals offline
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <DollarSign className="h-5 w-5 mr-2 text-green-500" />
              Today's Volume
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              K{systemStatus.totalVolumeToday.toFixed(2)}
            </div>
            <p className="text-sm text-gray-500 mt-1">
              {systemStatus.totalTransactionsToday} transactions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Users className="h-5 w-5 mr-2 text-purple-500" />
              Active Cards
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemStatus.activeCards}
            </div>
            <p className="text-sm text-gray-500 mt-1">
              Cards in circulation
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
              <CardDescription>
                Last 5 transactions across all terminals
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentTransactions.map(tx => (
                  <div key={tx.id} className="flex items-center justify-between border-b pb-3 last:border-0 last:pb-0">
                    <div>
                      <div className="flex items-center">
                        <CreditCard className="h-4 w-4 mr-2 text-gray-500" />
                        <span className="font-medium">{tx.cardId}</span>
                        <span className="mx-2 text-gray-400">•</span>
                        <span className="text-gray-600">Vendor: {tx.vendorId}</span>
                      </div>
                      <div className="text-sm text-gray-500 mt-1">
                        {new Date(tx.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium mr-3">K{tx.amount.toFixed(2)}</span>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        tx.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {tx.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4 text-center">
                <Button variant="outline" asChild>
                  <Link href="/dashboard/organizer/nfc/transactions">
                    View All Transactions
                    <ArrowUpRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Alerts</CardTitle>
              <CardDescription>
                Recent alerts and notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              {systemStatus.systemAlerts.length > 0 ? (
                <div className="space-y-4">
                  {systemStatus.systemAlerts.map(alert => (
                    <Alert key={alert.id} variant={alert.type === 'warning' ? 'destructive' : 'default'}>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>{alert.type === 'warning' ? 'Warning' : 'Information'}</AlertTitle>
                      <AlertDescription>
                        <div>{alert.message}</div>
                        <div className="text-xs mt-1 opacity-70">
                          {new Date(alert.timestamp).toLocaleTimeString()}
                        </div>
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-gray-500">
                  <CheckCircle className="h-8 w-8 mx-auto text-green-500 mb-2" />
                  <p>No active alerts</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full justify-start" asChild>
                <Link href="/dashboard/organizer/nfc/cards">
                  <CreditCard className="mr-2 h-4 w-4" />
                  Manage Cards
                </Link>
              </Button>
              <Button className="w-full justify-start" asChild>
                <Link href="/dashboard/organizer/nfc/transactions">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  View Transactions
                </Link>
              </Button>
              <Button className="w-full justify-start" asChild>
                <Link href="/dashboard/organizer/nfc/analytics">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  View Analytics
                </Link>
              </Button>
              <Button className="w-full justify-start" asChild>
                <Link href="/dashboard/organizer/nfc/settings">
                  <CreditCard className="mr-2 h-4 w-4" />
                  System Settings
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">Software Version</span>
                  <span className="font-medium">v2.5.0</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Last Sync</span>
                  <span className="font-medium">{new Date().toLocaleTimeString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">API Status</span>
                  <span className="font-medium text-green-600">Operational</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Database Status</span>
                  <span className="font-medium text-green-600">Operational</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
