import React from 'react'
import { Suspense } from 'react'
import { RoleGate } from '@/components/auth/role-gate'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ChevronLeft } from 'lucide-react'
import CancelledOrdersList from '@/components/orders/cancelled-orders-list'

export default function CancelledOrdersPage() {
  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div className="mb-6">
          <Button variant="ghost" asChild className="mb-4">
            <Link href="/dashboard/organizer/orders">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to All Orders
            </Link>
          </Button>
          
          <h1 className="text-3xl font-bold mb-2">Cancelled Orders</h1>
          <p className="text-gray-600">
            View all cancelled orders for your events
          </p>
        </div>
        
        <Suspense fallback={<div>Loading cancelled orders...</div>}>
          <CancelledOrdersList />
        </Suspense>
      </div>
    </RoleGate>
  )
}
