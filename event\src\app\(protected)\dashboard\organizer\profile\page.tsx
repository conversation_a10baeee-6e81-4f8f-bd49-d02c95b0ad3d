'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { useCurrentUser } from '@/hooks/use-current-user';
import { OrganizerVerificationForm } from '@/components/organizer/verification-form';
import { getOrganizerVerificationStatus } from '@/actions/organizer-verification';
import { AlertCircle, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { RoleGate } from '@/components/auth/role-gate';

export default function OrganizerProfilePage() {
  const router = useRouter();
  const user = useCurrentUser();
  const [activeTab, setActiveTab] = useState('profile');
  const [verificationStatus, setVerificationStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchVerificationStatus = async () => {
      try {
        const status = await getOrganizerVerificationStatus();
        setVerificationStatus(status);
      } catch (error) {
        console.error('Error fetching verification status:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchVerificationStatus();
  }, []);

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need to be logged in to view this page.</p>
          <Button onClick={() => router.push('/auth/login')}>Login</Button>
        </div>
      </div>
    );
  }

  const renderVerificationStatus = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    if (!verificationStatus) {
      return (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>Failed to load verification status</AlertDescription>
        </Alert>
      );
    }

    switch (verificationStatus.status) {
      case 'APPROVED':
        return (
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Verified Organizer</AlertTitle>
            <AlertDescription className="text-green-700">
              Your organizer account has been verified. You have full access to all organizer features.
              <div className="mt-2 text-sm">
                Verified on: {new Date(verificationStatus.verifiedAt).toLocaleDateString()}
              </div>
            </AlertDescription>
          </Alert>
        );

      case 'PENDING':
        return (
          <Alert className="bg-yellow-50 border-yellow-200">
            <Clock className="h-4 w-4 text-yellow-600" />
            <AlertTitle className="text-yellow-800">Verification Pending</AlertTitle>
            <AlertDescription className="text-yellow-700">
              Your verification is currently under review. This process typically takes 2-3 business days.
              <div className="mt-2 text-sm">
                Submitted on: {new Date(verificationStatus.submittedAt).toLocaleDateString()}
              </div>
            </AlertDescription>
          </Alert>
        );

      case 'REJECTED':
        return (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Verification Rejected</AlertTitle>
            <AlertDescription>
              <p>Your verification was not approved for the following reason:</p>
              <p className="mt-2 p-2 bg-red-50 border-l-2 border-red-500">{verificationStatus.rejectionReason}</p>
              <p className="mt-2">Please update your information and submit again.</p>
            </AlertDescription>
          </Alert>
        );

      case 'NOT_SUBMITTED':
      default:
        return (
          <Alert className="bg-blue-50 border-blue-200">
            <AlertCircle className="h-4 w-4 text-blue-600" />
            <AlertTitle className="text-blue-800">Verification Required</AlertTitle>
            <AlertDescription className="text-blue-700">
              To create and manage events, you need to verify your organizer account.
              Please complete the verification form.
            </AlertDescription>
          </Alert>
        );
    }
  };

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Organizer Profile</h1>

        {renderVerificationStatus()}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-8">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="profile">Profile Information</TabsTrigger>
            <TabsTrigger value="verification">Verification</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>
                  Manage your personal and account information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium">Account Details</h3>
                    <div className="mt-4 space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Name</label>
                          <div className="mt-1 p-3 bg-gray-50 rounded-md">
                            {user.name || 'Not provided'}
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">Email</label>
                          <div className="mt-1 p-3 bg-gray-50 rounded-md">
                            {user.email}
                          </div>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">Role</label>
                        <div className="mt-1 p-3 bg-gray-50 rounded-md">
                          {user.role}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button variant="outline" onClick={() => router.push('/dashboard/organizer/settings')}>
                  Edit Profile
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="verification" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Organizer Verification</CardTitle>
                <CardDescription>
                  Verify your identity and business information to gain full access to organizer features
                </CardDescription>
              </CardHeader>
              <CardContent>
                {verificationStatus?.status === 'APPROVED' ? (
                  <div className="p-6 text-center">
                    <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                    <h3 className="text-xl font-medium text-green-800 mb-2">Verification Complete</h3>
                    <p className="text-gray-600">
                      Your organizer account has been verified. You have full access to all organizer features.
                    </p>
                  </div>
                ) : verificationStatus?.status === 'PENDING' ? (
                  <div className="p-6 text-center">
                    <Clock className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
                    <h3 className="text-xl font-medium text-yellow-800 mb-2">Verification In Progress</h3>
                    <p className="text-gray-600 mb-4">
                      Your verification is currently under review. This process typically takes 2-3 business days.
                    </p>
                    <p className="text-sm text-gray-500">
                      Submitted on: {new Date(verificationStatus.submittedAt).toLocaleDateString()}
                    </p>
                  </div>
                ) : (
                  <OrganizerVerificationForm />
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
