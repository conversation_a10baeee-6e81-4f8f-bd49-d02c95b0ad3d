'use client';

import SimpleVerificationTest from '@/components/organizer/simple-verification-test';
import ApiVerificationTest from '@/components/organizer/api-verification-test';
import { DirectFixButton } from '@/components/account/direct-fix-button';
import { FixAccountButton } from '@/components/account/fix-account-button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function VerificationTestPage() {
  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-6">Verification Submission Tests</h1>
      <p className="mb-8 text-gray-600">
        This page provides different tests to diagnose issues with the verification form submission.
      </p>

      <Tabs defaultValue="server-action" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="server-action">Server Action Test</TabsTrigger>
          <TabsTrigger value="api">API Test</TabsTrigger>
          <TabsTrigger value="database">Database Fix</TabsTrigger>
        </TabsList>

        <TabsContent value="server-action" className="mt-6">
          <SimpleVerificationTest />
        </TabsContent>

        <TabsContent value="api" className="mt-6">
          <ApiVerificationTest />
        </TabsContent>

        <TabsContent value="database" className="mt-6">
          <div className="space-y-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <h2 className="text-lg font-medium text-yellow-800 mb-2">Database Fix Options</h2>
              <p className="text-sm text-yellow-700 mb-4">
                If you're seeing errors about "User account not properly configured" or "Foreign key constraint",
                try these options to fix your database records.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-medium mb-3">Direct Database Fix</h3>
                <p className="text-sm text-gray-600 mb-4">
                  This attempts to fix your user record directly in the database.
                </p>
                <DirectFixButton className="w-full" />
              </div>

              <div className="border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-medium mb-3">Alternative Fix Method</h3>
                <p className="text-sm text-gray-600 mb-4">
                  If the direct fix doesn't work, try this alternative method.
                </p>
                <FixAccountButton className="w-full" />
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="mt-10 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <h2 className="text-lg font-medium text-yellow-800 mb-2">Troubleshooting Tips</h2>
        <ul className="list-disc pl-5 space-y-1 text-sm text-yellow-700">
          <li>Check the browser console for any JavaScript errors</li>
          <li>Verify that you&apos;re properly authenticated as an organizer</li>
          <li>Make sure the server action is properly exported and accessible</li>
          <li>Check if the API endpoint is correctly configured</li>
          <li>Ensure the file system permissions allow writing to the uploads directory</li>
          <li>Verify that all required form fields are being submitted</li>
        </ul>
      </div>
    </div>
  );
}
