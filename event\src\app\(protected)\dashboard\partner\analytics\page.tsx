import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  DollarSign, 
  Users, 
  Calendar,
  Star,
  Download,
  Filter,
  Eye,
  Target,
  Clock
} from 'lucide-react';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
    name?: string;
    email?: string;
  };
};

// Mock analytics data
const analyticsData = {
  revenue: {
    current: 41250,
    previous: 35800,
    growth: 15.2
  },
  bookings: {
    current: 187,
    previous: 156,
    growth: 19.9
  },
  customers: {
    current: 142,
    previous: 128,
    growth: 10.9
  },
  rating: {
    current: 4.8,
    previous: 4.6,
    growth: 4.3
  }
};

const topPerformingItems = [
  { name: 'Executive Suite', revenue: 12600, bookings: 28, avgRating: 4.9 },
  { name: 'Deluxe Room', revenue: 11250, bookings: 45, avgRating: 4.8 },
  { name: 'Continental Breakfast', revenue: 3900, bookings: 156, avgRating: 4.7 },
  { name: 'Spa Package', revenue: 2760, bookings: 23, avgRating: 4.9 },
  { name: 'Airport Transfer', revenue: 420, bookings: 12, avgRating: 4.5 }
];

const monthlyData = [
  { month: 'Jan', revenue: 28500, bookings: 95, customers: 78 },
  { month: 'Feb', revenue: 32100, bookings: 112, customers: 89 },
  { month: 'Mar', revenue: 35800, bookings: 156, customers: 128 },
  { month: 'Apr', revenue: 41250, bookings: 187, customers: 142 }
];

const customerSegments = [
  { segment: 'Business Travelers', percentage: 45, revenue: 18562 },
  { segment: 'Leisure Travelers', percentage: 35, revenue: 14437 },
  { segment: 'Event Attendees', percentage: 15, revenue: 6187 },
  { segment: 'Local Customers', percentage: 5, revenue: 2064 }
];

export default async function PartnerAnalyticsPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only partners can access this page
  if (session.user.role !== 'PARTNER') {
    redirect('/dashboard');
  }

  const formatCurrency = (amount: number) => `K${amount.toLocaleString()}`;
  const formatPercentage = (value: number) => `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;

  const getTrendIcon = (growth: number) => {
    return growth > 0 ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-600" />
    );
  };

  const getTrendColor = (growth: number) => {
    return growth > 0 ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
          <p className="text-gray-500 mt-1">
            Track your business performance and insights
          </p>
        </div>
        <div className="flex gap-2 mt-4 md:mt-0">
          <Select defaultValue="30days">
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="1year">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analyticsData.revenue.current)}</div>
            <div className="flex items-center gap-1 text-xs">
              {getTrendIcon(analyticsData.revenue.growth)}
              <span className={getTrendColor(analyticsData.revenue.growth)}>
                {formatPercentage(analyticsData.revenue.growth)} from last month
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bookings</CardTitle>
            <Calendar className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.bookings.current}</div>
            <div className="flex items-center gap-1 text-xs">
              {getTrendIcon(analyticsData.bookings.growth)}
              <span className={getTrendColor(analyticsData.bookings.growth)}>
                {formatPercentage(analyticsData.bookings.growth)} from last month
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unique Customers</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.customers.current}</div>
            <div className="flex items-center gap-1 text-xs">
              {getTrendIcon(analyticsData.customers.growth)}
              <span className={getTrendColor(analyticsData.customers.growth)}>
                {formatPercentage(analyticsData.customers.growth)} from last month
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData.rating.current}</div>
            <div className="flex items-center gap-1 text-xs">
              {getTrendIcon(analyticsData.rating.growth)}
              <span className={getTrendColor(analyticsData.rating.growth)}>
                {formatPercentage(analyticsData.rating.growth)} from last month
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Monthly Trends */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Monthly Trends
                </CardTitle>
                <CardDescription>
                  Revenue and booking trends over the last 4 months
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {monthlyData.map((month, index) => (
                    <div key={month.month} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{month.month} 2024</p>
                        <p className="text-sm text-gray-500">{month.bookings} bookings</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-green-600">{formatCurrency(month.revenue)}</p>
                        <p className="text-sm text-gray-500">{month.customers} customers</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Performing Items */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Top Performing Items
                </CardTitle>
                <CardDescription>
                  Your best-selling services and accommodations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topPerformingItems.map((item, index) => (
                    <div key={item.name} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium">{item.name}</p>
                          <p className="text-sm text-gray-500">{item.bookings} bookings</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-green-600">{formatCurrency(item.revenue)}</p>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          <span className="text-sm">{item.avgRating}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Revenue Breakdown</CardTitle>
                <CardDescription>
                  Revenue distribution across different service categories
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <p className="text-2xl font-bold text-blue-600">K23,850</p>
                      <p className="text-sm text-gray-600">Accommodation</p>
                      <p className="text-xs text-gray-500">58% of total</p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <p className="text-2xl font-bold text-green-600">K3,900</p>
                      <p className="text-sm text-gray-600">Food & Beverage</p>
                      <p className="text-xs text-gray-500">9% of total</p>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <p className="text-2xl font-bold text-purple-600">K3,180</p>
                      <p className="text-sm text-gray-600">Services</p>
                      <p className="text-xs text-gray-500">8% of total</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue Goals</CardTitle>
                <CardDescription>
                  Monthly targets and achievements
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm">Monthly Target</span>
                    <span className="text-sm font-medium">K45,000</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${(analyticsData.revenue.current / 45000) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {Math.round((analyticsData.revenue.current / 45000) * 100)}% achieved
                  </p>
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm">Quarterly Target</span>
                    <span className="text-sm font-medium">K135,000</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: `${(150700 / 135000) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-green-600 mt-1">
                    112% achieved - Target exceeded!
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="customers" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Customer Segments</CardTitle>
                <CardDescription>
                  Revenue breakdown by customer type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {customerSegments.map((segment) => (
                    <div key={segment.segment} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{segment.segment}</span>
                        <span className="text-sm text-gray-500">{segment.percentage}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${segment.percentage}%` }}
                        ></div>
                      </div>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>Revenue: {formatCurrency(segment.revenue)}</span>
                        <span>Avg: {formatCurrency(Math.round(segment.revenue / (segment.percentage * 0.01 * analyticsData.customers.current)))}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Customer Insights</CardTitle>
                <CardDescription>
                  Key metrics about your customer base
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm">Repeat Customer Rate</span>
                  <span className="text-sm font-medium text-green-600">68%</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm">Average Stay Duration</span>
                  <span className="text-sm font-medium">2.3 nights</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm">Customer Lifetime Value</span>
                  <span className="text-sm font-medium">K1,245</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm">Booking Lead Time</span>
                  <span className="text-sm font-medium">12 days</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm">Cancellation Rate</span>
                  <span className="text-sm font-medium text-red-600">8.5%</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Operational Metrics</CardTitle>
                <CardDescription>
                  Key performance indicators
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Occupancy Rate</span>
                  <span className="text-sm font-medium text-green-600">78%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Average Daily Rate</span>
                  <span className="text-sm font-medium">K285</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Revenue per Room</span>
                  <span className="text-sm font-medium">K223</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Booking Conversion</span>
                  <span className="text-sm font-medium text-blue-600">24%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Service Quality</CardTitle>
                <CardDescription>
                  Customer satisfaction metrics
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Overall Rating</span>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm font-medium">4.8</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Response Rate</span>
                  <span className="text-sm font-medium text-green-600">94%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Avg Response Time</span>
                  <span className="text-sm font-medium">2.3 hours</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Recommendation Rate</span>
                  <span className="text-sm font-medium text-green-600">89%</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Growth Metrics</CardTitle>
                <CardDescription>
                  Month-over-month growth
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Revenue Growth</span>
                  <span className="text-sm font-medium text-green-600">+15.2%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Customer Growth</span>
                  <span className="text-sm font-medium text-green-600">+10.9%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Booking Growth</span>
                  <span className="text-sm font-medium text-green-600">+19.9%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Market Share</span>
                  <span className="text-sm font-medium text-blue-600">12.5%</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
