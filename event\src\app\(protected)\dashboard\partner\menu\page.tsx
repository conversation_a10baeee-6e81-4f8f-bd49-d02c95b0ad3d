import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { db } from '@/lib/prisma';
import { MenuManagementClient } from '@/components/partner/MenuManagementClient';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
    name?: string;
    email?: string;
  };
};



export default async function PartnerMenuPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only partners can access this page
  if (session.user.role !== 'PARTNER') {
    redirect('/dashboard');
  }

  // Get partner from database
  const partner = await db.partner.findUnique({
    where: { userId: session.user.id },
    include: {
      menuItems: {
        orderBy: [
          { category: 'asc' },
          { isPopular: 'desc' },
          { name: 'asc' },
        ],
      },
    },
  });

  if (!partner) {
    redirect('/dashboard');
  }

  // Get unique categories
  const categories = [...new Set(partner.menuItems.map(item => item.category))].sort();

  // Transform menu items to match the expected type
  const transformedMenuItems = partner.menuItems.map(item => ({
    ...item,
    description: item.description || undefined,
    imageUrl: item.imageUrl || undefined,
    preparationTime: item.preparationTime || undefined,
  }));

  return (
    <MenuManagementClient
      partnerId={partner.id}
      initialMenuItems={transformedMenuItems}
      initialCategories={categories}
    />
  );
}