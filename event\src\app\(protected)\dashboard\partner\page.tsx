import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { db } from '@/lib/prisma';
import {
  Hotel,
  Star,
  Calendar,
  TrendingUp,
  Users,
  DollarSign,
  Tag,
  Award,
  BarChart3,
  Settings,
  Plus,
  Eye,
  ArrowRight
} from 'lucide-react';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
    name?: string;
    email?: string;
  };
};

export default async function PartnerDashboardPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only partners can access this page
  if (session.user.role !== 'PARTNER') {
    redirect('/dashboard');
  }

  // Get partner data from database
  const partner = await db.partner.findUnique({
    where: { userId: session.user.id },
    include: {
      promotions: {
        where: { isActive: true }
      },
      eventPartnerships: {
        include: {
          event: true
        }
      },
      reviews: true,
      nfcTransactions: {
        where: {
          status: 'COMPLETED'
        }
      }
    }
  });

  if (!partner) {
    redirect('/dashboard');
  }

  // Calculate stats from real data
  const activePromotions = partner.promotions.length;
  const eventPartnerships = partner.eventPartnerships.length;
  const averageRating = partner.reviews.length > 0
    ? partner.reviews.reduce((sum, review) => sum + review.rating, 0) / partner.reviews.length
    : 0;
  const monthlyRevenue = partner.nfcTransactions.reduce((sum, transaction) => sum + transaction.amount, 0);

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">
          Welcome back, {session.user.name?.split(' ')[0] || 'Partner'}
        </h1>
        <p className="text-gray-500 mt-1">
          Manage your business profile, promotions, and partnerships
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Promotions</CardTitle>
            <Tag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activePromotions}</div>
            <p className="text-xs text-muted-foreground">
              Currently running
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Event Partnerships</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{eventPartnerships}</div>
            <p className="text-xs text-muted-foreground">
              Active partnerships
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customer Reviews</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageRating > 0 ? averageRating.toFixed(1) : 'N/A'}</div>
            <p className="text-xs text-muted-foreground">
              Average rating ({partner.reviews.length} reviews)
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">K{monthlyRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              From completed transactions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Promotions Management
            </CardTitle>
            <CardDescription>
              Create and manage promotional campaigns for your business
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Active Promotions</p>
                <p className="text-sm text-gray-500">{activePromotions} campaigns running</p>
              </div>
              <Badge variant="outline" className="bg-green-100 text-green-800">
                Active
              </Badge>
            </div>
            <div className="flex gap-2">
              <Button asChild size="sm">
                <Link href="/dashboard/partner/promotions">
                  <Eye className="mr-2 h-4 w-4" />
                  View All
                </Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/dashboard/partner/promotions?action=create">
                  <Plus className="mr-2 h-4 w-4" />
                  Create New
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Event Partnerships
            </CardTitle>
            <CardDescription>
              Manage your partnerships with event organizers
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Active Partnerships</p>
                <p className="text-sm text-gray-500">{eventPartnerships} events</p>
              </div>
              <Badge variant="outline" className="bg-blue-100 text-blue-800">
                {eventPartnerships > 0 ? 'Active' : 'None'}
              </Badge>
            </div>
            <div className="flex gap-2">
              <Button asChild size="sm">
                <Link href="/dashboard/partner/events">
                  <Eye className="mr-2 h-4 w-4" />
                  View Events
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Business Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Performance Overview
            </CardTitle>
            <CardDescription>
              Your business metrics and analytics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">Customer Engagement</p>
                  <p className="text-sm text-gray-500">Promotion views and interactions</p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-green-600">+24%</p>
                  <p className="text-sm text-gray-500">vs last month</p>
                </div>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">Partnership Growth</p>
                  <p className="text-sm text-gray-500">New event collaborations</p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-blue-600">+18%</p>
                  <p className="text-sm text-gray-500">vs last month</p>
                </div>
              </div>
            </div>
            <div className="mt-4">
              <Button asChild variant="outline" className="w-full">
                <Link href="/dashboard/partner/analytics">
                  View Detailed Analytics
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Quick Settings
            </CardTitle>
            <CardDescription>
              Manage your business profile and preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button asChild variant="outline" className="w-full justify-start">
              <Link href="/dashboard/partner/profile">
                <Hotel className="mr-2 h-4 w-4" />
                Business Profile
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full justify-start">
              <Link href="/dashboard/partner/menu">
                <Award className="mr-2 h-4 w-4" />
                Manage Menu
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full justify-start">
              <Link href="/dashboard/partner/loyalty">
                <Users className="mr-2 h-4 w-4" />
                Loyalty Program
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full justify-start">
              <Link href="/dashboard/partner/settings">
                <Settings className="mr-2 h-4 w-4" />
                Account Settings
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
