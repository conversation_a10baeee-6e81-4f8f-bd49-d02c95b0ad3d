import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { db } from '@/lib/prisma';
import ProfilePhotoSection from '@/components/partner/profile-photo-section';
import ProfileForm from '@/components/partner/profile-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Star,
  CheckCircle,
  AlertCircle,
  Crown,
  Target
} from 'lucide-react';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
    name?: string;
    email?: string;
  };
};

export default async function PartnerProfilePage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only partners can access this page
  if (session.user.role !== 'PARTNER') {
    redirect('/dashboard');
  }

  // Get partner data from database
  const partner = await db.partner.findUnique({
    where: { userId: session.user.id },
    include: {
      user: true,
      promotions: {
        where: { isActive: true }
      },
      eventPartnerships: true,
      reviews: true
    }
  });

  if (!partner) {
    redirect('/dashboard');
  }

  // Calculate stats
  const averageRating = partner.reviews.length > 0
    ? partner.reviews.reduce((sum, review) => sum + review.rating, 0) / partner.reviews.length
    : 0;

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Business Profile</h1>
        <p className="text-gray-500 mt-1">
          Manage your business information and settings
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Profile Form */}
        <ProfileForm
          partner={{
            id: partner.id,
            businessName: partner.businessName,
            partnerType: partner.partnerType,
            description: partner.description,
            contactName: partner.contactName,
            contactEmail: partner.contactEmail,
            contactPhone: partner.contactPhone,
            website: partner.website,
            address: partner.address
          }}
          userEmail={partner.user.email || ''}
          userName={partner.user.name || ''}
        />

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Verification Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Verification Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Business Verified</span>
                  <Badge variant="outline" className={partner.isVerified ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                    {partner.isVerified ? 'Verified' : 'Unverified'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Featured Partner</span>
                  <Badge variant="outline" className={partner.featured ? "bg-blue-100 text-blue-800" : "bg-gray-100 text-gray-800"}>
                    {partner.featured ? 'Featured' : 'Standard'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">NFC Payments</span>
                  <Badge variant="outline" className={partner.acceptsNfcPayments ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                    {partner.acceptsNfcPayments ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Partnership Tier */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-600" />
                Partnership Tier
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <Badge variant="outline" className="bg-yellow-100 text-yellow-800 text-lg px-4 py-2">
                  {partner.tier}
                </Badge>
                <p className="text-sm text-gray-500 mt-2">
                  Commission Rate: {partner.commissionRate}%
                </p>
                <Button variant="outline" size="sm" className="mt-3">
                  Upgrade Tier
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Total Reviews</span>
                <span className="font-semibold">{partner.totalReviews || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Active Promotions</span>
                <span className="font-semibold">{partner.promotions.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Event Partnerships</span>
                <span className="font-semibold">{partner.eventPartnerships.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Customer Rating</span>
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-semibold">{averageRating > 0 ? averageRating.toFixed(1) : 'N/A'}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Business Photo */}
          <ProfilePhotoSection
            initialBannerImage={partner.bannerImage}
            businessName={partner.businessName}
          />
        </div>
      </div>
    </div>
  );
}
