import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { db } from '@/lib/prisma';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { 
  Star, 
  MessageSquare, 
  TrendingUp, 
  Users,
  Reply,
  Flag,
  ThumbsUp,
  Calendar
} from 'lucide-react';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
    name?: string;
    email?: string;
  };
};



export default async function PartnerReviewsPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only partners can access this page
  if (session.user.role !== 'PARTNER') {
    redirect('/dashboard');
  }

  // Get partner and reviews from database
  const partner = await db.partner.findUnique({
    where: { userId: session.user.id },
    include: {
      reviews: {
        include: {
          user: true
        },
        orderBy: { createdAt: 'desc' }
      }
    }
  });

  if (!partner) {
    redirect('/dashboard');
  }

  const reviews = partner.reviews.map(review => ({
    ...review,
    customerName: review.user.name || 'Anonymous',
    hasResponse: false, // Response functionality would need to be added to schema
    response: null, // Response functionality would need to be added to schema
    helpful: 0, // This would need to be tracked separately
    category: 'General' // This would need to be added to schema
  }));

  const averageRating = reviews.length > 0
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
    : 0;
  const totalReviews = reviews.length;
  const pendingResponses = reviews.filter(review => !review.hasResponse).length;
  const totalHelpful = reviews.reduce((sum, review) => sum + review.helpful, 0);

  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
    rating,
    count: reviews.filter(review => review.rating === rating).length,
    percentage: totalReviews > 0 ? (reviews.filter(review => review.rating === rating).length / totalReviews) * 100 : 0
  }));

  const getStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4) return 'text-green-600';
    if (rating >= 3) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Customer Reviews</h1>
        <p className="text-gray-500 mt-1">
          Monitor and respond to customer feedback
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageRating.toFixed(1)}</div>
            <div className="flex items-center gap-1 mt-1">
              {getStars(Math.round(averageRating))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reviews</CardTitle>
            <MessageSquare className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalReviews}</div>
            <p className="text-xs text-muted-foreground">
              +3 this week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Responses</CardTitle>
            <Reply className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingResponses}</div>
            <p className="text-xs text-muted-foreground">
              Need your response
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Helpful Votes</CardTitle>
            <ThumbsUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalHelpful}</div>
            <p className="text-xs text-muted-foreground">
              Total helpful votes
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Reviews List */}
        <div className="lg:col-span-3">
          <Tabs defaultValue="all" className="space-y-6">
            <TabsList>
              <TabsTrigger value="all">All Reviews ({totalReviews})</TabsTrigger>
              <TabsTrigger value="pending">Pending Response ({pendingResponses})</TabsTrigger>
              <TabsTrigger value="responded">Responded ({totalReviews - pendingResponses})</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-4">
              {reviews.length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center py-8">
                      <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">No reviews yet</h3>
                      <p className="text-gray-500">Customer reviews will appear here once they start rating your business</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                reviews.map((review) => (
                <Card key={review.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold">{review.customerName}</h3>
                          <Badge variant="outline">{review.category}</Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            {getStars(review.rating)}
                          </div>
                          <span className={`font-semibold ${getRatingColor(review.rating)}`}>
                            {review.rating}.0
                          </span>
                          <span className="text-sm text-gray-500">
                            • {formatDate(review.createdAt)}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Flag className="h-4 w-4" />
                        </Button>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <ThumbsUp className="h-4 w-4" />
                          {review.helpful}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 mb-4">{review.comment}</p>
                    
                    {review.hasResponse ? (
                      <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
                        <div className="flex items-center gap-2 mb-2">
                          <Reply className="h-4 w-4 text-blue-600" />
                          <span className="font-medium text-blue-800">Your Response</span>
                        </div>
                        <p className="text-blue-700">{review.response}</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <Textarea 
                          placeholder="Write your response to this review..."
                          className="min-h-[80px]"
                        />
                        <div className="flex gap-2">
                          <Button size="sm">
                            <Reply className="mr-2 h-4 w-4" />
                            Post Response
                          </Button>
                          <Button variant="outline" size="sm">
                            Save Draft
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
                ))
              )}
            </TabsContent>

            <TabsContent value="pending" className="space-y-4">
              {reviews.filter(review => !review.hasResponse).map((review) => (
                <Card key={review.id} className="border-orange-200">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold">{review.customerName}</h3>
                          <Badge className="bg-orange-100 text-orange-800">Needs Response</Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            {getStars(review.rating)}
                          </div>
                          <span className={`font-semibold ${getRatingColor(review.rating)}`}>
                            {review.rating}.0
                          </span>
                          <span className="text-sm text-gray-500">
                            • {formatDate(review.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 mb-4">{review.comment}</p>
                    <div className="space-y-3">
                      <Textarea 
                        placeholder="Write your response to this review..."
                        className="min-h-[80px]"
                      />
                      <div className="flex gap-2">
                        <Button size="sm">
                          <Reply className="mr-2 h-4 w-4" />
                          Post Response
                        </Button>
                        <Button variant="outline" size="sm">
                          Save Draft
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="responded" className="space-y-4">
              {reviews.filter(review => review.hasResponse).map((review) => (
                <Card key={review.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold">{review.customerName}</h3>
                          <Badge className="bg-green-100 text-green-800">Responded</Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            {getStars(review.rating)}
                          </div>
                          <span className={`font-semibold ${getRatingColor(review.rating)}`}>
                            {review.rating}.0
                          </span>
                          <span className="text-sm text-gray-500">
                            • {formatDate(review.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 mb-4">{review.comment}</p>
                    <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
                      <div className="flex items-center gap-2 mb-2">
                        <Reply className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-blue-800">Your Response</span>
                      </div>
                      <p className="text-blue-700">{review.response}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Rating Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Rating Distribution
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {ratingDistribution.map((item) => (
                <div key={item.rating} className="flex items-center gap-3">
                  <div className="flex items-center gap-1 w-12">
                    <span className="text-sm">{item.rating}</span>
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  </div>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${item.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-500 w-8">{item.count}</span>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <MessageSquare className="mr-2 h-4 w-4" />
                Bulk Response
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Calendar className="mr-2 h-4 w-4" />
                Review Analytics
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Users className="mr-2 h-4 w-4" />
                Customer Insights
              </Button>
            </CardContent>
          </Card>

          {/* Review Insights */}
          <Card>
            <CardHeader>
              <CardTitle>Insights</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Response Rate</span>
                <span className="text-sm font-medium text-green-600">
                  {Math.round(((totalReviews - pendingResponses) / totalReviews) * 100)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Avg Response Time</span>
                <span className="text-sm font-medium">2.3 hours</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Most Mentioned</span>
                <span className="text-sm font-medium">Service</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Satisfaction Trend</span>
                <span className="text-sm font-medium text-green-600">↗ +0.2</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
