import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { db } from '@/lib/prisma';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CreditCard, 
  DollarSign, 
  TrendingUp, 
  Calendar,
  Filter,
  Download,
  Eye,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
    name?: string;
    email?: string;
  };
};



export default async function PartnerTransactionsPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only partners can access this page
  if (session.user.role !== 'PARTNER') {
    redirect('/dashboard');
  }

  // Get partner and transactions from database
  const partner = await db.partner.findUnique({
    where: { userId: session.user.id },
    include: {
      nfcTransactions: {
        include: {
          user: true
        },
        orderBy: { createdAt: 'desc' }
      }
    }
  });

  if (!partner) {
    redirect('/dashboard');
  }

  const transactions = partner.nfcTransactions.map(transaction => ({
    ...transaction,
    customerName: transaction.user.name || 'Unknown Customer',
    commission: transaction.amount * (partner.commissionRate / 100),
    type: 'PAYMENT', // Default type since it's not in the schema
    description: transaction.notes || `Payment of ${transaction.currency} ${transaction.amount}`
  }));

  const completedTransactions = transactions.filter(t => t.status === 'COMPLETED');
  const pendingTransactions = transactions.filter(t => t.status === 'PENDING');
  const failedTransactions = transactions.filter(t => t.status === 'FAILED');

  const totalRevenue = completedTransactions.reduce((sum, t) => sum + t.amount, 0);
  const totalCommission = completedTransactions.reduce((sum, t) => sum + t.commission, 0);
  const netRevenue = totalRevenue - totalCommission;

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'PENDING':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'FAILED':
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'ROOM_BOOKING':
        return '🏨';
      case 'FOOD_BEVERAGE':
        return '🍽️';
      case 'SERVICE':
        return '🛎️';
      default:
        return '💳';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Transactions</h1>
          <p className="text-gray-500 mt-1">
            Track all your business transactions and payments
          </p>
        </div>
        <div className="flex gap-2 mt-4 md:mt-0">
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">K{totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +18% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">K{netRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              After commission
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
            <CreditCard className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{transactions.length}</div>
            <p className="text-xs text-muted-foreground">
              {completedTransactions.length} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Commission Paid</CardTitle>
            <Calendar className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">K{totalCommission.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              Platform fees
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Transactions Tabs */}
      <Tabs defaultValue="all" className="space-y-6">
        <TabsList>
          <TabsTrigger value="all">All ({transactions.length})</TabsTrigger>
          <TabsTrigger value="completed">Completed ({completedTransactions.length})</TabsTrigger>
          <TabsTrigger value="pending">Pending ({pendingTransactions.length})</TabsTrigger>
          <TabsTrigger value="failed">Failed ({failedTransactions.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {transactions.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No transactions yet</h3>
                  <p className="text-gray-500">Transactions will appear here once customers make purchases</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            transactions.map((transaction) => (
            <Card key={transaction.id}>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="text-2xl">{getTypeIcon(transaction.type || 'PAYMENT')}</div>
                    <div>
                      <h3 className="font-semibold">{transaction.description || 'Payment'}</h3>
                      <p className="text-sm text-gray-500">Customer: {transaction.customerName}</p>
                      <p className="text-xs text-gray-400">{formatDate(transaction.createdAt)}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-2 mb-2">
                      {getStatusBadge(transaction.status)}
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-2xl font-bold text-green-600">K{transaction.amount}</p>
                    {transaction.commission > 0 && (
                      <p className="text-sm text-gray-500">Commission: K{transaction.commission}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
            ))
          )}
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          {completedTransactions.map((transaction) => (
            <Card key={transaction.id}>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                    <div>
                      <h3 className="font-semibold">{transaction.description}</h3>
                      <p className="text-sm text-gray-500">Customer: {transaction.customerName}</p>
                      <p className="text-xs text-gray-400">{formatDate(transaction.createdAt)}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-green-600">K{transaction.amount}</p>
                    <p className="text-sm text-gray-500">Commission: K{transaction.commission}</p>
                    <p className="text-sm font-medium">Net: K{(transaction.amount - transaction.commission).toFixed(2)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="pending" className="space-y-4">
          {pendingTransactions.map((transaction) => (
            <Card key={transaction.id}>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Clock className="h-8 w-8 text-yellow-600" />
                    <div>
                      <h3 className="font-semibold">{transaction.description}</h3>
                      <p className="text-sm text-gray-500">Customer: {transaction.customerName}</p>
                      <p className="text-xs text-gray-400">{formatDate(transaction.createdAt)}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge className="bg-yellow-100 text-yellow-800 mb-2">Processing</Badge>
                    <p className="text-2xl font-bold text-yellow-600">K{transaction.amount}</p>
                    <p className="text-sm text-gray-500">Expected commission: K{transaction.commission}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="failed" className="space-y-4">
          {failedTransactions.map((transaction) => (
            <Card key={transaction.id} className="border-red-200">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <AlertCircle className="h-8 w-8 text-red-600" />
                    <div>
                      <h3 className="font-semibold">{transaction.description}</h3>
                      <p className="text-sm text-gray-500">Customer: {transaction.customerName}</p>
                      <p className="text-xs text-gray-400">{formatDate(transaction.createdAt)}</p>
                      <p className="text-xs text-red-600">Payment failed - insufficient funds</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge className="bg-red-100 text-red-800 mb-2">Failed</Badge>
                    <p className="text-2xl font-bold text-red-600">K{transaction.amount}</p>
                    <Button variant="outline" size="sm" className="mt-2">
                      Retry Payment
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}
