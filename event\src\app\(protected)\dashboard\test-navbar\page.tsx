import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CalendarDays, Users, BarChart3, DollarSign, ArrowUpRight } from 'lucide-react';

export default function TestNavbarPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-gray-500 mt-1">Welcome to your dashboard</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Events</p>
                <h3 className="text-3xl font-bold mt-1">24</h3>
                <p className="text-sm text-green-600 mt-1 flex items-center">
                  <ArrowUpRight className="h-4 w-4 mr-1" />
                  <span>+2 this month</span>
                </p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <CalendarDays className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Attendees</p>
                <h3 className="text-3xl font-bold mt-1">1,234</h3>
                <p className="text-sm text-green-600 mt-1 flex items-center">
                  <ArrowUpRight className="h-4 w-4 mr-1" />
                  <span>+15% vs last month</span>
                </p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <Users className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium text-gray-500">Revenue</p>
                <h3 className="text-3xl font-bold mt-1">$12,345</h3>
                <p className="text-sm text-green-600 mt-1 flex items-center">
                  <ArrowUpRight className="h-4 w-4 mr-1" />
                  <span>+8% vs last month</span>
                </p>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium text-gray-500">Conversion Rate</p>
                <h3 className="text-3xl font-bold mt-1">5.2%</h3>
                <p className="text-sm text-green-600 mt-1 flex items-center">
                  <ArrowUpRight className="h-4 w-4 mr-1" />
                  <span>+0.5% vs last month</span>
                </p>
              </div>
              <div className="bg-amber-100 p-3 rounded-full">
                <BarChart3 className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="p-4 border rounded-md mt-4">
          <h3 className="text-lg font-medium mb-4">Overview Content</h3>
          <p>This is a test page to demonstrate the new navbar design.</p>
          <div className="mt-4">
            <Button>Test Button</Button>
          </div>
        </TabsContent>
        <TabsContent value="analytics" className="p-4 border rounded-md mt-4">
          <h3 className="text-lg font-medium mb-4">Analytics Content</h3>
          <p>Analytics content would go here.</p>
        </TabsContent>
        <TabsContent value="reports" className="p-4 border rounded-md mt-4">
          <h3 className="text-lg font-medium mb-4">Reports Content</h3>
          <p>Reports content would go here.</p>
        </TabsContent>
        <TabsContent value="notifications" className="p-4 border rounded-md mt-4">
          <h3 className="text-lg font-medium mb-4">Notifications Content</h3>
          <p>Notifications content would go here.</p>
        </TabsContent>
      </Tabs>
    </div>
  );
}
