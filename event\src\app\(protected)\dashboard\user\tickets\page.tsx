'use client';

import React, { useState } from 'react';
import { RoleGate } from '@/components/auth/role-gate';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Ticket, Search, Clock, MapPin, Download, QrCode, CheckCircle, XCircle } from 'lucide-react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';

export default function UserTicketsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('active');

  // Mock data for tickets
  const tickets = [
    {
      id: 'ticket1',
      eventId: 'event1',
      eventTitle: 'Summer Music Festival',
      ticketType: 'VIP Pass',
      date: '2023-07-15T18:00:00Z',
      location: 'Central Park, New York',
      status: 'ACTIVE',
      price: 150.00,
      qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=TICKET-12345'
    },
    {
      id: 'ticket2',
      eventId: 'event2',
      eventTitle: 'Tech Conference 2023',
      ticketType: 'General Admission',
      date: '2023-08-10T09:00:00Z',
      location: 'Convention Center, San Francisco',
      status: 'ACTIVE',
      price: 75.00,
      qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=TICKET-67890'
    },
    {
      id: 'ticket3',
      eventId: 'event3',
      eventTitle: 'Art Exhibition',
      ticketType: 'Standard Entry',
      date: '2023-06-20T10:00:00Z',
      location: 'Modern Art Gallery, Chicago',
      status: 'USED',
      price: 25.00,
      qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=TICKET-24680'
    },
    {
      id: 'ticket4',
      eventId: 'event4',
      eventTitle: 'Food & Wine Festival',
      ticketType: 'Premium Package',
      date: '2023-05-25T12:00:00Z',
      location: 'Waterfront Park, Seattle',
      status: 'CANCELLED',
      price: 95.00,
      qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=TICKET-13579'
    }
  ];

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'USED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'USED':
        return <Clock className="h-4 w-4 text-gray-600" />;
      case 'CANCELLED':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  // Filter tickets based on tab and search term
  const filteredTickets = tickets.filter(ticket => {
    // Filter by tab
    if (activeTab === 'active' && ticket.status !== 'ACTIVE') {
      return false;
    }
    if (activeTab === 'used' && ticket.status !== 'USED') {
      return false;
    }
    if (activeTab === 'cancelled' && ticket.status !== 'CANCELLED') {
      return false;
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      return (
        ticket.eventTitle.toLowerCase().includes(term) ||
        ticket.ticketType.toLowerCase().includes(term) ||
        ticket.location.toLowerCase().includes(term)
      );
    }

    return true;
  });

  return (
    <RoleGate allowedRole="USER">
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold mb-2">My Tickets</h1>
            <p className="text-gray-500 dark:text-gray-400">
              View and manage all your event tickets
            </p>
          </div>
          <Button className="mt-4 md:mt-0" asChild>
            <Link href="/events">
              <Ticket className="mr-2 h-4 w-4" />
              Browse Events
            </Link>
          </Button>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              placeholder="Search tickets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Tabs defaultValue="active" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="used">Used</TabsTrigger>
            <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
          </TabsList>

          <TabsContent value="active" className="space-y-6">
            {filteredTickets.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                    <Ticket className="h-8 w-8 text-gray-500" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No active tickets</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">You don&apos;t have any active tickets. Browse events to purchase tickets.</p>
                  <Button asChild>
                    <Link href="/events">Browse Events</Link>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {filteredTickets.map((ticket, index) => (
                  <Card key={index}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>{ticket.eventTitle}</CardTitle>
                          <CardDescription>{ticket.ticketType}</CardDescription>
                        </div>
                        <span className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                          {getStatusIcon(ticket.status)}
                          {ticket.status}
                        </span>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-2">
                      <div className="flex flex-col md:flex-row gap-4">
                        <div className="flex-1 space-y-3">
                          <div className="flex items-center text-gray-500 dark:text-gray-400">
                            <Clock className="h-4 w-4 mr-2" />
                            <span className="text-sm">{formatDate(ticket.date)}</span>
                          </div>
                          <div className="flex items-start text-gray-500 dark:text-gray-400">
                            <MapPin className="h-4 w-4 mr-2 mt-0.5" />
                            <span className="text-sm">{ticket.location}</span>
                          </div>
                          <div className="flex items-center text-gray-500 dark:text-gray-400">
                            <Ticket className="h-4 w-4 mr-2" />
                            <span className="text-sm">Price: {formatCurrency(ticket.price)}</span>
                          </div>
                        </div>
                        <div className="flex flex-col items-center justify-center">
                          <img
                            src={ticket.qrCode}
                            alt="Ticket QR Code"
                            className="w-24 h-24 mb-2"
                          />
                          <span className="text-xs text-gray-500 dark:text-gray-400">#{ticket.id}</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-4 pt-4 border-t dark:border-gray-700">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/events/${ticket.eventId}`}>
                            Event Details
                          </Link>
                        </Button>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-1" />
                            Download
                          </Button>
                          <Button size="sm" asChild>
                            <Link href={`/dashboard/user/tickets/${ticket.id}`}>
                              <QrCode className="h-4 w-4 mr-1" />
                              View Ticket
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="used" className="space-y-6">
            {filteredTickets.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                    <Ticket className="h-8 w-8 text-gray-500" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No used tickets</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">You don&apos;t have any used tickets yet.</p>
                  <Button asChild>
                    <Link href="/events">Browse Events</Link>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {filteredTickets.map((ticket, index) => (
                  <Card key={index} className="opacity-80">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>{ticket.eventTitle}</CardTitle>
                          <CardDescription>{ticket.ticketType}</CardDescription>
                        </div>
                        <span className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                          {getStatusIcon(ticket.status)}
                          {ticket.status}
                        </span>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-2">
                      <div className="flex flex-col md:flex-row gap-4">
                        <div className="flex-1 space-y-3">
                          <div className="flex items-center text-gray-500 dark:text-gray-400">
                            <Clock className="h-4 w-4 mr-2" />
                            <span className="text-sm">{formatDate(ticket.date)}</span>
                          </div>
                          <div className="flex items-start text-gray-500 dark:text-gray-400">
                            <MapPin className="h-4 w-4 mr-2 mt-0.5" />
                            <span className="text-sm">{ticket.location}</span>
                          </div>
                          <div className="flex items-center text-gray-500 dark:text-gray-400">
                            <Ticket className="h-4 w-4 mr-2" />
                            <span className="text-sm">Price: {formatCurrency(ticket.price)}</span>
                          </div>
                        </div>
                        <div className="flex flex-col items-center justify-center">
                          <img
                            src={ticket.qrCode}
                            alt="Ticket QR Code"
                            className="w-24 h-24 mb-2 opacity-50"
                          />
                          <span className="text-xs text-gray-500 dark:text-gray-400">#{ticket.id}</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-4 pt-4 border-t dark:border-gray-700">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/events/${ticket.eventId}`}>
                            Event Details
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="cancelled" className="space-y-6">
            {filteredTickets.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                    <Ticket className="h-8 w-8 text-gray-500" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No cancelled tickets</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">You don&apos;t have any cancelled tickets.</p>
                  <Button asChild>
                    <Link href="/events">Browse Events</Link>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {filteredTickets.map((ticket, index) => (
                  <Card key={index} className="opacity-80">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>{ticket.eventTitle}</CardTitle>
                          <CardDescription>{ticket.ticketType}</CardDescription>
                        </div>
                        <span className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                          {getStatusIcon(ticket.status)}
                          {ticket.status}
                        </span>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-2">
                      <div className="flex flex-col md:flex-row gap-4">
                        <div className="flex-1 space-y-3">
                          <div className="flex items-center text-gray-500 dark:text-gray-400">
                            <Clock className="h-4 w-4 mr-2" />
                            <span className="text-sm">{formatDate(ticket.date)}</span>
                          </div>
                          <div className="flex items-start text-gray-500 dark:text-gray-400">
                            <MapPin className="h-4 w-4 mr-2 mt-0.5" />
                            <span className="text-sm">{ticket.location}</span>
                          </div>
                          <div className="flex items-center text-gray-500 dark:text-gray-400">
                            <Ticket className="h-4 w-4 mr-2" />
                            <span className="text-sm">Price: {formatCurrency(ticket.price)}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-4 pt-4 border-t dark:border-gray-700">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/events/${ticket.eventId}`}>
                            Event Details
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-1" />
                          Download Receipt
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
