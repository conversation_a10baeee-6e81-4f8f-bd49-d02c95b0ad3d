'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  Crown,
  Check,
  X,
  Star,
  Users,
  MessageCircle,
  Calendar,
  Video,
  FileText,
  Zap,
  Shield,
  Sparkles,
  ArrowRight,
  CreditCard,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { EliteCommunicationTier, EliteSubscriptionType } from '@prisma/client';
import { ELITE_PRICING_TIERS, ELITE_FEATURE_MATRIX } from '@/config/elite-pricing';
import { toast } from 'sonner';

interface UserSubscription {
  id: string;
  tier: EliteCommunicationTier;
  subscriptionType: EliteSubscriptionType;
  isActive: boolean;
  expiresAt?: Date;
  eventId?: string;
}

export default function UpgradePage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [selectedTier, setSelectedTier] = useState<EliteCommunicationTier>(EliteCommunicationTier.ELITE);
  const [subscriptionType, setSubscriptionType] = useState<EliteSubscriptionType>(EliteSubscriptionType.MONTHLY);
  const [currentSubscriptions, setCurrentSubscriptions] = useState<UserSubscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [upgrading, setUpgrading] = useState(false);
  const [upcomingEvents, setUpcomingEvents] = useState<any[]>([]);

  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      setLoading(true);

      // Fetch current subscriptions
      const subscriptionsResponse = await fetch('/api/elite-communication/user-subscriptions');
      if (subscriptionsResponse.ok) {
        const subscriptionsData = await subscriptionsResponse.json();
        setCurrentSubscriptions(subscriptionsData.subscriptions || []);
      }

      // Fetch upcoming events for per-event pricing
      const eventsResponse = await fetch('/api/events/upcoming?limit=10');
      if (eventsResponse.ok) {
        const eventsData = await eventsResponse.json();
        setUpcomingEvents(eventsData.events || []);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      toast.error('Failed to load subscription data');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (eventId?: string) => {
    if (!session?.user?.id) {
      toast.error('Please log in to upgrade');
      return;
    }

    try {
      setUpgrading(true);

      const upgradeData = {
        tier: selectedTier,
        subscriptionType: subscriptionType,
        eventId: eventId
      };

      const response = await fetch('/api/elite-communication/subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(upgradeData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`Successfully upgraded to ${selectedTier}!`);
        await fetchUserData(); // Refresh data

        // Redirect to payment if needed
        if (data.paymentUrl) {
          window.location.href = data.paymentUrl;
        }
      } else {
        toast.error(data.error || 'Upgrade failed');
      }
    } catch (error) {
      console.error('Error upgrading:', error);
      toast.error('Upgrade failed. Please try again.');
    } finally {
      setUpgrading(false);
    }
  };

  const getPrice = (tier: EliteCommunicationTier, type: EliteSubscriptionType) => {
    const tierData = ELITE_PRICING_TIERS.find(t => t.tier === tier);
    if (!tierData) return 0;

    switch (type) {
      case EliteSubscriptionType.PER_EVENT:
        return tierData.pricePerEvent || 0;
      case EliteSubscriptionType.MONTHLY:
        return tierData.monthlyPrice || 0;
      case EliteSubscriptionType.YEARLY:
        return tierData.annualPrice || 0;
      default:
        return 0;
    }
  };

  const getCurrentTier = (): EliteCommunicationTier => {
    const activeSubscription = currentSubscriptions.find(sub => sub.isActive);
    return activeSubscription?.tier || EliteCommunicationTier.BASIC;
  };

  const isCurrentTier = (tier: EliteCommunicationTier): boolean => {
    return getCurrentTier() === tier;
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-2">
          <Crown className="h-8 w-8 text-purple-600" />
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            Elite Communication
          </h1>
        </div>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Unlock premium networking features and connect with like-minded professionals at events
        </p>

        {/* Current Status */}
        <div className="flex items-center justify-center space-x-2">
          <span className="text-sm text-gray-500">Current tier:</span>
          <Badge className={`${getCurrentTier() === EliteCommunicationTier.BASIC ? 'bg-gray-100 text-gray-800' :
            getCurrentTier() === EliteCommunicationTier.ELITE ? 'bg-purple-100 text-purple-800' :
            'bg-yellow-100 text-yellow-800'}`}>
            {getCurrentTier()}
          </Badge>
        </div>
      </div>

      {/* Subscription Type Selection */}
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-center">Choose Your Subscription Type</CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={subscriptionType}
            onValueChange={(value) => setSubscriptionType(value as EliteSubscriptionType)}
            className="grid grid-cols-1 md:grid-cols-3 gap-4"
          >
            <div className="flex items-center space-x-2 p-4 border rounded-lg">
              <RadioGroupItem value={EliteSubscriptionType.PER_EVENT} id="per-event" />
              <Label htmlFor="per-event" className="flex-1">
                <div>
                  <div className="font-medium">Per Event</div>
                  <div className="text-sm text-gray-500">Pay for individual events</div>
                </div>
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-4 border rounded-lg">
              <RadioGroupItem value={EliteSubscriptionType.MONTHLY} id="monthly" />
              <Label htmlFor="monthly" className="flex-1">
                <div>
                  <div className="font-medium">Monthly</div>
                  <div className="text-sm text-gray-500">Unlimited events per month</div>
                </div>
              </Label>
            </div>
            <div className="flex items-center space-x-2 p-4 border rounded-lg">
              <RadioGroupItem value={EliteSubscriptionType.YEARLY} id="yearly" />
              <Label htmlFor="yearly" className="flex-1">
                <div>
                  <div className="font-medium">Annual</div>
                  <div className="text-sm text-gray-500 flex items-center">
                    Best value
                    <Star className="h-3 w-3 ml-1 text-yellow-500" />
                  </div>
                </div>
              </Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Pricing Tiers */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {ELITE_PRICING_TIERS.filter(tier => tier.tier !== EliteCommunicationTier.BASIC).map((tier) => (
          <Card
            key={tier.tier}
            className={`relative ${tier.popular ? 'ring-2 ring-purple-500 scale-105' : ''} ${
              isCurrentTier(tier.tier) ? 'opacity-50' : ''
            }`}
          >
            {tier.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-purple-600 text-white px-4 py-1">
                  Most Popular
                </Badge>
              </div>
            )}

            <CardHeader className="text-center">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <Crown className={`h-6 w-6 ${tier.tier === EliteCommunicationTier.ELITE ? 'text-purple-600' : 'text-yellow-600'}`} />
                <CardTitle className="text-2xl">{tier.name}</CardTitle>
              </div>
              <p className="text-gray-600">{tier.description}</p>

              <div className="mt-4">
                <div className="text-4xl font-bold">
                  ${getPrice(tier.tier, subscriptionType)}
                </div>
                <div className="text-sm text-gray-500">
                  {subscriptionType === EliteSubscriptionType.PER_EVENT && 'per event'}
                  {subscriptionType === EliteSubscriptionType.MONTHLY && 'per month'}
                  {subscriptionType === EliteSubscriptionType.YEARLY && 'per year'}
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="space-y-2">
                {tier.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Check className="h-4 w-4 text-green-500" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>

              <Button
                className={`w-full ${tier.popular ? 'bg-purple-600 hover:bg-purple-700' : ''}`}
                disabled={isCurrentTier(tier.tier) || upgrading}
                onClick={() => {
                  setSelectedTier(tier.tier);
                  if (subscriptionType === EliteSubscriptionType.PER_EVENT) {
                    // Show event selection for per-event subscriptions
                    // For now, just upgrade without specific event
                    handleUpgrade();
                  } else {
                    handleUpgrade();
                  }
                }}
              >
                {upgrading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Sparkles className="h-4 w-4 mr-2" />
                )}
                {isCurrentTier(tier.tier) ? 'Current Plan' : `Upgrade to ${tier.name}`}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Feature Comparison */}
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="text-center">Feature Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Feature</th>
                  <th className="text-center py-2">Basic</th>
                  <th className="text-center py-2">Elite</th>
                  <th className="text-center py-2">Elite Pro</th>
                </tr>
              </thead>
              <tbody className="space-y-2">
                {Object.entries(ELITE_FEATURE_MATRIX[EliteCommunicationTier.ELITE_PRO]).map(([feature, _]) => (
                  <tr key={feature} className="border-b">
                    <td className="py-2 font-medium">{feature.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</td>
                    <td className="text-center py-2">
                      {(ELITE_FEATURE_MATRIX[EliteCommunicationTier.BASIC] as any)[feature] ?
                        <Check className="h-4 w-4 text-green-500 mx-auto" /> :
                        <X className="h-4 w-4 text-red-500 mx-auto" />
                      }
                    </td>
                    <td className="text-center py-2">
                      {(ELITE_FEATURE_MATRIX[EliteCommunicationTier.ELITE] as any)[feature] ?
                        <Check className="h-4 w-4 text-green-500 mx-auto" /> :
                        <X className="h-4 w-4 text-red-500 mx-auto" />
                      }
                    </td>
                    <td className="text-center py-2">
                      {(ELITE_FEATURE_MATRIX[EliteCommunicationTier.ELITE_PRO] as any)[feature] ?
                        <Check className="h-4 w-4 text-green-500 mx-auto" /> :
                        <X className="h-4 w-4 text-red-500 mx-auto" />
                      }
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Current Subscriptions */}
      {currentSubscriptions.length > 0 && (
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>Your Current Subscriptions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {currentSubscriptions.map((subscription) => (
                <div key={subscription.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="font-medium">{subscription.tier} Tier</div>
                    <div className="text-sm text-gray-500">
                      {subscription.subscriptionType} • {subscription.isActive ? 'Active' : 'Inactive'}
                      {subscription.expiresAt && ` • Expires ${new Date(subscription.expiresAt).toLocaleDateString()}`}
                    </div>
                  </div>
                  <Badge className={subscription.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                    {subscription.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
