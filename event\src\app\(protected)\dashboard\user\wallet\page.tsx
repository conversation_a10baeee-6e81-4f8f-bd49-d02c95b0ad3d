'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Wallet, PlusCircle, Clock, ArrowRight, CreditCard, Download, ChevronLeft, Loader2 } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import Link from 'next/link';
import { RoleGate } from '@/components/auth/role-gate';
import { formatCurrency } from '@/lib/utils';

interface WalletData {
  accountBalance: number;
  availableBalance: number;
  pendingWithdrawals: number;
  recentTransactions: Transaction[];
}

interface Transaction {
  id: string;
  userId: string;
  amount: number;
  type: string;
  description: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export default function UserWalletPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [walletData, setWalletData] = useState<WalletData>({
    accountBalance: 0,
    availableBalance: 0,
    pendingWithdrawals: 0,
    recentTransactions: []
  });

  useEffect(() => {
    const fetchWalletData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/wallet/balance');

        if (!response.ok) {
          throw new Error('Failed to fetch wallet data');
        }

        const data = await response.json();
        setWalletData({
          accountBalance: data.accountBalance || 0,
          availableBalance: data.availableBalance || 0,
          pendingWithdrawals: data.pendingWithdrawals || 0,
          recentTransactions: data.recentTransactions || []
        });
      } catch (error) {
        console.error('Error fetching wallet data:', error);
        setError('Failed to load wallet data. Please try again later.');
        toast({
          title: 'Error',
          description: 'Failed to load wallet data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchWalletData();
  }, []);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  // Get transaction icon based on type
  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'TOP_UP':
      case 'DEPOSIT':
        return <PlusCircle className="h-4 w-4 text-green-600 dark:text-green-400" />;
      case 'WITHDRAWAL':
      case 'PAYMENT':
        return <CreditCard className="h-4 w-4 text-red-600 dark:text-red-400" />;
      case 'TRANSFER':
        return <Wallet className="h-4 w-4 text-blue-600 dark:text-blue-400" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  // Get transaction color based on amount
  const getTransactionColor = (amount: number) => {
    return amount >= 0 ? 'text-green-600' : 'text-red-600';
  };

  return (
    <RoleGate allowedRole="USER">
      <div className="space-y-6">
        <Button variant="ghost" asChild className="-ml-4">
          <Link href="/dashboard/user">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold mb-2">My Wallet</h1>
            <p className="text-gray-500 dark:text-gray-400">
              Manage your balance and transactions
            </p>
          </div>
          <Button className="mt-4 md:mt-0" asChild>
            <Link href="/dashboard/user/wallet/topup">
              <PlusCircle className="mr-2 h-4 w-4" />
              Top Up Balance
            </Link>
          </Button>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-10">
              <div className="rounded-full bg-red-100 p-3 mb-4">
                <Wallet className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium mb-2">Error Loading Wallet</h3>
              <p className="text-gray-500 text-center mb-4">{error}</p>
              <Button onClick={() => router.refresh()}>Try Again</Button>
            </CardContent>
          </Card>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <Card className="md:col-span-2 bg-gradient-to-br from-green-500 to-green-600 text-white">
                <CardContent className="p-6">
                  <div className="flex flex-col">
                    <p className="text-green-100 mb-1">Current Balance</p>
                    <h3 className="text-3xl font-bold mb-4">{formatCurrency(walletData.accountBalance)}</h3>

                    {walletData.pendingWithdrawals > 0 && (
                      <p className="text-sm text-green-100 mb-4">
                        Available Balance: {formatCurrency(walletData.availableBalance)}
                        <span className="ml-2">
                          (Pending Withdrawals: {formatCurrency(walletData.pendingWithdrawals)})
                        </span>
                      </p>
                    )}

                    <div className="flex gap-3 mt-2">
                      <Button variant="secondary" className="bg-white text-green-600 hover:bg-green-50" asChild>
                        <Link href="/dashboard/user/wallet/topup">
                          Top Up Balance
                          <PlusCircle className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="outline" className="text-white border-white hover:bg-green-600" asChild>
                        <Link href="/dashboard/user/wallet/history">
                          Transaction History
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button className="w-full justify-start" variant="outline" asChild>
                    <Link href="/dashboard/user/wallet/topup">
                      <PlusCircle className="mr-2 h-4 w-4" />
                      Top Up Balance
                    </Link>
                  </Button>
                  <Button className="w-full justify-start" variant="outline" asChild>
                    <Link href="/dashboard/user/wallet/history">
                      <Clock className="mr-2 h-4 w-4" />
                      Transaction History
                    </Link>
                  </Button>
                  <Button className="w-full justify-start" variant="outline" asChild>
                    <Link href="/dashboard/user/wallet/payment-methods">
                      <CreditCard className="mr-2 h-4 w-4" />
                      Payment Methods
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Recent Transactions</CardTitle>
                  <CardDescription>Your recent wallet activity</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </CardHeader>
              <CardContent>
                {walletData.recentTransactions.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500">No transactions found</p>
                    <p className="text-sm text-gray-400 mt-1">Your transaction history will appear here</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {walletData.recentTransactions.map((transaction) => (
                      <div key={transaction.id} className="flex justify-between items-center py-2 border-b dark:border-gray-700">
                        <div className="flex items-center">
                          <div className={`p-2 rounded-full mr-3 ${
                            transaction.amount >= 0
                              ? 'bg-green-100 dark:bg-green-900'
                              : 'bg-red-100 dark:bg-red-900'
                          }`}>
                            {getTransactionIcon(transaction.type)}
                          </div>
                          <div>
                            <p className="font-medium">{transaction.description || transaction.type}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">{formatDate(transaction.createdAt)}</p>
                          </div>
                        </div>
                        <p className={`font-medium ${getTransactionColor(transaction.amount)}`}>
                          {transaction.amount >= 0 ? '+' : ''}
                          {formatCurrency(transaction.amount)}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/dashboard/user/wallet/history">
                    View All Transactions
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </>
        )}
      </div>
    </RoleGate>
  );
}
