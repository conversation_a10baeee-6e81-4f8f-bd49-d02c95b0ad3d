'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { AlertCircle, Bluetooth, CreditCard, Info, Check, X } from 'lucide-react';

export default function DeviceApiGuidePage() {
  const [bluetoothStatus, setBluetoothStatus] = useState<'idle' | 'scanning' | 'success' | 'error'>('idle');
  const [bluetoothError, setBluetoothError] = useState<string | null>(null);
  const [bluetoothDevice, setBluetoothDevice] = useState<any>(null);
  
  const [nfcStatus, setNfcStatus] = useState<'idle' | 'scanning' | 'success' | 'error'>('idle');
  const [nfcError, setNfcError] = useState<string | null>(null);
  const [nfcCardId, setNfcCardId] = useState<string | null>(null);

  // Handle Bluetooth scanning - CORRECT implementation
  const handleBluetoothScan = async () => {
    if (!(navigator as any).bluetooth) {
      setBluetoothError('Web Bluetooth API is not supported in this browser');
      setBluetoothStatus('error');
      return;
    }

    setBluetoothStatus('scanning');
    setBluetoothError(null);
    setBluetoothDevice(null);

    try {
      // This must be called directly from a user gesture (like this button click)
      if (!navigator.bluetooth) {
        throw new Error('Web Bluetooth API is not supported in your browser');
      }
      const device = await navigator.bluetooth.requestDevice({
        // Accept all devices
        acceptAllDevices: true,
        // Specify services you need access to
        optionalServices: [
          '00001800-0000-1000-8000-00805f9b34fb', // Generic Access
          '00001801-0000-1000-8000-00805f9b34fb', // Generic Attribute
          '0000180a-0000-1000-8000-00805f9b34fb', // Device Information
        ]
      });

      setBluetoothDevice({
        id: device.id,
        name: device.name || 'Unknown Device',
      });
      
      setBluetoothStatus('success');
      
      toast({
        title: 'Bluetooth Device Selected',
        description: `Selected: ${device.name || 'Unknown Device'}`,
      });
    } catch (error) {
      console.error('Bluetooth error:', error);
      
      // Don't show an error for user cancellation
      if (error instanceof Error && error.message.includes('User cancelled')) {
        setBluetoothStatus('idle');
      } else {
        setBluetoothError(error instanceof Error ? error.message : 'Unknown error occurred');
        setBluetoothStatus('error');
        
        toast({
          title: 'Bluetooth Error',
          description: error instanceof Error ? error.message : 'Failed to scan for Bluetooth devices',
          variant: 'destructive',
        });
      }
    }
  };

  // Handle NFC scanning - CORRECT implementation
  const handleNfcScan = async () => {
    if (typeof window === 'undefined' || !('NDEFReader' in window)) {
      setNfcError('Web NFC API is not supported in this browser');
      setNfcStatus('error');
      return;
    }

    setNfcStatus('scanning');
    setNfcError(null);
    setNfcCardId(null);

    try {
      // This must be called directly from a user gesture (like this button click)
      const ndef = new (window as any).NDEFReader();
      
      // Start scanning - this is the critical part that must be in the user gesture
      await ndef.scan();
      
      toast({
        title: 'NFC Scanner Active',
        description: 'Please tap an NFC card or tag to scan.',
      });
      
      // Set up event listeners
      ndef.addEventListener("reading", ({ serialNumber }: any) => {
        setNfcCardId(serialNumber);
        setNfcStatus('success');
        
        toast({
          title: 'NFC Card Detected',
          description: `Card ID: ${serialNumber}`,
        });
      });
      
      ndef.addEventListener("readingerror", (error: any) => {
        console.error('NFC reading error:', error);
        setNfcError('Error reading NFC card');
        setNfcStatus('error');
        
        toast({
          title: 'NFC Reading Error',
          description: 'Error reading NFC card. Please try again.',
          variant: 'destructive',
        });
      });
    } catch (error) {
      console.error('NFC error:', error);
      setNfcError(error instanceof Error ? error.message : 'Unknown error occurred');
      setNfcStatus('error');
      
      toast({
        title: 'NFC Error',
        description: error instanceof Error ? error.message : 'Failed to start NFC scanner',
        variant: 'destructive',
      });
    }
  };

  // WRONG implementation example (for demonstration only)
  const wrongBluetoothImplementation = () => {
    // This would fail with "Must be handling a user gesture" error
    setTimeout(async () => {
      try {
        const device = await (navigator as any).bluetooth.requestDevice({
          acceptAllDevices: true
        });
        console.log('This will never execute because the API call will fail');
      } catch (error) {
        console.error('This will fail with a user gesture error:', error);
      }
    }, 0);
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold">Device API Guide</h1>
        <p className="text-gray-600 mt-1">
          Learn how to properly use Web Bluetooth and Web NFC APIs
        </p>
      </div>

      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>Important Information</AlertTitle>
        <AlertDescription>
          Both the Web Bluetooth API and Web NFC API require user gestures (like button clicks) to request permissions.
          This guide demonstrates the correct way to implement these features.
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="bluetooth" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="bluetooth">
            <Bluetooth className="mr-2 h-4 w-4" />
            Bluetooth
          </TabsTrigger>
          <TabsTrigger value="nfc">
            <CreditCard className="mr-2 h-4 w-4" />
            NFC
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="bluetooth" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Web Bluetooth API</CardTitle>
              <CardDescription>
                The Web Bluetooth API allows websites to communicate with Bluetooth devices.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Common Error</AlertTitle>
                <AlertDescription>
                  <p className="font-mono text-sm my-2">
                    Failed to execute 'requestDevice' on 'Bluetooth': Must be handling a user gesture to show a permission request
                  </p>
                  <p>This error occurs when you try to call the Bluetooth API outside of a direct user gesture.</p>
                </AlertDescription>
              </Alert>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Correct Implementation</h3>
                <p>Call <code className="text-sm font-mono bg-muted px-1 py-0.5 rounded">navigator.bluetooth.requestDevice()</code> directly in a button click handler:</p>
                
                <div className="bg-muted p-4 rounded-md">
                  <pre className="text-sm overflow-x-auto">
{`// CORRECT - Call directly in the click handler
button.addEventListener('click', async () => {
  try {
    const device = await navigator.bluetooth.requestDevice({
      acceptAllDevices: true
    });
    // Continue with device operations
  } catch (error) {
    console.error('Bluetooth error:', error);
  }
});`}
                  </pre>
                </div>
                
                <h3 className="text-lg font-medium">Incorrect Implementation</h3>
                <p>Using <code className="text-sm font-mono bg-muted px-1 py-0.5 rounded">setTimeout</code> or other async operations before the API call:</p>
                
                <div className="bg-muted p-4 rounded-md">
                  <pre className="text-sm overflow-x-auto">
{`// WRONG - This breaks the user gesture context
button.addEventListener('click', () => {
  setTimeout(async () => {
    // This will fail with "Must be handling a user gesture" error
    const device = await navigator.bluetooth.requestDevice({});
  }, 0);
});`}
                  </pre>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="text-lg font-medium mb-4">Try It Yourself</h3>
                <div className="flex flex-col items-center space-y-4">
                  <Button 
                    onClick={handleBluetoothScan}
                    disabled={bluetoothStatus === 'scanning'}
                    className="w-full max-w-xs"
                  >
                    {bluetoothStatus === 'scanning' ? 'Scanning...' : 'Scan for Bluetooth Devices'}
                  </Button>
                  
                  {bluetoothStatus === 'success' && bluetoothDevice && (
                    <div className="w-full max-w-xs p-4 border rounded-md bg-green-50 border-green-200">
                      <div className="flex items-center">
                        <Check className="h-5 w-5 text-green-500 mr-2" />
                        <h4 className="font-medium">Device Selected</h4>
                      </div>
                      <p className="mt-2"><span className="font-medium">Name:</span> {bluetoothDevice.name}</p>
                      <p><span className="font-medium">ID:</span> {bluetoothDevice.id}</p>
                    </div>
                  )}
                  
                  {bluetoothStatus === 'error' && bluetoothError && (
                    <div className="w-full max-w-xs p-4 border rounded-md bg-red-50 border-red-200">
                      <div className="flex items-center">
                        <X className="h-5 w-5 text-red-500 mr-2" />
                        <h4 className="font-medium">Error</h4>
                      </div>
                      <p className="mt-2">{bluetoothError}</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="nfc" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Web NFC API</CardTitle>
              <CardDescription>
                The Web NFC API allows websites to read and write to NFC tags when they are in close proximity.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Common Error</AlertTitle>
                <AlertDescription>
                  <p>Similar to Bluetooth, NFC operations must be initiated by a user gesture.</p>
                  <p className="font-mono text-sm my-2">
                    Failed to execute 'scan' on 'NDEFReader': Must be handling a user gesture to show a permission request
                  </p>
                </AlertDescription>
              </Alert>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Correct Implementation</h3>
                <p>Call <code className="text-sm font-mono bg-muted px-1 py-0.5 rounded">ndef.scan()</code> directly in a button click handler:</p>
                
                <div className="bg-muted p-4 rounded-md">
                  <pre className="text-sm overflow-x-auto">
{`// CORRECT - Call directly in the click handler
button.addEventListener('click', async () => {
  try {
    const ndef = new NDEFReader();
    await ndef.scan(); // This must be in the user gesture
    
    ndef.addEventListener("reading", ({ serialNumber }) => {
      console.log("NFC tag detected:", serialNumber);
    });
  } catch (error) {
    console.error('NFC error:', error);
  }
});`}
                  </pre>
                </div>
                
                <h3 className="text-lg font-medium">Incorrect Implementation</h3>
                <p>Calling NFC operations in useEffect or other non-user-gesture contexts:</p>
                
                <div className="bg-muted p-4 rounded-md">
                  <pre className="text-sm overflow-x-auto">
{`// WRONG - Not tied to a user gesture
useEffect(() => {
  const setupNFC = async () => {
    const ndef = new NDEFReader();
    await ndef.scan(); // This will fail
  };
  
  setupNFC();
}, []);`}
                  </pre>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="text-lg font-medium mb-4">Try It Yourself</h3>
                <div className="flex flex-col items-center space-y-4">
                  <Button 
                    onClick={handleNfcScan}
                    disabled={nfcStatus === 'scanning'}
                    className="w-full max-w-xs"
                  >
                    {nfcStatus === 'scanning' ? 'Scanning for NFC...' : 'Start NFC Scanner'}
                  </Button>
                  
                  {nfcStatus === 'scanning' && (
                    <div className="w-full max-w-xs p-4 border rounded-md bg-blue-50 border-blue-200">
                      <p className="text-center">NFC scanner active. Please tap an NFC card or tag.</p>
                    </div>
                  )}
                  
                  {nfcStatus === 'success' && nfcCardId && (
                    <div className="w-full max-w-xs p-4 border rounded-md bg-green-50 border-green-200">
                      <div className="flex items-center">
                        <Check className="h-5 w-5 text-green-500 mr-2" />
                        <h4 className="font-medium">NFC Card Detected</h4>
                      </div>
                      <p className="mt-2"><span className="font-medium">Card ID:</span> {nfcCardId}</p>
                    </div>
                  )}
                  
                  {nfcStatus === 'error' && nfcError && (
                    <div className="w-full max-w-xs p-4 border rounded-md bg-red-50 border-red-200">
                      <div className="flex items-center">
                        <X className="h-5 w-5 text-red-500 mr-2" />
                        <h4 className="font-medium">Error</h4>
                      </div>
                      <p className="mt-2">{nfcError}</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <Card>
        <CardHeader>
          <CardTitle>Additional Resources</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p>
            <a href="https://developer.mozilla.org/en-US/docs/Web/API/Bluetooth/requestDevice" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
              MDN Web Docs: Bluetooth.requestDevice()
            </a>
          </p>
          <p>
            <a href="https://developer.chrome.com/docs/capabilities/bluetooth" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
              Chrome Developers: Web Bluetooth API
            </a>
          </p>
          <p>
            <a href="https://developer.chrome.com/docs/capabilities/nfc" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
              Chrome Developers: Web NFC API
            </a>
          </p>
        </CardContent>
        <CardFooter>
          <p className="text-sm text-gray-500">
            Note: Both Web Bluetooth and Web NFC APIs are only available in secure contexts (HTTPS) and have limited browser support.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
