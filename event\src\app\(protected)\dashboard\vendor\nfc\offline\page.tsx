'use client';

import { useEffect, useState } from 'react';
import { NFCService } from '@/lib/services/nfc-service';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { AlertCircle, CheckCircle, CloudOff, RefreshCw, WifiOff } from 'lucide-react';

export default function OfflineModePage() {
  const [isOnline, setIsOnline] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [offlineCount, setOfflineCount] = useState(0);
  const [lastSyncTime, setLastSyncTime] = useState<string | null>(null);

  // Monitor online status
  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
    updateOnlineStatus();

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  // Load initial offline transaction count
  useEffect(() => {
    const count = NFCService.getOfflineTransactionCount();
    setOfflineCount(count);

    // Load last sync time from localStorage
    const storedLastSync = localStorage.getItem('lastOfflineSync');
    if (storedLastSync) {
      setLastSyncTime(storedLastSync);
    }
  }, []);

  // Handle manual sync
  const handleSync = async () => {
    if (!isOnline) {
      toast({
        title: "Cannot sync while offline",
        description: "Please check your internet connection and try again.",
        variant: "destructive",
      });
      return;
    }

    setIsSyncing(true);
    try {
      const result = await NFCService.synchronizeOfflineTransactions();
      if (result.success) {
        toast({
          title: "Sync successful",
          description: `${result.syncedCount} transactions synchronized`,
        });
        setOfflineCount(0);
        setLastSyncTime(new Date().toISOString());
        localStorage.setItem('lastOfflineSync', new Date().toISOString());
      } else {
        toast({
          title: "Sync failed",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Sync error",
        description: "An unexpected error occurred while syncing",
        variant: "destructive",
      });
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Offline Mode</h1>
          <p className="text-muted-foreground">Manage offline transactions and synchronization</p>
        </div>
        <Badge variant={isOnline ? "success" : "destructive"} className="h-8 px-3 py-1.5">
          {isOnline ? (
            <CheckCircle className="h-4 w-4 mr-1" />
          ) : (
            <WifiOff className="h-4 w-4 mr-1" />
          )}
          {isOnline ? "Online" : "Offline"}
        </Badge>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Offline Transaction Queue</CardTitle>
            <CardDescription>Transactions waiting to be synced</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Pending Transactions</span>
              <Badge variant={offlineCount > 0 ? "secondary" : "outline"}>
                {offlineCount}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Last Sync</span>
              <span>{lastSyncTime ? new Date(lastSyncTime).toLocaleString() : 'Never'}</span>
            </div>
          </CardContent>
          <CardFooter>
            <Button 
              className="w-full" 
              onClick={handleSync}
              disabled={!isOnline || isSyncing || offlineCount === 0}
            >
              {isSyncing ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Sync Now
                </>
              )}
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Offline Mode Status</CardTitle>
            <CardDescription>Current system status and information</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-secondary/10 rounded-lg space-y-4">
              <div className="flex items-start">
                <CloudOff className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
                <div>
                  <h4 className="font-medium">How Offline Mode Works</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    When offline, transactions are stored locally and will be automatically 
                    synchronized when internet connection is restored.
                  </p>
                </div>
              </div>
              <Separator />
              <div className="flex items-start">
                <AlertCircle className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
                <div>
                  <h4 className="font-medium">Important Notes</h4>
                  <ul className="text-sm text-muted-foreground mt-1 list-disc list-inside space-y-1">
                    <li>All offline transactions are encrypted and stored securely</li>
                    <li>Maximum offline transaction limit: 1000 transactions</li>
                    <li>Transactions are automatically synced when online</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}