'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { 
  CreditCard, 
  Search, 
  Loader2, 
  Calendar, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Download, 
  Filter, 
  ChevronLeft, 
  ChevronRight,
  FileText,
  Printer
} from 'lucide-react';
import TransactionReceipt from '@/components/vendor/nfc/TransactionReceipt';

// Define the transaction interface
interface Transaction {
  id: string;
  amount: number;
  createdAt: string;
  status: 'COMPLETED' | 'FAILED' | 'PENDING';
  cardId: string;
  customerName: string;
  customerEmail: string;
  eventId: string;
  eventName: string;
  vendorId: string;
  vendorName: string;
  products: Array<{
    productId: string;
    name: string;
    price: number;
    quantity: number;
  }>;
}

// Constants
const PAGE_SIZE = 10;
const STATUS_OPTIONS = [
  { value: 'ALL', label: 'All Statuses' },
  { value: 'COMPLETED', label: 'Completed' },
  { value: 'FAILED', label: 'Failed' },
  { value: 'PENDING', label: 'Pending' }
];
const DATE_OPTIONS = [
  { value: 'ALL', label: 'All Time' },
  { value: 'TODAY', label: 'Today' },
  { value: 'YESTERDAY', label: 'Yesterday' },
  { value: 'THIS_WEEK', label: 'This Week' },
  { value: 'THIS_MONTH', label: 'This Month' }
];

export default function NFCReceiptsPage() {
  const router = useRouter();
  
  // State variables
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [dateFilter, setDateFilter] = useState('ALL');
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [showReceipt, setShowReceipt] = useState(false);
  
  // Fetch transactions on component mount and when filters change
  useEffect(() => {
    fetchTransactions();
  }, [currentPage, statusFilter, dateFilter]);
  
  // Function to fetch transactions
  async function fetchTransactions() {
    try {
      setLoading(true);
      
      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', currentPage.toString());
      params.append('pageSize', PAGE_SIZE.toString());
      
      if (searchQuery) {
        params.append('search', searchQuery);
      }
      
      if (statusFilter !== 'ALL') {
        params.append('status', statusFilter);
      }
      
      if (dateFilter !== 'ALL') {
        params.append('dateFilter', dateFilter);
      }
      
      const response = await fetch(`/api/vendors/nfc/transactions?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch transactions');
      }
      
      const data = await response.json();
      
      setTransactions(data.transactions);
      setTotalPages(data.totalPages);
      setTotalTransactions(data.totalTransactions);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast({
        title: 'Error',
        description: 'Failed to load transactions. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }
  
  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page
    fetchTransactions();
  };
  
  // Handle export functionality
  const handleExport = () => {
    toast({
      title: 'Export Started',
      description: 'Your transaction data is being exported...',
    });
    
    // In a real implementation, this would trigger a download
    setTimeout(() => {
      toast({
        title: 'Export Complete',
        description: 'Your transaction data has been exported successfully.',
      });
    }, 1500);
  };
  
  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        );
      case 'FAILED':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="h-3 w-3 mr-1" />
            Failed
          </Badge>
        );
      case 'PENDING':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };
  
  // Handle viewing a receipt
  const handleViewReceipt = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setShowReceipt(true);
  };
  
  // Handle closing the receipt
  const handleCloseReceipt = () => {
    setShowReceipt(false);
    setSelectedTransaction(null);
  };
  
  // Handle refund (placeholder)
  const handleRefund = async (transactionId: string) => {
    try {
      toast({
        title: 'Processing Refund',
        description: 'Please wait while we process the refund...',
      });
      
      // In a real implementation, this would call an API endpoint
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast({
        title: 'Refund Successful',
        description: 'The transaction has been refunded successfully.',
      });
      
      // Refresh transactions
      fetchTransactions();
    } catch (error) {
      console.error('Error processing refund:', error);
      toast({
        title: 'Refund Failed',
        description: 'Failed to process the refund. Please try again.',
        variant: 'destructive',
      });
    }
  };
  
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold">Transaction Receipts</h1>
          <p className="text-gray-600 mt-1">
            View and manage your transaction receipts
          </p>
        </div>
        
        <Button 
          variant="outline"
          onClick={handleExport}
        >
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
      </div>
      
      {showReceipt && selectedTransaction ? (
        <div className="mb-8">
          <Button 
            variant="outline" 
            onClick={handleCloseReceipt}
            className="mb-4"
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Receipts
          </Button>
          
          <TransactionReceipt
            transactionId={selectedTransaction.id}
            timestamp={selectedTransaction.createdAt}
            cardId={selectedTransaction.cardId}
            products={selectedTransaction.products.map(p => ({ ...p, id: p.productId }))}
            total={selectedTransaction.amount}
            eventName={selectedTransaction.eventName}
            vendorName={selectedTransaction.vendorName}
            onClose={handleCloseReceipt}
            onRefund={async () => await handleRefund(selectedTransaction.id)}
          />
        </div>
      ) : (
        <>
          <Card>
            <CardHeader>
              <CardTitle>Transaction Receipts</CardTitle>
              <CardDescription>
                View and print receipts for your NFC transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
                <form 
                  className="relative w-full md:w-1/3"
                  onSubmit={handleSearch}
                >
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                  <Input
                    placeholder="Search by event, customer..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </form>
                
                <div className="flex flex-col sm:flex-row gap-2">
                  <Select
                    value={statusFilter}
                    onValueChange={setStatusFilter}
                  >
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      {STATUS_OPTIONS.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <Select
                    value={dateFilter}
                    onValueChange={setDateFilter}
                  >
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Filter by date" />
                    </SelectTrigger>
                    <SelectContent>
                      {DATE_OPTIONS.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Loading transactions...</span>
                </div>
              ) : transactions.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Transactions Found</h3>
                  <p className="text-gray-500 mb-4">
                    {searchQuery || statusFilter !== 'ALL' || dateFilter !== 'ALL'
                      ? 'Try adjusting your filters to see more results'
                      : 'You haven\'t processed any NFC transactions yet'}
                  </p>
                  {(searchQuery || statusFilter !== 'ALL' || dateFilter !== 'ALL') && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSearchQuery('');
                        setStatusFilter('ALL');
                        setDateFilter('ALL');
                        setCurrentPage(1);
                        fetchTransactions();
                      }}
                    >
                      <Filter className="mr-2 h-4 w-4" />
                      Clear Filters
                    </Button>
                  )}
                </div>
              ) : (
                <>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Customer</TableHead>
                          <TableHead>Event</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {transactions.map(transaction => (
                          <TableRow key={transaction.id}>
                            <TableCell className="font-medium">
                              {formatDate(transaction.createdAt)}
                            </TableCell>
                            <TableCell>{transaction.customerName}</TableCell>
                            <TableCell>{transaction.eventName}</TableCell>
                            <TableCell>${transaction.amount.toFixed(2)}</TableCell>
                            <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleViewReceipt(transaction)}
                                >
                                  <FileText className="h-4 w-4 mr-1" />
                                  Receipt
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    handleViewReceipt(transaction);
                                    // In a real implementation, this would trigger printing directly
                                    setTimeout(() => {
                                      toast({
                                        title: 'Print Dialog',
                                        description: 'Print dialog would open here',
                                      });
                                    }, 500);
                                  }}
                                >
                                  <Printer className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                  
                  <div className="flex items-center justify-between mt-6">
                    <p className="text-sm text-gray-500">
                      Showing {transactions.length} of {totalTransactions} transactions
                    </p>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <p className="text-sm">
                        Page {currentPage} of {totalPages}
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
