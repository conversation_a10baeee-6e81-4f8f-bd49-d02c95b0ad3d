'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Loader2, CreditCard, AlertCircle } from 'lucide-react';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface Event {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  venue: string;
  city: string;
  participationStatus: string;
}

interface VendorProfile {
  id: string;
  businessName: string;
  verificationStatus: string;
}

export default function NFCTerminalPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [vendorProfile, setVendorProfile] = useState<VendorProfile | null>(null);
  const [activeEvents, setActiveEvents] = useState<Event[]>([]);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);

        // Fetch vendor profile
        const profileResponse = await fetch('/api/vendors/profile');

        if (!profileResponse.ok) {
          if (profileResponse.status === 404) {
            router.push('/dashboard/vendor/create-profile');
            return;
          }
          throw new Error('Failed to fetch vendor profile');
        }

        const profileData = await profileResponse.json();
        setVendorProfile(profileData);

        // Check if vendor is verified
        if (profileData.verificationStatus !== 'APPROVED') {
          setError('Your vendor account is not verified. Please complete the verification process to access the NFC terminal.');
          setLoading(false);
          return;
        }

        // Fetch vendor events
        const eventsResponse = await fetch('/api/vendors/events');

        if (!eventsResponse.ok) {
          throw new Error('Failed to fetch events');
        }

        const eventsData = await eventsResponse.json();
        setEvents(eventsData);

        // Filter active events (approved participation and event is ongoing or upcoming)
        const now = new Date();
        const active = eventsData.filter((event: Event) =>
          event.participationStatus === 'APPROVED' &&
          new Date(event.endDate) >= now
        );
        setActiveEvents(active);

      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load required data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [router]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  if (error || !vendorProfile) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center text-red-500">
            <AlertCircle className="h-5 w-5 mr-2" />
            Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error || 'Failed to load vendor profile'}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => router.push('/dashboard/vendor')}
          >
            Return to Dashboard
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (activeEvents.length === 0) {
    return (
      <div className="container mx-auto py-8 px-4">
        <h1 className="text-2xl font-bold mb-6">NFC Terminal</h1>

        <Card className="w-full max-w-3xl mx-auto">
          <CardHeader>
            <CardTitle>No Active Events</CardTitle>
            <CardDescription>
              You don't have any active events where you can process NFC transactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <CreditCard className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Active Events Found</h3>
              <p className="text-gray-500 mb-4">You need to participate in an event to process NFC transactions</p>
              <Button
                onClick={() => router.push('/dashboard/vendor/events')}
              >
                View All Events
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">NFC Terminal</h1>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Select Event</CardTitle>
          <CardDescription>
            Choose an event to process NFC transactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue={activeEvents[0].id} className="w-full">
            <TabsList className="mb-4">
              {activeEvents.map(event => (
                <TabsTrigger key={event.id} value={event.id}>
                  {event.title}
                </TabsTrigger>
              ))}
            </TabsList>

            {activeEvents.map(event => (
              <TabsContent key={event.id} value={event.id}>
                <Card>
                  <CardHeader>
                    <CardTitle>{event.title}</CardTitle>
                    <CardDescription>
                      {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}
                      <br />
                      {event.venue}, {event.city}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-4">
                      <Button
                        size="lg"
                        onClick={() => router.push(`/dashboard/vendor/nfc-terminal?eventId=${event.id}`)}
                        className="w-full md:w-auto"
                      >
                        <CreditCard className="mr-2 h-5 w-5" />
                        Open NFC Terminal for this Event
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
