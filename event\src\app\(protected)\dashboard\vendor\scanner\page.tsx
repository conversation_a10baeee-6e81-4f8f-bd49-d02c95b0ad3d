'use client';

import { useState } from 'react';
import DeviceScanner from '@/components/vendor/device-scanner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';

export default function ScannerPage() {
  const [selectedBluetoothDevice, setSelectedBluetoothDevice] = useState<any>(null);
  const [detectedNfcCard, setDetectedNfcCard] = useState<string | null>(null);

  const handleBluetoothDeviceSelected = (device: any) => {
    setSelectedBluetoothDevice(device);
    toast({
      title: 'Bluetooth Device Selected',
      description: `Selected: ${device.name}`,
    });
  };

  const handleNfcCardDetected = (cardId: string) => {
    setDetectedNfcCard(cardId);
    toast({
      title: 'NFC Card Detected',
      description: `Card ID: ${cardId}`,
    });
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold"><PERSON><PERSON>anner</h1>
        <p className="text-gray-600 mt-1">
          Scan for Bluetooth devices or NFC cards
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <DeviceScanner
            onBluetoothDeviceSelected={handleBluetoothDeviceSelected}
            onNfcCardDetected={handleNfcCardDetected}
          />
        </div>

        <div className="space-y-6">
          {selectedBluetoothDevice && (
            <Card>
              <CardHeader>
                <CardTitle>Selected Bluetooth Device</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p><span className="font-medium">Name:</span> {selectedBluetoothDevice.name}</p>
                  <p><span className="font-medium">ID:</span> {selectedBluetoothDevice.id}</p>
                  <p><span className="font-medium">Type:</span> {selectedBluetoothDevice.isPrinter ? 'Printer' : 'Other Device'}</p>
                  {selectedBluetoothDevice.info && (
                    <p><span className="font-medium">Info:</span> {selectedBluetoothDevice.info}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {detectedNfcCard && (
            <Card>
              <CardHeader>
                <CardTitle>Detected NFC Card</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p><span className="font-medium">Card ID:</span> {detectedNfcCard}</p>
                  <p><span className="font-medium">Detected At:</span> {new Date().toLocaleString()}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
