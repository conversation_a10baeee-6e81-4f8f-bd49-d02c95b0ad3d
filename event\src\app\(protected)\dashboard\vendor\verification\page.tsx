'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import EnhancedVendorVerificationForm from '@/components/vendor/verification/enhanced-verification-form';
import { Loader2 } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { RoleGate } from '@/components/auth/role-gate';

export default function VendorVerificationPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function checkVerificationStatus() {
      try {
        const response = await fetch('/api/vendors/verification/status');

        if (response.ok) {
          // Verification exists, redirect to status page
          router.push('/dashboard/vendor/verification/status');
          return;
        }

        // If 404, verification doesn't exist, show the form
        if (response.status === 404) {
          setLoading(false);
          return;
        }

        // Handle other errors
        throw new Error('Failed to check verification status');
      } catch (error) {
        console.error('Error checking verification status:', error);
        setLoading(false);
        toast({
          title: 'Error',
          description: 'Failed to check verification status',
          variant: 'destructive',
        });
      }
    }

    checkVerificationStatus();
  }, [router]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  return (
    <RoleGate allowedRole="VENDOR">
      <EnhancedVendorVerificationForm />
    </RoleGate>
  );
}
