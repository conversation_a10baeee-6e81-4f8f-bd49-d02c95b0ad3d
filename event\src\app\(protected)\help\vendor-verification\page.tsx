"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, AlertCircle, Clock, Store, Calendar } from 'lucide-react';

export default function VendorVerificationHelpPage() {
  return (
    <div className="container mx-auto py-10 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Vendor Verification Process</h1>
      <p className="text-lg text-gray-700 mb-8">
        Our platform uses a two-step process to ensure quality and trust for all participants.
      </p>

      <div className="grid gap-8 mb-10">
        <Card>
          <CardHeader className="bg-blue-50">
            <div className="flex items-center gap-3">
              <Store className="h-8 w-8 text-blue-600" />
              <div>
                <CardTitle>Step 1: Admin Verification</CardTitle>
                <CardDescription>One-time platform verification</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <p className="mb-4">
              Before you can apply to any events, your vendor account must be verified by our admin team. This is a one-time process that verifies your business credentials.
            </p>
            
            <h3 className="font-semibold text-lg mt-6 mb-3">What gets verified:</h3>
            <ul className="list-disc pl-6 space-y-2">
              <li>Business registration documents</li>
              <li>Tax identification numbers</li>
              <li>Business licenses and permits</li>
              <li>Contact information</li>
            </ul>

            <h3 className="font-semibold text-lg mt-6 mb-3">Verification statuses:</h3>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-amber-500" />
                <div>
                  <p className="font-medium">Pending</p>
                  <p className="text-sm text-gray-600">Your documents are being reviewed by our admin team</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="font-medium">Approved</p>
                  <p className="text-sm text-gray-600">Your vendor account is verified and you can apply to events</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <div>
                  <p className="font-medium">Rejected</p>
                  <p className="text-sm text-gray-600">Your verification was rejected - you can resubmit with updated documents</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="bg-green-50">
            <div className="flex items-center gap-3">
              <Calendar className="h-8 w-8 text-green-600" />
              <div>
                <CardTitle>Step 2: Event Application</CardTitle>
                <CardDescription>Apply to specific events</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-6">
            <p className="mb-4">
              Once your vendor account is verified, you can apply to participate in specific events. Each application is reviewed by the event organizer.
            </p>
            
            <h3 className="font-semibold text-lg mt-6 mb-3">Application process:</h3>
            <ol className="list-decimal pl-6 space-y-2">
              <li>Browse available events</li>
              <li>Submit an application with your booth/product details</li>
              <li>Wait for the event organizer to review your application</li>
              <li>If approved, you'll receive booth assignment and event details</li>
            </ol>

            <h3 className="font-semibold text-lg mt-6 mb-3">Application statuses:</h3>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-amber-500" />
                <div>
                  <p className="font-medium">Pending</p>
                  <p className="text-sm text-gray-600">Your application is being reviewed by the event organizer</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="font-medium">Approved</p>
                  <p className="text-sm text-gray-600">Your application has been approved by the event organizer</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <div>
                  <p className="font-medium">Rejected</p>
                  <p className="text-sm text-gray-600">Your application was rejected by the event organizer</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator className="my-8" />

      <h2 className="text-2xl font-bold mb-4">Frequently Asked Questions</h2>
      <div className="space-y-6">
        <div>
          <h3 className="font-semibold text-lg">Why do I need admin verification?</h3>
          <p className="text-gray-700">
            Admin verification ensures that all vendors on our platform are legitimate businesses. This creates trust for event organizers and attendees.
          </p>
        </div>
        <div>
          <h3 className="font-semibold text-lg">How long does verification take?</h3>
          <p className="text-gray-700">
            Admin verification typically takes 1-3 business days. Event application approvals depend on the event organizer.
          </p>
        </div>
        <div>
          <h3 className="font-semibold text-lg">Can I apply to events before being verified?</h3>
          <p className="text-gray-700">
            No, you must complete the admin verification process before you can apply to any events.
          </p>
        </div>
        <div>
          <h3 className="font-semibold text-lg">What if my verification is rejected?</h3>
          <p className="text-gray-700">
            You'll receive feedback on why your verification was rejected. You can update your documents and resubmit for verification.
          </p>
        </div>
      </div>
    </div>
  );
}
