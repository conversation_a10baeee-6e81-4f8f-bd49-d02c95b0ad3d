'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useCurrentUser } from '@/hooks/use-current-user';
import {
  Users,
  Building,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Mail
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';

// Invitation interface
interface Invitation {
  id: string;
  email: string;
  role: string;
  status: string;
  expiresAt: string;
  createdAt: string;
  team: {
    id: string;
    name: string;
    description?: string;
    owner: {
      id: string;
      name: string;
      email: string;
      image: string;
    };
    _count: {
      members: number;
      events: number;
    };
  };
  invitedBy: {
    id: string;
    name: string;
    email: string;
    image: string;
  };
}

export default function InvitationPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);
  const router = useRouter();
  const user = useCurrentUser();
  const [invitation, setInvitation] = useState<Invitation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch invitation data
  useEffect(() => {
    const fetchInvitation = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/teams/invitations/${id}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError('Invitation not found');
            return;
          }
          if (response.status === 410) {
            const data = await response.json();
            setError(data.error || 'Invitation has expired');
            setInvitation(data.invitation);
            return;
          }
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        setInvitation(data.invitation);
      } catch (error) {
        console.error('Error fetching invitation:', error);
        setError('Failed to load invitation. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchInvitation();
  }, [id]);

  // Handle accept invitation
  const handleAcceptInvitation = async () => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to accept an invitation',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/teams/invitations/${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'accept',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: 'You have successfully joined the team',
      });

      // Redirect to the team page
      router.push(`/dashboard/organizer/team/${data.teamId}`);
    } catch (error) {
      console.error('Error accepting invitation:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to accept invitation',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle decline invitation
  const handleDeclineInvitation = async () => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to decline an invitation',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/teams/invitations/${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'decline',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      toast({
        title: 'Success',
        description: 'You have declined the invitation',
      });

      // Redirect to the dashboard
      router.push('/dashboard');
    } catch (error) {
      console.error('Error declining invitation:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to decline invitation',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get role display name
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'ORGANIZER_ADMIN':
        return 'Admin';
      case 'ORGANIZER_MANAGER':
        return 'Manager';
      case 'ORGANIZER_EDITOR':
        return 'Editor';
      case 'ORGANIZER_ANALYST':
        return 'Analyst';
      case 'ORGANIZER_SUPPORT':
        return 'Support';
      default:
        return role;
    }
  };

  // Get role description
  const getRoleDescription = (role: string) => {
    switch (role) {
      case 'ORGANIZER_ADMIN':
        return 'Full access to manage the team, events, and members.';
      case 'ORGANIZER_MANAGER':
        return 'Can create and edit events, and invite members.';
      case 'ORGANIZER_EDITOR':
        return 'Can edit event details but cannot create new events.';
      case 'ORGANIZER_ANALYST':
        return 'View-only access to events and analytics.';
      case 'ORGANIZER_SUPPORT':
        return 'Can manage attendees and tickets.';
      default:
        return '';
    }
  };

  // Get user initials for avatar
  const getUserInitials = (name: string) => {
    if (!name) return '??';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <div className="flex justify-center mb-4">
              <div className="rounded-full bg-red-100 p-3">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
            </div>
            <CardTitle className="text-center">Invitation Error</CardTitle>
            <CardDescription className="text-center">
              {error}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {invitation && (
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-4">
                  This invitation to join {invitation.team.name} has expired or is no longer valid.
                </p>
                <p className="text-sm text-gray-500">
                  Please contact {invitation.invitedBy.name || invitation.invitedBy.email} for a new invitation.
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button asChild>
              <Link href="/dashboard">
                Go to Dashboard
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (!invitation) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Invitation Not Found</h2>
          <p className="text-gray-500 mb-6">The invitation you are looking for does not exist or has been removed.</p>
          <Button asChild>
            <Link href="/dashboard">
              Go to Dashboard
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  // Check if the invitation is for the current user
  const isForCurrentUser = user?.email === invitation.email;

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <div className="flex justify-center mb-4">
            <div className="rounded-full bg-blue-100 p-3">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <CardTitle className="text-center">Team Invitation</CardTitle>
          <CardDescription className="text-center">
            You've been invited to join a team
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <Avatar className="h-10 w-10 mr-3">
                  <AvatarImage src={invitation.invitedBy.image || ''} alt={invitation.invitedBy.name || 'Inviter'} />
                  <AvatarFallback>{getUserInitials(invitation.invitedBy.name || '')}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{invitation.invitedBy.name || 'Unnamed User'}</div>
                  <div className="text-sm text-gray-500">{invitation.invitedBy.email}</div>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                has invited you to join their team:
              </p>
            </div>

            <div className="border rounded-lg p-4">
              <div className="flex items-center mb-3">
                <Building className="h-6 w-6 mr-2 text-gray-400" />
                <div className="font-medium text-lg">{invitation.team.name}</div>
              </div>

              {invitation.team.description && (
                <p className="text-sm text-gray-600 mb-3">
                  {invitation.team.description}
                </p>
              )}

              <div className="flex items-center text-sm text-gray-500 mb-2">
                <Users className="h-4 w-4 mr-2" />
                <span>{invitation.team._count.members} members</span>
              </div>

              <div className="flex items-center text-sm text-gray-500">
                <Clock className="h-4 w-4 mr-2" />
                <span>Expires on {format(new Date(invitation.expiresAt), 'PPP')}</span>
              </div>
            </div>

            <div className="border rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                  {getRoleDisplayName(invitation.role)}
                </Badge>
              </div>
              <p className="text-sm text-gray-600">
                {getRoleDescription(invitation.role)}
              </p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-3">
          {!user ? (
            <div className="text-center w-full">
              <p className="text-sm text-gray-500 mb-3">
                You need to sign in to accept this invitation
              </p>
              <Button asChild className="w-full">
                <Link href={`/auth/login?callbackUrl=${encodeURIComponent(`/invitations/${id}`)}`}>
                  Sign In
                </Link>
              </Button>
            </div>
          ) : !isForCurrentUser ? (
            <div className="text-center w-full">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-3">
                <div className="flex items-start">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm text-yellow-800 font-medium">Wrong Account</p>
                    <p className="text-sm text-yellow-700">
                      This invitation was sent to {invitation.email}, but you're signed in as {user.email}.
                    </p>
                  </div>
                </div>
              </div>
              <Button asChild variant="outline" className="w-full">
                <Link href="/api/auth/direct-signout">
                  Sign Out
                </Link>
              </Button>
            </div>
          ) : (
            <div className="flex space-x-3 w-full">
              <Button
                variant="outline"
                className="flex-1"
                onClick={handleDeclineInvitation}
                disabled={isSubmitting}
              >
                <XCircle className="h-4 w-4 mr-2" />
                Decline
              </Button>
              <Button
                className="flex-1"
                onClick={handleAcceptInvitation}
                disabled={isSubmitting}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                {isSubmitting ? 'Processing...' : 'Accept'}
              </Button>
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
