import { Navbar } from "../../components/ui/dashboardui/navbar";
import { Toaster } from "sonner";
import { Sidebar } from "../../components/ui/dashboardui/sidebar";
import { redirect } from "next/navigation";
import { getSession } from "@/auth";
import { AuthSessionProvider } from "@/components/providers/session-provider";
// import { RoleCookieSync } from "@/components/auth/role-cookie-sync";
import { SubscriptionProvider } from "@/contexts/subscription-context";
import { DashboardLayoutClient } from "@/components/dashboard/dashboard-layout-client";

export default async function ProtectedLayout({ children }: { children: React.ReactNode }) {
  // Get the session using our custom getSession function
  const session = await getSession();

  // If there's no session, redirect to login
  if (!session) {
    redirect('/auth/login');
  }

  return (
    <AuthSessionProvider session={session}>
      <SubscriptionProvider>
        <div className="min-h-screen bg-gray-50">
          {/* User synchronization components */}
          {/* <UserSync /> */}
          {/* <RoleCookieSync /> */}

          <DashboardLayoutClient>
            {children}
          </DashboardLayoutClient>

          <Toaster richColors closeButton />
        </div>
      </SubscriptionProvider>
    </AuthSessionProvider>
  );
}
