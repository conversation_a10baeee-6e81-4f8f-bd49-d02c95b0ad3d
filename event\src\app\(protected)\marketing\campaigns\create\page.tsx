import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import { CampaignManager } from '@/components/marketing/campaign-manager';

// Sample data
const events = [
  {
    id: '1',
    name: 'Summer Music Festival',
    date: 'July 10-12, 2024',
    location: 'Central Park, New York',
    imageUrl: 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
  },
  {
    id: '2',
    name: 'Tech Conference 2024',
    date: 'June 15-17, 2024',
    location: 'Convention Center, New York',
    imageUrl: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
  },
  {
    id: '3',
    name: 'Food & Wine Festival',
    date: 'August 5-7, 2024',
    location: 'Waterfront Park, San Francisco',
    imageUrl: 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
  },
];

const audienceSegments = [
  {
    id: '1',
    name: 'Active Users',
    description: 'Users who have logged in within the last 30 days',
    count: 1245,
  },
  {
    id: '2',
    name: 'Event Organizers',
    description: 'All registered event organizers',
    count: 387,
  },
  {
    id: '3',
    name: 'New Users',
    description: 'Users who registered within the last 7 days',
    count: 156,
  },
  {
    id: '4',
    name: 'Ticket Buyers',
    description: 'Users who have purchased tickets in the last 90 days',
    count: 892,
  },
];

export default function CreateCampaignPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild className="mb-4">
          <Link href="/marketing/campaigns">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Campaigns
          </Link>
        </Button>
      </div>
      
      <CampaignManager 
        events={events.map(event => ({
          id: event.id,
          title: event.name,
          startDate: event.date,
          endDate: event.date,
          venue: event.location,
          location: event.location,
          imageUrl: event.imageUrl
        }))}
        audienceSegments={audienceSegments}
      />
    </div>
  );
}
