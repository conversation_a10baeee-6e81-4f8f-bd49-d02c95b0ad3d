'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Plus, Mail, Send, Clock, CheckCircle, AlertTriangle, Calendar, Users, Search, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Define campaign types
type CampaignStatus = 'SENT' | 'SCHEDULED' | 'ACTIVE' | 'DRAFT';

interface Campaign {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: CampaignStatus;
  content?: string;
  subject?: string;
  previewText?: string;
  audienceType?: string;
  audienceSegment?: string;
  sentAt?: string;
  scheduledDate?: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  eventId?: string | null;
  event?: {
    id: string;
    title: string;
    startDate: string;
    endDate: string;
    imagePath?: string;
  };
  recipients: {
    id: string;
    sentAt?: string;
    openedAt?: string;
    clickedAt?: string;
  }[];
  metrics?: {
    totalRecipients: number;
    openCount: number;
    clickCount: number;
    openRate: number;
    clickRate: number;
  };
}

// Define API response type
interface CampaignResponse {
  campaigns: Campaign[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export default function CampaignsPage() {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentTab, setCurrentTab] = useState('all');

  // Fetch campaigns from the API
  useEffect(() => {
    const fetchCampaigns = async () => {
      try {
        setLoading(true);
        setError(null);

        // Build query parameters
        const params = new URLSearchParams();
        if (searchQuery) params.append('search', searchQuery);
        if (statusFilter !== 'all') params.append('status', statusFilter.toUpperCase());

        const response = await fetch(`/api/emails/campaigns?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`Error fetching campaigns: ${response.status}`);
        }

        const data: CampaignResponse = await response.json();
        setCampaigns(data.campaigns);
      } catch (err) {
        console.error('Error fetching campaigns:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching campaigns');
      } finally {
        setLoading(false);
      }
    };

    fetchCampaigns();
  }, [searchQuery, statusFilter]);
  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Email Campaigns</h1>
          <p className="text-gray-600">
            Create and manage your email marketing campaigns.
          </p>
        </div>
        <Button asChild>
          <Link href="/marketing/campaigns/create">
            <Plus className="mr-2 h-4 w-4" />
            Create Campaign
          </Link>
        </Button>
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
          <Input
            placeholder="Search campaigns..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Select
            defaultValue="all"
            value={statusFilter}
            onValueChange={setStatusFilter}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="sent">Sent</SelectItem>
              <SelectItem value="scheduled">Scheduled</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <span>More Filters</span>
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="py-8 text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
          <p className="mt-2 text-gray-500">Loading campaigns...</p>
        </div>
      ) : error ? (
        <div className="py-8 text-center">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
          </div>
        </div>
      ) : campaigns.length === 0 ? (
        <div className="py-8 text-center">
          <p className="text-gray-500">No campaigns found. Create your first campaign to get started!</p>
          <Button className="mt-4" asChild>
            <Link href="/marketing/campaigns/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Campaign
            </Link>
          </Button>
        </div>
      ) : (
        <Tabs defaultValue="all" value={currentTab} onValueChange={setCurrentTab} className="space-y-6">
          <TabsList>
            <TabsTrigger value="all">All Campaigns</TabsTrigger>
            <TabsTrigger value="sent">Sent</TabsTrigger>
            <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="draft">Drafts</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {campaigns.map((campaign) => (
              <CampaignCard key={campaign.id} campaign={campaign} />
            ))}
          </TabsContent>

          <TabsContent value="sent" className="space-y-4">
            {campaigns.filter(c => c.status === 'SENT').map((campaign) => (
              <CampaignCard key={campaign.id} campaign={campaign} />
            ))}
          </TabsContent>

          <TabsContent value="scheduled" className="space-y-4">
            {campaigns.filter(c => c.status === 'SCHEDULED').map((campaign) => (
              <CampaignCard key={campaign.id} campaign={campaign} />
            ))}
          </TabsContent>

          <TabsContent value="active" className="space-y-4">
            {campaigns.filter(c => c.status === 'ACTIVE').map((campaign) => (
              <CampaignCard key={campaign.id} campaign={campaign} />
            ))}
          </TabsContent>

          <TabsContent value="draft" className="space-y-4">
            {campaigns.filter(c => c.status === 'DRAFT').map((campaign) => (
              <CampaignCard key={campaign.id} campaign={campaign} />
            ))}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}

interface CampaignCardProps {
  campaign: Campaign;
}

function CampaignCard({ campaign }: CampaignCardProps) {
  const getStatusBadge = (status: CampaignStatus) => {
    switch (status) {
      case 'SENT':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Sent
          </Badge>
        );
      case 'SCHEDULED':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Calendar className="h-3 w-3 mr-1" />
            Scheduled
          </Badge>
        );
      case 'ACTIVE':
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            <Send className="h-3 w-3 mr-1" />
            Active
          </Badge>
        );
      case 'DRAFT':
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <Clock className="h-3 w-3 mr-1" />
            Draft
          </Badge>
        );
      default:
        return null;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <div className="bg-orange-100 p-2 rounded-full">
                <Mail className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">{campaign.name}</h3>
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <span>Email Campaign</span>
                  <span>•</span>
                  {campaign.status === 'SENT' && (
                    <span>Sent on {formatDate(campaign.sentAt)}</span>
                  )}
                  {campaign.status === 'SCHEDULED' && (
                    <span>Scheduled for {formatDate(campaign.scheduledDate)}</span>
                  )}
                  {campaign.status === 'ACTIVE' && (
                    <span>Active since {formatDate(campaign.createdAt)}</span>
                  )}
                  {campaign.status === 'DRAFT' && (
                    <span>Last updated on {formatDate(campaign.updatedAt)}</span>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              {getStatusBadge(campaign.status)}

              {campaign.recipients && (
                <Badge variant="outline" className="bg-gray-50">
                  <Users className="h-3 w-3 mr-1" />
                  {campaign.metrics?.totalRecipients || campaign.recipients.length} recipients
                </Badge>
              )}
            </div>

            <div className="flex gap-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/marketing/campaigns/${campaign.id}`}>
                  View
                </Link>
              </Button>

              {campaign.status === 'DRAFT' && (
                <Button size="sm" asChild>
                  <Link href={`/marketing/campaigns/${campaign.id}/edit`}>
                    Edit
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </div>

        {(campaign.status === 'SENT' || campaign.status === 'ACTIVE') && campaign.metrics && (
          <div className="mt-4 pt-4 border-t grid grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-500 mb-1">Open Rate</p>
              <p className="text-lg font-semibold">{campaign.metrics.openRate.toFixed(1)}%</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-1">Click Rate</p>
              <p className="text-lg font-semibold">{campaign.metrics.clickRate.toFixed(1)}%</p>
            </div>
            <div className="text-right">
              <Button variant="link" size="sm" className="h-auto p-0" asChild>
                <Link href={`/marketing/campaigns/${campaign.id}/analytics`}>
                  View Analytics
                </Link>
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
