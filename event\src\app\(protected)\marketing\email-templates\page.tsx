'use client'

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TemplatePreview } from '@/components/email-templates/template-preview';
import { NewsletterTemplate } from '@/components/email-templates/newsletter-template';
import { EventPromotionTemplate, generateEventPromotionHTML } from '@/components/email-templates/event-promotion-template';
import { WelcomeTemplate, generateWelcomeHTML } from '@/components/email-templates/welcome-template';
import { TicketConfirmationTemplate, generateTicketConfirmationHTML } from '@/components/email-templates/ticket-confirmation-template';



import { generateNewsletterHTML } from '@/components/email-templates/newsletter-template'

import dynamic from 'next/dynamic'

const EmailTemplateGenerator = dynamic(
  () => import('@/components/email-templates/email-templates-client'),
  { ssr: false }
)

export default function EmailTemplatesPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Email Templates</h1>
        <p className="text-gray-600">
          Preview and customize email templates for your marketing campaigns.
        </p>
      </div>

      <Tabs defaultValue="newsletter" className="space-y-6">
        <TabsList className="w-full">
          <TabsTrigger value="newsletter" className="flex-1">Newsletter</TabsTrigger>
          <TabsTrigger value="event" className="flex-1">Event Promotion</TabsTrigger>
          <TabsTrigger value="welcome" className="flex-1">Welcome</TabsTrigger>
          <TabsTrigger value="ticket" className="flex-1">Ticket Confirmation</TabsTrigger>
        </TabsList>

        <TabsContent value="newsletter">
          <Card>
            <CardHeader>
              <CardTitle>Newsletter Template</CardTitle>
              <CardDescription>
                Use this template for regular newsletters and updates.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TemplatePreview 
                template={<NewsletterTemplate {...newsletterData} />}
                html={newsletterHtml}
                name="Newsletter Template"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="event">
          <Card>
            <CardHeader>
              <CardTitle>Event Promotion Template</CardTitle>
              <CardDescription>
                Use this template to promote upcoming events.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TemplatePreview 
                template={<EventPromotionTemplate {...eventData} />}
                html={eventHtml}
                name="Event Promotion Template"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="welcome">
          <Card>
            <CardHeader>
              <CardTitle>Welcome Template</CardTitle>
              <CardDescription>
                Use this template to welcome new users to your platform.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TemplatePreview 
                template={<WelcomeTemplate {...welcomeData} />}
                html={welcomeHtml}
                name="Welcome Template"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ticket">
          <Card>
            <CardHeader>
              <CardTitle>Ticket Confirmation Template</CardTitle>
              <CardDescription>
                Use this template to confirm ticket purchases.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TemplatePreview 
                template={<TicketConfirmationTemplate {...ticketData} />}
                html={ticketHtml}
                name="Ticket Confirmation Template"
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
 // End of EmailTemplatesPage component
}

// Move the function to a server-side compatible file if needed

// Sample data for templates
  const newsletterData = {
    title: 'Latest Events & Updates',
    subtitle: 'Stay updated with our latest news and upcoming events',
    articles: [
      {
        title: 'Summer Music Festival 2024',
        excerpt: 'Join us for the biggest music festival of the year featuring top artists from around the world. Early bird tickets now available!',
        imageUrl: 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        linkUrl: '#',
      },
      {
        title: 'New Feature: Personalized Event Recommendations',
        excerpt: "We've launched a new feature that recommends events based on your interests and past attendance. Check it out now!",
        linkUrl: '#',
      },
      {
        title: 'Organizer Spotlight: Jane Smith',
        excerpt: 'Meet Jane Smith, the organizer behind some of the most successful tech conferences in the region.',
        imageUrl: 'https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
        linkUrl: '#',
      },
    ],
    ctaText: 'Browse All Events',
    ctaUrl: '#',
  };

  const eventData = {
    eventName: 'Tech Conference 2024',
    eventDate: 'June 15-17, 2024',
    eventLocation: 'Convention Center, New York',
    eventDescription: 'Join us for the biggest tech conference of the year. Network with industry leaders, attend workshops, and learn about the latest innovations in technology.',
    eventImageUrl: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    eventUrl: '#',
    ticketPrices: [
      { type: 'Early Bird', price: '$199' },
      { type: 'Regular', price: '$299' },
      { type: 'VIP', price: '$499' },
    ],
    featuredArtists: [
      { name: 'John Smith', imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80' },
      { name: 'Sarah Johnson', imageUrl: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80' },
      { name: 'Michael Brown', imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80' },
    ],
    organizerName: 'Tech Events Inc.',
    organizerLogo: 'https://via.placeholder.com/150',
    userName: 'Event Attendee',
  };

  const welcomeData = {
    userName: 'John',
    welcomeImageUrl: 'https://images.unsplash.com/photo-1531058020387-3be344556be6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    features: [
      {
        title: 'Discover Events',
        description: 'Find exciting events happening near you',
      },
      {
        title: 'Buy Tickets',
        description: 'Secure your spot with easy ticket purchasing',
      },
      {
        title: 'Manage Your Events',
        description: 'Create and manage your own events',
      },
      {
        title: 'Connect with Others',
        description: 'Network with like-minded individuals',
      },
    ],
  };

  const ticketData = {
    userName: 'John',
    eventName: 'Summer Music Festival',
    eventDate: 'July 10-12, 2024',
    eventLocation: 'Central Park, New York',
    eventImageUrl: 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80',
    ticketType: 'VIP Pass',
    ticketPrice: '$199.99',
    ticketQuantity: 2,
    orderNumber: 'ORD-12345',
    purchaseDate: 'June 1, 2024',
    qrCodeUrl: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=ORD-12345',
    downloadTicketUrl: '#',
    viewOrderUrl: '#',
  };

  // Generate HTML for each template
  const newsletterHtml = generateNewsletterHTML(newsletterData);
  const eventHtml = generateEventPromotionHTML(eventData);
  const welcomeHtml = generateWelcomeHTML(welcomeData);
  const ticketHtml = generateTicketConfirmationHTML(ticketData);
