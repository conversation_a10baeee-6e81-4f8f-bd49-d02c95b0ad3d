'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from '@/components/ui/use-toast';
import { CreditCard, Tag, Watch, ShoppingCart, Plus, Check, Info, AlertTriangle, Loader2, User } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

// Types
interface NFCProduct {
  id: string;
  deviceType: string;
  name: string;
  description: string | null;
  price: number;
  currency: string;
  imageUrl: string;
  features?: string[];
}

// Helper function to get icon for device type
const getDeviceIcon = (type: string) => {
  switch (type) {
    case 'CARD':
      return CreditCard;
    case 'FABRIC_WRISTBAND':
    case 'PAPER_WRISTBAND':
    case 'SILICONE_WRISTBAND':
      return Watch;
    case 'TAG':
      return Tag;
    default:
      return CreditCard;
  }
};

// Default features for each device type
const defaultFeatures: Record<string, string[]> = {
  'CARD': [
    'Credit card sized for wallet storage',
    'Durable plastic construction',
    'Waterproof and drop resistant',
    'Works with all event payment systems',
    '5-year lifespan'
  ],
  'FABRIC_WRISTBAND': [
    'Comfortable fabric material',
    'Adjustable size fits most wrists',
    'Ideal for multi-day events',
    'Secure clasp prevents loss',
    'Available in multiple colors'
  ],
  'PAPER_WRISTBAND': [
    'Disposable paper material',
    'Perfect for single-day events',
    'Cost-effective solution',
    'Tamper-evident design',
    'Customizable colors'
  ],
  'SILICONE_WRISTBAND': [
    'Premium silicone material',
    'Waterproof for pool and beach events',
    'Durable and long-lasting',
    'Comfortable for extended wear',
    'Available in multiple colors'
  ],
  'TAG': [
    'Compact and lightweight design',
    'Keychain hole for easy attachment',
    'Durable epoxy construction',
    'Ideal for festivals and concerts',
    'Most affordable option'
  ]
};

export default function NFCStorePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [products, setProducts] = useState<NFCProduct[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [color, setColor] = useState('blue');
  const [isProcessing, setIsProcessing] = useState(false);

  // Guest purchase state
  const [isGuest, setIsGuest] = useState(false);
  const [guestName, setGuestName] = useState('');
  const [guestEmail, setGuestEmail] = useState('');

  // Toggle guest mode
  const handleToggleGuest = () => {
    setIsGuest(!isGuest);
  };

  // Fetch NFC products
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);

      try {
        const response = await fetch('/api/nfc/products');

        if (response.ok) {
          const data = await response.json();

          // Add features to each product
          const productsWithFeatures = data.map((product: NFCProduct) => ({
            ...product,
            features: defaultFeatures[product.deviceType] || []
          }));

          setProducts(productsWithFeatures);

          // Select the first product by default if available
          if (productsWithFeatures.length > 0 && !selectedProductId) {
            setSelectedProductId(productsWithFeatures[0].id);
          }
        } else {
          throw new Error('Failed to fetch NFC products');
        }
      } catch (error) {
        console.error('Error fetching NFC products:', error);
        toast({
          title: 'Error',
          description: 'Failed to load NFC products',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Only redirect to login if trying to access user-specific features
  if (status === 'unauthenticated' && !isGuest) {
    // Don't redirect automatically - let the user choose to buy as guest
  }

  const handlePurchase = async () => {
    if (!selectedProductId) {
      toast({
        title: 'Please select a device',
        description: 'You need to select an NFC device to purchase',
        variant: 'destructive',
      });
      return;
    }

    // Validate guest information if in guest mode
    if (isGuest) {
      if (!guestName.trim()) {
        toast({
          title: 'Name required',
          description: 'Please enter your full name',
          variant: 'destructive',
        });
        return;
      }

      if (!guestEmail.trim() || !guestEmail.includes('@')) {
        toast({
          title: 'Valid email required',
          description: 'Please enter a valid email address where we can send your receipt',
          variant: 'destructive',
        });
        return;
      }
    }

    setIsProcessing(true);

    try {
      const selectedProduct = products.find(p => p.id === selectedProductId);

      if (!selectedProduct) {
        throw new Error('Selected product not found');
      }

      // Determine which API endpoint to use based on user status
      const endpoint = isGuest
        ? '/api/guest/nfc-devices/purchase'
        : '/api/user/nfc-devices/purchase';

      // Prepare request body
      const requestBody = {
        type: selectedProduct.deviceType,
        quantity,
        color: selectedProduct.deviceType.includes('WRISTBAND') ? color : undefined,
        price: selectedProduct.price,
        productId: selectedProduct.id,
        ...(isGuest && { guestName, guestEmail })
      };

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        const result = await response.json();

        toast({
          title: 'Purchase successful!',
          description: 'Your NFC device has been ordered and will be delivered soon.',
        });

        if (isGuest) {
          // For guests, show a confirmation page with their purchase details
          router.push(`/nfc-purchase-confirmation?email=${encodeURIComponent(guestEmail)}`);
        } else {
          // For logged-in users, redirect to their devices page
          router.push('/my-tickets?tab=nfc');
        }
      } else {
        const error = await response.json();
        throw new Error(error.error || 'Failed to purchase device');
      }
    } catch (error) {
      console.error('Error purchasing device:', error);
      toast({
        title: 'Purchase failed',
        description: error instanceof Error ? error.message : 'An error occurred during purchase',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Get the selected product
  const selectedProduct = products.find(p => p.id === selectedProductId);

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <h1 className="text-2xl font-bold mb-6">NFC Devices Store</h1>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading products...</span>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6 text-blue-700">NFC Devices Store</h1>

      <div className="bg-gradient-to-r from-blue-50 to-orange-50 border-l-4 border-l-blue-500 border-r-4 border-r-orange-500 rounded-lg p-4 mb-6">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-700">What are NFC devices?</h3>
            <p className="text-sm text-blue-600 mt-1">
              NFC (Near Field Communication) devices allow you to make contactless payments at events.
              Simply tap your NFC device at payment terminals to quickly pay for food, drinks, and merchandise
              without carrying cash or cards.
            </p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="browse">
        <TabsList className="grid w-full grid-cols-2 mb-8 bg-gray-100">
          <TabsTrigger value="browse" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">Browse Devices</TabsTrigger>
          <TabsTrigger value="checkout" className="data-[state=active]:bg-orange-600 data-[state=active]:text-white">Checkout</TabsTrigger>
        </TabsList>

        <TabsContent value="browse" className="space-y-6">
          {products.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <AlertTriangle className="h-12 w-12 text-orange-500 mb-4" />
                <h3 className="text-xl font-medium text-blue-700">No NFC products available</h3>
                <p className="text-gray-600 mt-2 text-center">
                  There are currently no NFC products available for purchase. Please check back later.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {products.map((product) => {
                const DeviceIcon = getDeviceIcon(product.deviceType);
                const isSelected = selectedProductId === product.id;

                return (
                  <Card
                    key={product.id}
                    className={`overflow-hidden cursor-pointer transition-all ${
                      isSelected ? 'ring-2 ring-orange-500 ring-offset-2 border-t-4 border-t-blue-500' : 'hover:shadow-md border-t-2 border-t-gray-200'
                    }`}
                    onClick={() => setSelectedProductId(product.id)}
                  >
                    <div className="relative h-48 bg-gray-100">
                      {product.imageUrl ? (
                        <Image
                          src={product.imageUrl}
                          alt={product.name}
                          fill
                          className="object-cover"
                          onError={(e) => {
                            // Fallback if image fails to load
                            e.currentTarget.src = '/images/placeholder.jpg';
                          }}
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <DeviceIcon className="h-16 w-16 text-gray-400" />
                        </div>
                      )}
                      <Badge className="absolute top-2 right-2 bg-orange-600">
                        ${product.price.toFixed(2)}
                      </Badge>
                    </div>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-lg flex items-center gap-2 text-blue-700">
                          <DeviceIcon className="h-5 w-5 text-blue-600" />
                          {product.name}
                        </CardTitle>
                        {isSelected && (
                          <Check className="h-5 w-5 text-orange-500" />
                        )}
                      </div>
                      <CardDescription>{product.description}</CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <ul className="text-sm space-y-1">
                        {product.features?.slice(0, 3).map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                    <CardFooter>
                      <Button
                        variant={isSelected ? "default" : "outline"}
                        className={`w-full ${isSelected ? 'bg-blue-600 hover:bg-blue-700' : 'border-orange-500 text-orange-600 hover:bg-orange-50'}`}
                        onClick={() => setSelectedProductId(product.id)}
                      >
                        {isSelected ? 'Selected' : 'Select'}
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          )}

          <div className="flex justify-end">
            <Button
              onClick={() => (document.querySelector('[data-value="checkout"]') as HTMLElement)?.click()}
              disabled={!selectedProductId}
              className="bg-orange-600 hover:bg-orange-700"
            >
              Continue to Checkout
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="checkout">
          {!selectedProductId ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <AlertTriangle className="h-12 w-12 text-orange-500 mb-4" />
                <h3 className="text-xl font-medium text-blue-700">No device selected</h3>
                <p className="text-gray-600 mt-2 text-center">
                  Please select an NFC device from the Browse tab first.
                </p>
                <Button
                  className="mt-6 bg-blue-600 hover:bg-blue-700"
                  onClick={() => (document.querySelector('[data-value="browse"]') as HTMLElement)?.click()}
                >
                  Browse Devices
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-t-4 border-t-blue-500">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-white">
                  <CardTitle className="text-blue-700">Order Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedProduct && (
                    <div className="space-y-4">
                      <div className="flex items-start gap-4">
                        <div className="relative h-20 w-20 bg-gray-100 rounded-md overflow-hidden">
                          {selectedProduct.imageUrl ? (
                            <Image
                              src={selectedProduct.imageUrl}
                              alt={selectedProduct.name}
                              fill
                              className="object-cover"
                            />
                          ) : (
                            <div className="flex items-center justify-center h-full">
                              {(() => {
                                const DeviceIcon = getDeviceIcon(selectedProduct.deviceType);
                                return <DeviceIcon className="h-10 w-10 text-gray-400" />;
                              })()}
                            </div>
                          )}
                        </div>
                        <div>
                          <h3 className="font-medium">
                            {selectedProduct.name}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {selectedProduct.description}
                          </p>
                          <div className="flex items-center mt-2">
                            <span className="font-medium">
                              ${selectedProduct.price.toFixed(2)}
                            </span>
                            <span className="mx-2 text-gray-400">×</span>
                            <span>{quantity}</span>
                          </div>
                        </div>
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Subtotal</span>
                          <span>
                            ${(selectedProduct.price * quantity).toFixed(2)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Shipping</span>
                          <span>$5.00</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Tax</span>
                          <span>
                            ${((selectedProduct.price * quantity) * 0.1).toFixed(2)}
                          </span>
                        </div>
                        <Separator />
                        <div className="flex justify-between font-bold">
                          <span>Total</span>
                          <span>
                            ${(
                              (selectedProduct.price * quantity) +
                              5 +
                              ((selectedProduct.price * quantity) * 0.1)
                            ).toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="border-t-4 border-t-orange-500">
                <CardHeader className="bg-gradient-to-r from-orange-50 to-white">
                  <CardTitle className="text-orange-600">Order Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="quantity">Quantity</Label>
                      <div className="flex items-center">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => setQuantity(Math.max(1, quantity - 1))}
                          disabled={quantity <= 1}
                        >
                          -
                        </Button>
                        <Input
                          id="quantity"
                          type="number"
                          value={quantity}
                          onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                          min="1"
                          max="10"
                          className="w-16 mx-2 text-center"
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => setQuantity(Math.min(10, quantity + 1))}
                          disabled={quantity >= 10}
                        >
                          +
                        </Button>
                      </div>
                    </div>

                    {selectedProduct && selectedProduct.deviceType.includes('WRISTBAND') && (
                      <div className="space-y-2">
                        <Label>Color</Label>
                        <RadioGroup value={color} onValueChange={setColor} className="flex flex-wrap gap-2">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="blue" id="blue" />
                            <Label htmlFor="blue" className="flex items-center cursor-pointer">
                              <div className="w-4 h-4 rounded-full bg-blue-500 mr-2"></div>
                              Blue
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="red" id="red" />
                            <Label htmlFor="red" className="flex items-center cursor-pointer">
                              <div className="w-4 h-4 rounded-full bg-red-500 mr-2"></div>
                              Red
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="green" id="green" />
                            <Label htmlFor="green" className="flex items-center cursor-pointer">
                              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                              Green
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="black" id="black" />
                            <Label htmlFor="black" className="flex items-center cursor-pointer">
                              <div className="w-4 h-4 rounded-full bg-black mr-2"></div>
                              Black
                            </Label>
                          </div>
                        </RadioGroup>
                      </div>
                    )}

                    {/* Guest purchase option */}
                    {status === 'unauthenticated' && (
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="guest-purchase"
                            checked={isGuest}
                            onCheckedChange={() => handleToggleGuest()}
                          />
                          <Label htmlFor="guest-purchase" className="font-medium">
                            Purchase as guest (no account required)
                          </Label>
                        </div>

                        {isGuest && (
                          <div className="space-y-3 p-3 bg-orange-50 rounded-md border border-orange-200">
                            <div className="space-y-1">
                              <Label htmlFor="guest-name" className="text-sm">
                                Your Full Name <span className="text-red-500">*</span>
                              </Label>
                              <Input
                                id="guest-name"
                                value={guestName}
                                onChange={(e) => setGuestName(e.target.value)}
                                placeholder="Enter your full name"
                                className="w-full"
                              />
                            </div>

                            <div className="space-y-1">
                              <Label htmlFor="guest-email" className="text-sm">
                                Email Address <span className="text-red-500">*</span>
                              </Label>
                              <Input
                                id="guest-email"
                                type="email"
                                value={guestEmail}
                                onChange={(e) => setGuestEmail(e.target.value)}
                                placeholder="Enter your email address"
                                className="w-full"
                              />
                              <p className="text-xs text-gray-500 mt-1">
                                We'll send your receipt and device details to this email
                              </p>
                            </div>
                          </div>
                        )}

                        {!isGuest && status === 'unauthenticated' && (
                          <div className="flex items-center justify-between text-sm">
                            <span>Already have an account?</span>
                            <Button variant="link" asChild className="p-0 h-auto text-blue-600">
                              <Link href="/auth/login?callbackUrl=/nfc-store">
                                Sign in
                              </Link>
                            </Button>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="bg-gradient-to-r from-blue-50 to-orange-50 border-l-2 border-l-blue-400 border-r-2 border-r-orange-400 rounded-md p-3 text-sm">
                      <div className="flex items-start gap-2">
                        <Info className="h-4 w-4 mt-0.5 flex-shrink-0 text-blue-600" />
                        <p className="text-blue-700">
                          Your NFC device will be delivered to your registered address.
                          You can start using it immediately after activation.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col space-y-2 bg-gradient-to-r from-orange-50 to-blue-50">
                  <Button
                    className="w-full bg-orange-600 hover:bg-orange-700"
                    onClick={handlePurchase}
                    disabled={isProcessing || (isGuest && (!guestName || !guestEmail))}
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      'Complete Purchase'
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full border-blue-500 text-blue-600 hover:bg-blue-50"
                    onClick={() => (document.querySelector('[data-value="browse"]') as HTMLElement)?.click()}
                  >
                    Back to Browse
                  </Button>
                </CardFooter>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>

      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4 text-blue-700">Already have an NFC device?</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="border-t-4 border-t-orange-500">
            <CardHeader className="bg-gradient-to-r from-orange-50 to-white">
              <CardTitle className="text-orange-600">Top Up Your Balance</CardTitle>
              <CardDescription>
                Add funds to your existing NFC devices
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Quickly add funds to your NFC devices to use at events for food, drinks, and merchandise.
              </p>
            </CardContent>
            <CardFooter className="bg-orange-50">
              <Button asChild className="w-full bg-orange-600 hover:bg-orange-700">
                <Link href="/nfc-topup">Top Up Now</Link>
              </Button>
            </CardFooter>
          </Card>

          <Card className="border-t-4 border-t-blue-500">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-white">
              <CardTitle className="text-blue-700">Manage Your Devices</CardTitle>
              <CardDescription>
                View and manage all your NFC devices
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Check balances, transaction history, and manage device settings.
              </p>
            </CardContent>
            <CardFooter className="bg-blue-50">
              <Button asChild variant="outline" className="w-full border-blue-500 text-blue-600 hover:bg-blue-50">
                <Link href="/my-tickets?tab=nfc">View My Devices</Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
