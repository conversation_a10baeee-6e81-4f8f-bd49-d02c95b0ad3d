import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { getSession } from '@/auth';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import { TransactionsList } from '@/components/wallet/transactions-list';

export default async function TransactionsPage() {
  const session = await getSession();

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only organizers can access this page
  if (session.user.role !== 'ORGANIZER') {
    redirect('/dashboard');
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild>
          <Link href="/organizer/wallet">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Wallet
          </Link>
        </Button>
      </div>

      <div className="mb-6">
        <h1 className="text-3xl font-bold">Transaction History</h1>
        <p className="text-gray-500 mt-1">
          View all your financial transactions
        </p>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <TransactionsList />
      </Suspense>
    </div>
  );
}
