import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { getSession } from '@/auth';
import { db } from '@/lib/prisma';
import { Button } from '@/components/ui/button';
import { WithdrawalForm } from '@/components/wallet/withdrawal-form';
import { WithdrawalHistory } from '@/components/wallet/withdrawal-history';
import { ChevronLeft } from 'lucide-react';

export default async function WithdrawPage() {
  const session = await getSession();

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only organizers can access this page
  if (session.user.role !== 'ORGANIZER') {
    redirect('/dashboard');
  }

  // Get user with account balance
  const user = await db.user.findUnique({
    where: { id: session.user.id },
    select: {
      id: true,
      accountBalance: true,
    },
  });

  if (!user) {
    redirect('/dashboard');
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild>
          <Link href="/organizer/wallet">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Wallet
          </Link>
        </Button>
      </div>

      <div className="mb-6">
        <h1 className="text-3xl font-bold">Withdraw Funds</h1>
        <p className="text-gray-500 mt-1">
          Request a withdrawal to your bank account
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Suspense fallback={<div>Loading...</div>}>
          <WithdrawalForm accountBalance={user.accountBalance} />
        </Suspense>

        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Recent Withdrawals</h2>
          <Suspense fallback={<div>Loading...</div>}>
            <WithdrawalHistory />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
