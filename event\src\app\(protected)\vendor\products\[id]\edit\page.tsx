'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Upload, ArrowLeft } from 'lucide-react';

// Product categories and types from Prisma schema
const ProductCategory = {
  ELECTRONICS: 'ELECTRONICS',
  FASHION: 'FASHION',
  HOME_AND_GARDEN: 'HOME_AND_GARDEN',
  SPORTS_AND_OUTDOORS: 'SPORTS_AND_OUTDOORS',
  TOYS_AND_GAMES: 'TOYS_AND_GAMES',
  HEALTH_AND_BEAUTY: 'HEALTH_AND_BEAUTY',
  BABY_PRODUCTS: 'BABY_PRODUCTS',
  PET_PRODUCTS: 'PET_PRODUCTS',
  ART_AND_CRAFTS: 'ART_AND_CRAFTS',
  FOOD_AND_BEVERAGES: 'FOOD_AND_BEVERAGES',
  OTHER: 'OTHER',
};

const ProductType = {
  PHYSICAL: 'PHYSICAL',
  DIGITAL: 'DIGITAL',
};

const ProductStatus = {
  Draft: 'Draft',
  Onsale: 'Onsale',
  Cancelled: 'Cancelled',
  Sold: 'Sold',
};

// Form validation schema
const productSchema = z.object({
  name: z.string().min(2, 'Product name is required'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  about: z.string().optional(),
  price: z.coerce.number().positive('Price must be positive'),
  category: z.enum([
    'ELECTRONICS', 'FASHION', 'HOME_AND_GARDEN', 'SPORTS_AND_OUTDOORS',
    'TOYS_AND_GAMES', 'HEALTH_AND_BEAUTY', 'BABY_PRODUCTS', 'PET_PRODUCTS',
    'ART_AND_CRAFTS', 'FOOD_AND_BEVERAGES', 'OTHER'
  ]),
  productType: z.enum(['PHYSICAL', 'DIGITAL']),
  status: z.enum(['Draft', 'Onsale', 'Cancelled', 'Sold']),
  stockQuantity: z.coerce.number().int().nonnegative('Stock quantity must be a non-negative integer'),
  weight: z.coerce.number().nonnegative('Weight must be a non-negative number'),
  dimensions: z.string().min(1, 'Dimensions are required'),
  material: z.string().min(1, 'Material is required'),
  color: z.string().min(1, 'Color is required'),
});

export default function EditProductPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const resolvedParams = use(params);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const form = useForm({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: '',
      description: '',
      about: '',
      price: 0,
      category: 'OTHER' as const,
      productType: 'PHYSICAL' as const,
      status: 'Onsale' as const,
      stockQuantity: 1,
      weight: 0,
      dimensions: '',
      material: '',
      color: '',
    },
  });

  useEffect(() => {
    async function fetchProduct() {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/vendors/products/${resolvedParams.id}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch product');
        }
        
        const product = await response.json();
        
        // Set form values
        form.reset({
          name: product.name,
          description: product.description || '',
          about: product.about || '',
          price: product.price,
          category: product.category,
          productType: product.productType,
          status: product.status,
          stockQuantity: product.stockQuantity,
          weight: product.weight,
          dimensions: product.dimensions,
          material: product.material,
          color: product.color || '',
        });
        
        // Set image preview if available
        if (product.imagePath) {
          setImagePreview(product.imagePath);
        }
      } catch (err) {
        console.error('Error fetching product:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load product',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchProduct();
  }, [resolvedParams.id, form]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (event) => {
        setImagePreview(event.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: z.infer<typeof productSchema>) => {
    setIsSubmitting(true);

    try {
      let updatedData: any = { ...data };
      
      // If there's a new image, upload it first
      if (imageFile) {
        const formData = new FormData();
        formData.append('image', imageFile);
        
        const uploadResponse = await fetch('/api/upload/product-image', {
          method: 'POST',
          body: formData,
        });
        
        if (!uploadResponse.ok) {
          throw new Error('Failed to upload image');
        }
        
        const { imagePath } = await uploadResponse.json();
        updatedData.imagePath = imagePath;
      }

      // Update the product
      const response = await fetch(`/api/vendors/products/${resolvedParams.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update product');
      }

      toast({
        title: 'Success',
        description: 'Product updated successfully',
        variant: 'default',
      });

      // Redirect to products page
      router.push('/vendor/products');
    } catch (error) {
      console.error('Error updating product:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An error occurred while updating the product',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading product...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <h3 className="text-lg font-medium mb-2 text-red-500">{error}</h3>
              <Button onClick={() => router.push('/vendor/products')}>
                Back to Products
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center mb-6">
        <Button variant="outline" size="sm" onClick={() => router.back()} className="mr-4">
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Edit Product</h1>
      </div>
      
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Edit Product</CardTitle>
          <CardDescription>
            Update your product information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Product Image */}
            <div className="space-y-2">
              <Label htmlFor="image">Product Image</Label>
              <div className="flex items-center gap-4">
                <div 
                  className="border-2 border-dashed border-gray-300 rounded-lg p-4 w-40 h-40 flex flex-col items-center justify-center cursor-pointer hover:border-primary"
                  onClick={() => document.getElementById('image')?.click()}
                >
                  {imagePreview ? (
                    <img 
                      src={imagePreview} 
                      alt="Product preview" 
                      className="max-h-full max-w-full object-contain"
                    />
                  ) : (
                    <>
                      <Upload className="h-10 w-10 text-gray-400 mb-2" />
                      <span className="text-sm text-gray-500">Upload image</span>
                    </>
                  )}
                </div>
                <Input
                  id="image"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageChange}
                />
                <div className="text-sm text-gray-500">
                  <p>Click to upload a new product image</p>
                  <p>Recommended size: 800x800px</p>
                  <p>Max size: 5MB</p>
                </div>
              </div>
            </div>

            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Product Name *</Label>
                  <Input
                    id="name"
                    {...form.register('name')}
                    placeholder="Enter product name"
                  />
                  {form.formState.errors.name && (
                    <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="price">Price (ZMW) *</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    min="0"
                    {...form.register('price')}
                    placeholder="0.00"
                  />
                  {form.formState.errors.price && (
                    <p className="text-sm text-red-500">{form.formState.errors.price.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Short Description *</Label>
                <Textarea
                  id="description"
                  {...form.register('description')}
                  placeholder="Brief description of your product"
                  rows={3}
                />
                {form.formState.errors.description && (
                  <p className="text-sm text-red-500">{form.formState.errors.description.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="about">Detailed Description</Label>
                <Textarea
                  id="about"
                  {...form.register('about')}
                  placeholder="Detailed information about your product"
                  rows={5}
                />
                {form.formState.errors.about && (
                  <p className="text-sm text-red-500">{form.formState.errors.about.message}</p>
                )}
              </div>
            </div>

            {/* Product Details */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Product Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select 
                    defaultValue={form.getValues('category')}
                    onValueChange={(value) => form.setValue('category', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(ProductCategory).map(([key, value]) => (
                        <SelectItem key={key} value={value}>
                          {key.replace(/_/g, ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {form.formState.errors.category && (
                    <p className="text-sm text-red-500">{form.formState.errors.category.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="productType">Product Type *</Label>
                  <Select 
                    defaultValue={form.getValues('productType')}
                    onValueChange={(value) => form.setValue('productType', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select product type" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(ProductType).map(([key, value]) => (
                        <SelectItem key={key} value={value}>
                          {key}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {form.formState.errors.productType && (
                    <p className="text-sm text-red-500">{form.formState.errors.productType.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Status *</Label>
                  <Select 
                    defaultValue={form.getValues('status')}
                    onValueChange={(value) => form.setValue('status', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(ProductStatus).map(([key, value]) => (
                        <SelectItem key={key} value={value}>
                          {value}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {form.formState.errors.status && (
                    <p className="text-sm text-red-500">{form.formState.errors.status.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="stockQuantity">Stock Quantity *</Label>
                  <Input
                    id="stockQuantity"
                    type="number"
                    min="0"
                    step="1"
                    {...form.register('stockQuantity')}
                  />
                  {form.formState.errors.stockQuantity && (
                    <p className="text-sm text-red-500">{form.formState.errors.stockQuantity.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="weight">Weight (kg) *</Label>
                  <Input
                    id="weight"
                    type="number"
                    min="0"
                    step="0.01"
                    {...form.register('weight')}
                  />
                  {form.formState.errors.weight && (
                    <p className="text-sm text-red-500">{form.formState.errors.weight.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="dimensions">Dimensions *</Label>
                  <Input
                    id="dimensions"
                    {...form.register('dimensions')}
                    placeholder="e.g., 10x5x2 cm"
                  />
                  {form.formState.errors.dimensions && (
                    <p className="text-sm text-red-500">{form.formState.errors.dimensions.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="material">Material *</Label>
                  <Input
                    id="material"
                    {...form.register('material')}
                    placeholder="e.g., Cotton, Wood, Metal"
                  />
                  {form.formState.errors.material && (
                    <p className="text-sm text-red-500">{form.formState.errors.material.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="color">Color *</Label>
                  <Input
                    id="color"
                    {...form.register('color')}
                    placeholder="e.g., Red, Blue, Multi-color"
                  />
                  {form.formState.errors.color && (
                    <p className="text-sm text-red-500">{form.formState.errors.color.message}</p>
                  )}
                </div>
              </div>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button 
            onClick={form.handleSubmit(onSubmit)} 
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              'Update Product'
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
