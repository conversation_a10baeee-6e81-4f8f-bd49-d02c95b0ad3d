'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tag, ArrowLeft } from 'lucide-react';

// Product categories from Prisma schema
const ProductCategory = {
  ELECTRONICS: 'Electronics',
  FASHION: 'Fashion',
  HOME_AND_GARDEN: 'Home & Garden',
  SPORTS_AND_OUTDOORS: 'Sports & Outdoors',
  TOYS_AND_GAMES: 'Toys & Games',
  HEALTH_AND_BEAUTY: 'Health & Beauty',
  BABY_PRODUCTS: 'Baby Products',
  PET_PRODUCTS: 'Pet Products',
  ART_AND_CRAFTS: 'Art & Crafts',
  FOOD_AND_BEVERAGES: 'Food & Beverages',
  OTHER: 'Other',
};

export default function ProductCategoriesPage() {
  const router = useRouter();

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold">Product Categories</h1>
          <p className="text-gray-500 mt-1">Browse and manage product categories</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Button onClick={() => router.push('/vendor/createproduct')}>
            Create Product
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Object.entries(ProductCategory).map(([key, value]) => (
          <Card key={key} className="overflow-hidden">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center">
                <Tag className="h-5 w-5 mr-2 text-primary" />
                {value}
              </CardTitle>
              <CardDescription>
                Products in the {value.toLowerCase()} category
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center">
                <Badge variant="outline" className="px-3 py-1">
                  {key}
                </Badge>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => router.push(`/vendor/products?category=${key}`)}
                >
                  View Products
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
