import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { WalletSettingsForm } from '@/components/wallet/wallet-settings-form';

export const metadata = {
  title: 'Wallet Settings | QuickTimeEvents',
  description: 'Customize your wallet preferences and notifications',
};

export default async function WalletSettingsPage() {
  const session = await getSession();

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild>
          <Link href="/wallet">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Wallet
          </Link>
        </Button>
      </div>

      <div className="mb-6">
        <h1 className="text-3xl font-bold">Wallet Settings</h1>
        <p className="text-gray-500 mt-1">
          Customize your wallet preferences and notifications
        </p>
      </div>

      <WalletSettingsForm />
    </div>
  );
}
