import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get total users count
    const totalUsers = await db.user.count();

    // Get total events count
    const totalEvents = await db.event.count();

    // Get pending organizer verifications count
    const pendingOrganizerVerifications = await db.organizerVerification.count({
      where: { status: 'PENDING' }
    });

    // Get pending vendor verifications count
    const pendingVendorVerifications = await db.vendorVerification.count({
      where: { status: 'PENDING' }
    });

    // Total pending verifications
    const pendingVerifications = pendingOrganizerVerifications + pendingVendorVerifications;

    // Get pending withdrawals count
    const pendingWithdrawals = await db.withdrawal.count({
      where: { status: 'Pending' }
    });

    // Get total revenue (sum of all completed orders)
    const revenueResult = await db.order.aggregate({
      where: { status: 'Completed' },
      _sum: { totalPrice: true }
    });
    const totalRevenue = revenueResult._sum.totalPrice || 0;

    // Get active events count (events with status Published and end date in the future)
    const activeEvents = await db.event.count({
      where: {
        status: 'Published',
        endDate: { gt: new Date() }
      }
    });

    // Get total tickets sold
    const ticketsSold = await db.ticket.count({
      where: { isAvailable: false }
    });

    // Get user management stats
    const activeUsers = await db.user.count({
      where: { emailVerified: { not: null } }
    });

    const pendingInvitations = await db.userInvitation.count({
      where: {
        status: 'PENDING',
        expiresAt: { gt: new Date() }
      }
    });

    // Get recent activity count (last 24 hours)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    const recentActivity = await db.activityLog.count({
      where: {
        timestamp: { gte: yesterday }
      }
    });

    // Get recent users (last 5 registered)
    const recentUsers = await db.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    // Get recent events (last 5 created)
    const recentEvents = await db.event.findMany({
      select: {
        id: true,
        title: true,
        status: true,
        createdAt: true,
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    // Format the data
    const dashboardStats = {
      totalUsers,
      totalEvents,
      pendingVerifications,
      pendingWithdrawals,
      totalRevenue,
      activeEvents,
      totalTicketsSold: ticketsSold,
      // User management stats
      activeUsers,
      pendingInvitations,
      recentActivity,
      recentUsers: recentUsers.map(user => ({
        id: user.id,
        name: user.name || 'Anonymous',
        email: user.email || '',
        role: user.role,
        createdAt: user.createdAt.toISOString().split('T')[0]
      })),
      recentEvents: recentEvents.map(event => ({
        id: event.id,
        title: event.title,
        status: event.status,
        createdAt: event.createdAt.toISOString().split('T')[0]
      }))
    };

    return NextResponse.json(dashboardStats);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);

    // Return fallback data when database is not available
    const fallbackStats = {
      totalUsers: 0,
      totalEvents: 0,
      pendingVerifications: 0,
      pendingWithdrawals: 0,
      totalRevenue: 0,
      activeEvents: 0,
      totalTicketsSold: 0,
      activeUsers: 0,
      pendingInvitations: 0,
      recentActivity: 0,
      recentUsers: [],
      recentEvents: [],
      warning: 'Database not available. Please ensure your database is running and properly configured.'
    };

    return NextResponse.json(fallbackStats);
  }
}
