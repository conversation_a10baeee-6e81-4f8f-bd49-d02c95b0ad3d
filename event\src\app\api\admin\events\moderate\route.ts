import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * POST /api/admin/events/moderate
 * Moderate published events (suspend/restore)
 */
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();
    
    if (!user || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    const { eventId, action, reason } = await request.json();

    // Validate input
    if (!eventId || !action) {
      return NextResponse.json(
        { error: 'Event ID and action are required' },
        { status: 400 }
      );
    }

    if (!['suspend', 'restore'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be either "suspend" or "restore"' },
        { status: 400 }
      );
    }

    // Get the current event
    const event = await db.event.findUnique({
      where: { id: eventId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!event) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }

    // Determine new status
    const newStatus = action === 'suspend' ? 'Suspended' : 'Published';

    // Update event status
    const updatedEvent = await db.event.update({
      where: { id: eventId },
      data: {
        status: newStatus,
        metadata: {
          ...(typeof event.metadata === 'object' && event.metadata !== null ? event.metadata as any : {}),
          moderationReason: reason || null,
          moderatedBy: user.id,
          moderatedAt: new Date().toISOString()
        }
      }
    });

    // Create notification for the organizer
    const notificationMessage = action === 'suspend'
      ? `Your event "${event.title}" has been suspended by an administrator. ${reason ? `Reason: ${reason}` : ''}`
      : `Your event "${event.title}" has been restored and is now live again.`;

    await db.notification.create({
      data: {
        userId: event.user.id,
        message: notificationMessage,
        type: action === 'suspend' ? 'EVENT_SUSPENDED' : 'EVENT_RESTORED',
        isRead: false
      }
    });

    return NextResponse.json({
      success: true,
      event: {
        id: updatedEvent.id,
        title: updatedEvent.title,
        status: updatedEvent.status,
        organizer: {
          id: event.user.id,
          name: event.user.name,
          email: event.user.email
        }
      },
      message: `Event ${action}ed successfully`
    });

  } catch (error) {
    console.error('Error moderating event:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/events/moderate
 * Get events that need moderation (published events)
 */
export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();
    
    if (!user || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status') || 'Published';
    const skip = (page - 1) * limit;

    // Get events for moderation
    const events = await db.event.findMany({
      where: {
        status: status as any
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        _count: {
          select: {
            tickets: true,
            attendance: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    });

    const totalEvents = await db.event.count({
      where: {
        status: status as any
      }
    });

    return NextResponse.json({
      events,
      pagination: {
        page,
        limit,
        total: totalEvents,
        pages: Math.ceil(totalEvents / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching events for moderation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
