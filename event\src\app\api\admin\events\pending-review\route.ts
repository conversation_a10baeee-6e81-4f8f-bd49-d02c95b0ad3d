import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();
    
    // Check if user is authenticated and has admin role
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status') || 'UnderReview';
    
    // Validate status
    const validStatuses = ['UnderReview', 'Approved', 'Rejected', 'All'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status parameter' },
        { status: 400 }
      );
    }
    
    // Build where clause
    const where = status === 'All'
      ? { status: { in: ['UnderReview', 'Published', 'Rejected'] as any } }
      : status === 'Approved'
        ? { status: 'Published' as any }
        : { status: status as any };
    
    // Get events with pagination
    const events = await db.event.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        eventReviews: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
      skip: (page - 1) * limit,
      take: limit,
    });
    
    // Get total count for pagination
    const totalEvents = await db.event.count({ where });
    
    return NextResponse.json({
      events,
      pagination: {
        total: totalEvents,
        page,
        limit,
        totalPages: Math.ceil(totalEvents / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching events pending review:', error);
    return NextResponse.json(
      { error: 'Failed to fetch events' },
      { status: 500 }
    );
  }
}
