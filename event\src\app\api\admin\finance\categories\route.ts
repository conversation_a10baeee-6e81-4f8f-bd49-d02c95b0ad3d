import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/finance/categories
 * Get event categories and vendor types for filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all event categories
    const events = await db.event.findMany({
      select: {
        category: true
      },
      distinct: ['category']
    });

    // Get all vendor types
    const vendors = await db.vendorProfile.findMany({
      select: {
        businessType: true
      },
      distinct: ['businessType']
    });

    // Format the response
    const eventCategories = events
      .map(event => event.category)
      .filter(category => category) // Filter out null/undefined
      .sort();

    const vendorTypes = vendors
      .map(vendor => vendor.businessType)
      .filter(type => type) // Filter out null/undefined
      .sort();

    return NextResponse.json({
      eventCategories,
      vendorTypes
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}
