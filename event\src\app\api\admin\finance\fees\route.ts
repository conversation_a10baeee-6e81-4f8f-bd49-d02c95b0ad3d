import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/finance/fees
 * Redirects to /api/admin/fees for backward compatibility
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Forward the request to the actual fees endpoint
    // Use absolute URL with origin to ensure proper routing
    const origin = request.headers.get('host') || 'localhost';
    const protocol = origin.includes('localhost') ? 'http' : 'https';
    const apiUrl = `${protocol}://${origin}/api/admin/fees`;

    // Get the session cookie to forward with the request
    const cookies = request.headers.get('cookie') || '';

    const response = await fetch(apiUrl, {
      headers: {
        'Cookie': cookies,
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();

    // Ensure we're returning an array
    if (Array.isArray(data)) {
      return NextResponse.json(data);
    } else if (data.error) {
      // If there's an error, pass it through
      return NextResponse.json({ error: data.error }, { status: response.status });
    } else {
      // If it's not an array and not an error, log a warning and return an empty array
      console.warn('Unexpected response format from fees API:', data);
      return NextResponse.json([]);
    }
  } catch (error) {
    console.error('Error fetching fee configurations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch fee configurations' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/finance/fees
 * Redirects to /api/admin/fees for backward compatibility
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the request body
    const body = await request.json();

    // Forward the request to the actual fees endpoint
    // Use absolute URL with origin to ensure proper routing
    const origin = request.headers.get('host') || 'localhost';
    const protocol = origin.includes('localhost') ? 'http' : 'https';
    const apiUrl = `${protocol}://${origin}/api/admin/fees`;

    // Get the session cookie to forward with the request
    const cookies = request.headers.get('cookie') || '';

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Cookie': cookies,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error creating fee configuration:', error);
    return NextResponse.json(
      { error: 'Failed to create fee configuration' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/finance/fees/:id
 * Redirects to /api/admin/fees/:id for backward compatibility
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const id = resolvedParams.id;
    const body = await request.json();

    // Forward the request to the actual fees endpoint
    // Use absolute URL with origin to ensure proper routing
    const origin = request.headers.get('host') || 'localhost';
    const protocol = origin.includes('localhost') ? 'http' : 'https';
    const apiUrl = `${protocol}://${origin}/api/admin/fees/${id}`;

    // Get the session cookie to forward with the request
    const cookies = request.headers.get('cookie') || '';

    const response = await fetch(apiUrl, {
      method: 'PATCH',
      headers: {
        'Cookie': cookies,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error updating fee configuration:', error);
    return NextResponse.json(
      { error: 'Failed to update fee configuration' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/finance/fees/:id
 * Redirects to /api/admin/fees/:id for backward compatibility
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const id = resolvedParams.id;

    // Forward the request to the actual fees endpoint
    // Use absolute URL with origin to ensure proper routing
    const origin = request.headers.get('host') || 'localhost';
    const protocol = origin.includes('localhost') ? 'http' : 'https';
    const apiUrl = `${protocol}://${origin}/api/admin/fees/${id}`;

    // Get the session cookie to forward with the request
    const cookies = request.headers.get('cookie') || '';

    const response = await fetch(apiUrl, {
      method: 'DELETE',
      headers: {
        'Cookie': cookies
      }
    });

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error deleting fee configuration:', error);
    return NextResponse.json(
      { error: 'Failed to delete fee configuration' },
      { status: 500 }
    );
  }
}
