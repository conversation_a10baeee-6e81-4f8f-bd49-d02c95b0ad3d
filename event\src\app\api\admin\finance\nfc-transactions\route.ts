import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { Prisma } from '@prisma/client';

/**
 * GET /api/admin/finance/nfc-transactions
 * Get detailed NFC transactions data for admin
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;
    
    // Build date filter
    const dateFilter: Prisma.VendorNFCTransactionWhereInput = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) {
        dateFilter.createdAt.gte = startDate;
      }
      if (endDate) {
        dateFilter.createdAt.lte = endDate;
      }
    }

    // Get all completed NFC transactions
    const nfcTransactions = await db.vendorNFCTransaction.findMany({
      where: {
        status: 'COMPLETED',
        ...dateFilter
      },
      include: {
        vendor: {
          select: {
            id: true,
            businessName: true
          }
        },
        event: {
          select: {
            id: true,
            title: true
          }
        }
      }
    });

    // Aggregate NFC transactions by vendor and event
    const transactionsByVendorEvent: any = {};

    nfcTransactions.forEach(tx => {
      const vendorId = tx.vendorId;
      const vendorName = tx.vendor?.businessName || 'Unknown Vendor';
      const eventId = tx.eventId;
      const eventTitle = tx.event?.title || 'Unknown Event';
      const key = `${vendorId}-${eventId}`;

      if (!transactionsByVendorEvent[key]) {
        transactionsByVendorEvent[key] = {
          vendorId,
          vendorName,
          eventId,
          eventTitle,
          transactionCount: 0,
          totalAmount: 0,
          platformFees: 0,
          netAmount: 0
        };
      }

      transactionsByVendorEvent[key].transactionCount += 1;
      transactionsByVendorEvent[key].totalAmount += tx.amount;
      
      // Calculate platform fees (3.5% of transaction amount)
      const platformFee = tx.amount * 0.035;
      transactionsByVendorEvent[key].platformFees += platformFee;
      transactionsByVendorEvent[key].netAmount += (tx.amount - platformFee);
    });

    // Convert to array and sort by total amount
    const nfcTransactionsArray = Object.values(transactionsByVendorEvent).sort((a: any, b: any) => 
      b.totalAmount - a.totalAmount
    );

    return NextResponse.json(nfcTransactionsArray);
  } catch (error) {
    console.error('Error fetching NFC transactions data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch NFC transactions data' },
      { status: 500 }
    );
  }
}
