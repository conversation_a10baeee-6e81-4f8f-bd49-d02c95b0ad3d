import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { Prisma, EventCategory } from '@prisma/client';

/**
 * GET /api/admin/finance/ticket-sales
 * Get detailed ticket sales data for admin
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;
    const category = searchParams.get('category');

    // Build date filter
    const dateFilter: Prisma.OrderWhereInput = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) {
        dateFilter.createdAt.gte = startDate;
      }
      if (endDate) {
        dateFilter.createdAt.lte = endDate;
      }
    }

    // Build category filter
    const eventFilter: Prisma.EventWhereInput = {};
    if (category) {
      eventFilter.category = category as EventCategory;
    }

    // Get all completed orders with tickets
    const orders = await db.order.findMany({
      where: {
        status: 'Completed',
        ...dateFilter,
        event: Object.keys(eventFilter).length > 0 ? {
          ...eventFilter
        } : undefined
      },
      include: {
        event: {
          select: {
            id: true,
            title: true,
            user: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        tickets: {
          select: {
            id: true,
            type: true,
            price: true,
            quantity: true,
            totalPrice: true
          }
        }
      }
    });

    // Aggregate ticket sales by event and ticket type
    const eventTicketSales: Record<string, any> = {};

    orders.forEach(order => {
      const eventId = order.eventId;
      const eventTitle = order.event.title;
      const organizerName = order.event.user?.name || 'Unknown Organizer';

      if (!eventTicketSales[eventId]) {
        eventTicketSales[eventId] = {
          eventId,
          eventTitle,
          organizerName,
          regularTickets: 0,
          regularRevenue: 0,
          vipTickets: 0,
          vipRevenue: 0,
          vvipTickets: 0,
          vvipRevenue: 0,
          totalTickets: 0,
          totalRevenue: 0
        };
      }

      order.tickets.forEach(ticket => {
        const ticketType = ticket.type;
        const quantity = ticket.quantity;
        const revenue = ticket.totalPrice;

        eventTicketSales[eventId].totalTickets += quantity;
        eventTicketSales[eventId].totalRevenue += revenue;

        if (ticketType === 'REGULAR') {
          eventTicketSales[eventId].regularTickets += quantity;
          eventTicketSales[eventId].regularRevenue += revenue;
        } else if (ticketType === 'VIP') {
          eventTicketSales[eventId].vipTickets += quantity;
          eventTicketSales[eventId].vipRevenue += revenue;
        } else if (ticketType === 'VVIP') {
          eventTicketSales[eventId].vvipTickets += quantity;
          eventTicketSales[eventId].vvipRevenue += revenue;
        }
      });
    });

    // Convert to array and sort by total revenue
    const ticketSalesArray = Object.values(eventTicketSales).sort((a: any, b: any) =>
      b.totalRevenue - a.totalRevenue
    );

    return NextResponse.json(ticketSalesArray);
  } catch (error) {
    console.error('Error fetching ticket sales data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch ticket sales data' },
      { status: 500 }
    );
  }
}
