import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { Prisma } from '@prisma/client';

/**
 * GET /api/admin/finance/vendor-sales
 * Get detailed vendor sales data for admin
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;
    const businessType = searchParams.get('businessType');

    // Build date filter
    const dateFilter: Prisma.VendorOrderWhereInput = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) {
        dateFilter.createdAt.gte = startDate;
      }
      if (endDate) {
        dateFilter.createdAt.lte = endDate;
      }
    }

    // Build business type filter
    const vendorFilter: Prisma.VendorProfileWhereInput = {};
    if (businessType) {
      vendorFilter.businessType = businessType;
    }

    // Get all completed vendor orders
    const vendorOrders = await db.vendorOrder.findMany({
      where: {
        status: 'Completed',
        ...dateFilter
      },
      include: {
        items: true,
        user: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // Get all vendors
    const vendors = await db.vendorProfile.findMany({
      where: Object.keys(vendorFilter).length > 0 ? vendorFilter : undefined,
      select: {
        id: true,
        businessName: true,
        userId: true,
        businessType: true,
        eventParticipations: {
          select: {
            event: {
              select: {
                id: true,
                title: true
              }
            }
          }
        }
      }
    });

    // Create a map of vendor ID to vendor details
    const vendorMap: Record<string, any> = {};
    vendors.forEach(vendor => {
      vendorMap[vendor.id] = {
        vendorId: vendor.id,
        vendorName: vendor.businessName || 'Unknown Vendor',
        events: (vendor.eventParticipations || []).map(e => ({
          eventId: e.event.id,
          eventTitle: e.event.title
        }))
      };
    });

    // Aggregate vendor sales by vendor and event
    const vendorSales: any[] = [];

    // Group orders by vendor
    const vendorOrdersMap: Record<string, any[]> = {};
    vendorOrders.forEach(order => {
      const vendorId = order.vendorId;
      if (!vendorOrdersMap[vendorId]) {
        vendorOrdersMap[vendorId] = [];
      }
      vendorOrdersMap[vendorId].push(order);
    });

    // Process each vendor's orders
    Object.keys(vendorOrdersMap).forEach(vendorId => {
      const orders = vendorOrdersMap[vendorId];
      const vendorInfo = vendorMap[vendorId] || {
        vendorId,
        vendorName: 'Unknown Vendor',
        events: []
      };

      // Calculate total revenue and order count
      const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
      const orderCount = orders.length;

      // Calculate platform fees (6% of total revenue)
      const platformFees = totalRevenue * 0.06;

      // Calculate net revenue
      const netRevenue = totalRevenue - platformFees;

      // Add to vendor sales array
      vendorSales.push({
        vendorId: vendorInfo.vendorId,
        vendorName: vendorInfo.vendorName,
        eventId: vendorInfo.events[0]?.eventId || 'unknown',
        eventTitle: vendorInfo.events[0]?.eventTitle || 'Unknown Event',
        orderCount,
        totalRevenue,
        platformFees,
        netRevenue
      });
    });

    // Sort by total revenue
    vendorSales.sort((a, b) => b.totalRevenue - a.totalRevenue);

    return NextResponse.json(vendorSales);
  } catch (error) {
    console.error('Error fetching vendor sales data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch vendor sales data' },
      { status: 500 }
    );
  }
}
