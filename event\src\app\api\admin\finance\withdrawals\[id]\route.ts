import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * PATCH /api/admin/finance/withdrawals/:id
 * Approve or reject a withdrawal request
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      return NextResponse.json(
        { error: 'You do not have permission to perform this action' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const withdrawalId = resolvedParams.id;
    const { action, notes } = await req.json();

    // Validate the action
    if (!action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "approve" or "reject".' },
        { status: 400 }
      );
    }

    // Get the withdrawal with user information
    const withdrawal = await db.withdrawal.findUnique({
      where: { id: withdrawalId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            accountBalance: true
          }
        },
        bankAccount: true
      }
    });

    if (!withdrawal) {
      return NextResponse.json(
        { error: 'Withdrawal not found' },
        { status: 404 }
      );
    }

    // Check if withdrawal is still pending
    if (withdrawal.status !== 'Pending') {
      return NextResponse.json(
        { error: 'Withdrawal has already been processed' },
        { status: 400 }
      );
    }

    if (action === 'approve') {
      // Check if user still has sufficient balance
      if (withdrawal.user.accountBalance < withdrawal.amount) {
        return NextResponse.json(
          { error: 'User has insufficient balance for this withdrawal' },
          { status: 400 }
        );
      }

      // Update withdrawal status and deduct from user balance
      const updatedWithdrawal = await db.$transaction(async (prisma) => {
        // Update withdrawal status
        const updated = await prisma.withdrawal.update({
          where: { id: withdrawalId },
          data: {
            status: 'Approved',
            processedDate: new Date(),
            adminNotes: notes || `Approved by admin (${user.name || user.email})`
          }
        });

        // Deduct amount from user's account balance
        await prisma.user.update({
          where: { id: withdrawal.userId },
          data: {
            accountBalance: {
              decrement: withdrawal.amount
            }
          }
        });

        // Create a transaction record
        await prisma.financialTransaction.create({
          data: {
            userId: withdrawal.userId,
            amount: -withdrawal.amount,
            type: 'WITHDRAWAL',
            status: 'COMPLETED',
            description: `Withdrawal approved - ${withdrawal.reference || withdrawal.id}`,
            metadata: {
              withdrawalId: withdrawal.id,
              processedBy: user.id,
              notes: notes || 'Approved by admin'
            }
          }
        });

        return updated;
      });

      return NextResponse.json({
        message: 'Withdrawal approved successfully',
        withdrawal: updatedWithdrawal
      });

    } else if (action === 'reject') {
      // Update withdrawal status to rejected
      const updatedWithdrawal = await db.withdrawal.update({
        where: { id: withdrawalId },
        data: {
          status: 'Rejected',
          processedDate: new Date(),
          adminNotes: notes || `Rejected by admin (${user.name || user.email})`
        }
      });

      return NextResponse.json({
        message: 'Withdrawal rejected successfully',
        withdrawal: updatedWithdrawal
      });
    }

  } catch (error) {
    console.error('Error processing withdrawal:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/finance/withdrawals/:id
 * Get details of a specific withdrawal request
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const resolvedParams = await params;
    const withdrawalId = resolvedParams.id;

    // Get withdrawal with all related information
    const withdrawal = await db.withdrawal.findUnique({
      where: { id: withdrawalId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            accountBalance: true
          }
        },
        bankAccount: {
          select: {
            bankName: true,
            accountName: true,
            accountNumber: true,
            routingNumber: true
          }
        }
      }
    });

    if (!withdrawal) {
      return NextResponse.json(
        { error: 'Withdrawal not found' },
        { status: 404 }
      );
    }

    // Format withdrawal for response
    const formattedWithdrawal = {
      id: withdrawal.id,
      amount: withdrawal.amount,
      status: withdrawal.status,
      requestDate: withdrawal.requestDate.toISOString(),
      processedDate: withdrawal.processedDate?.toISOString(),
      reference: withdrawal.reference || withdrawal.id,
      notes: withdrawal.notes,
      user: {
        id: withdrawal.user.id,
        name: withdrawal.user.name,
        email: withdrawal.user.email,
        role: withdrawal.user.role,
        accountBalance: withdrawal.user.accountBalance
      },
      bankAccount: withdrawal.bankAccount ? {
        bankName: withdrawal.bankAccount.bankName,
        accountName: withdrawal.bankAccount.accountName,
        accountNumber: withdrawal.bankAccount.accountNumber,
        routingNumber: withdrawal.bankAccount.routingNumber
      } : null
    };

    return NextResponse.json(formattedWithdrawal);

  } catch (error) {
    console.error('Error fetching withdrawal details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
