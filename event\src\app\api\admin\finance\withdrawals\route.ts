import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/finance/withdrawals
 * Get all withdrawal requests for admin finance dashboard
 */
export async function GET(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can access withdrawal data
    if (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get URL parameters
    const { searchParams } = new URL(req.url);
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    const skip = (page - 1) * limit;

    // Build the where clause
    const where: any = {};

    if (status && status !== 'all') {
      where.status = status;
    }

    if (search) {
      where.OR = [
        {
          user: {
            name: {
              contains: search,
              mode: 'insensitive'
            }
          }
        },
        {
          user: {
            email: {
              contains: search,
              mode: 'insensitive'
            }
          }
        },
        {
          reference: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          amount: {
            equals: isNaN(parseFloat(search)) ? undefined : parseFloat(search)
          }
        }
      ].filter(condition => condition.amount?.equals !== undefined || condition.user || condition.reference);
    }

    // Get withdrawals with user and bank account information
    const withdrawals = await db.withdrawal.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        bankAccount: {
          select: {
            bankName: true,
            accountNumber: true,
            accountName: true
          }
        }
      },
      orderBy: { requestDate: 'desc' },
      skip,
      take: limit
    });

    // Get total count for pagination
    const totalCount = await db.withdrawal.count({ where });

    // Calculate summary statistics
    const pendingCount = await db.withdrawal.count({
      where: { status: 'Pending' }
    });

    const pendingAmount = await db.withdrawal.aggregate({
      where: { status: 'Pending' },
      _sum: { amount: true }
    });

    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    const todayCount = await db.withdrawal.count({
      where: {
        requestDate: {
          gte: todayStart
        }
      }
    });

    const todayAmount = await db.withdrawal.aggregate({
      where: {
        requestDate: {
          gte: todayStart
        }
      },
      _sum: { amount: true }
    });

    // Format withdrawals for response
    const formattedWithdrawals = withdrawals.map(withdrawal => ({
      id: withdrawal.id,
      amount: withdrawal.amount,
      status: withdrawal.status,
      requestDate: withdrawal.requestDate.toISOString(),
      processedDate: withdrawal.processedDate?.toISOString(),
      reference: withdrawal.reference || withdrawal.id,
      method: withdrawal.bankAccount
        ? `${withdrawal.bankAccount.bankName}`
        : 'Bank Transfer',
      notes: withdrawal.notes,
      user: {
        id: withdrawal.user.id,
        name: withdrawal.user.name,
        email: withdrawal.user.email
      },
      bankAccount: withdrawal.bankAccount ? {
        bankName: withdrawal.bankAccount.bankName,
        accountName: withdrawal.bankAccount.accountName,
        accountNumber: withdrawal.bankAccount.accountNumber
      } : undefined
    }));

    return NextResponse.json({
      withdrawals: formattedWithdrawals,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      },
      summary: {
        pendingCount,
        pendingAmount: pendingAmount._sum.amount || 0,
        todayCount,
        todayAmount: todayAmount._sum.amount || 0
      }
    });

  } catch (error) {
    console.error('Error fetching withdrawals:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
