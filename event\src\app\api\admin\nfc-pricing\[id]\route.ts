import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

// Create a direct instance for this API route
const prisma = new PrismaClient();

/**
 * GET /api/admin/nfc-pricing/:id
 * Get a specific NFC product pricing by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the authenticated user from the session
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({
        error: 'Not authenticated'
      }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json({
        error: 'Unauthorized. Admin access required'
      }, { status: 403 });
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;

    // Get the pricing using Prisma's findUnique method
    const pricingRecord = await prisma.nFCProductPricing.findUnique({
      where: { id }
    });

    if (!pricingRecord) {
      return NextResponse.json({
        error: 'NFC product pricing not found'
      }, { status: 404 });
    }

    return NextResponse.json(pricingRecord);

  } catch (error) {
    console.error('Error fetching NFC product pricing:', error);
    return NextResponse.json({
      error: 'Failed to fetch NFC product pricing'
    }, { status: 500 });
  }
}

/**
 * PUT /api/admin/nfc-pricing/:id
 * Update an NFC product pricing
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the authenticated user from the session
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({
        error: 'Not authenticated'
      }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json({
        error: 'Unauthorized. Admin access required'
      }, { status: 403 });
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;

    // Parse the request body
    const body = await request.json();
    const { name, description, price, isActive, imageUrl } = body;

    // Check if pricing exists using Prisma's findUnique method
    const existingPricingRecord = await prisma.nFCProductPricing.findUnique({
      where: { id }
    });

    if (!existingPricingRecord) {
      return NextResponse.json({
        error: 'NFC product pricing not found'
      }, { status: 404 });
    }

    // Update the pricing using Prisma's update method
    const now = new Date().toISOString();

    // Prepare the data to update
    const updateData: any = {
      updatedAt: new Date(now)
    };

    if (name !== undefined) {
      updateData.name = name;
    }

    if (description !== undefined) {
      updateData.description = description;
    }

    if (price !== undefined) {
      updateData.price = price;
    }

    if (isActive !== undefined) {
      updateData.isActive = isActive;
    }

    if (imageUrl !== undefined) {
      updateData.imageUrl = imageUrl;
    }

    // Update the record using Prisma's update method
    await prisma.nFCProductPricing.update({
      where: { id },
      data: updateData
    });

    // Get the updated record using Prisma's findUnique method
    const updatedPricing = await prisma.nFCProductPricing.findUnique({
      where: { id }
    });

    return NextResponse.json(updatedPricing || { success: true });

  } catch (error) {
    console.error('Error updating NFC product pricing:', error);
    return NextResponse.json({
      error: 'Failed to update NFC product pricing'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/admin/nfc-pricing/:id
 * Delete an NFC product pricing
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the authenticated user from the session
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({
        error: 'Not authenticated'
      }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json({
        error: 'Unauthorized. Admin access required'
      }, { status: 403 });
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;

    // Check if pricing exists using Prisma's findUnique method
    const existingPricingRecord = await prisma.nFCProductPricing.findUnique({
      where: { id }
    });

    if (!existingPricingRecord) {
      return NextResponse.json({
        error: 'NFC product pricing not found'
      }, { status: 404 });
    }

    // Delete the pricing using Prisma's delete method
    await prisma.nFCProductPricing.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'NFC product pricing deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting NFC product pricing:', error);
    return NextResponse.json({
      error: 'Failed to delete NFC product pricing'
    }, { status: 500 });
  }
}
