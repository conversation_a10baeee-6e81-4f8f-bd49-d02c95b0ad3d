import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

// Create a direct instance for this API route
const prisma = new PrismaClient();

/**
 * GET /api/admin/nfc-pricing
 * Get all NFC product pricing
 */
export async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from the session
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({
        error: 'Not authenticated'
      }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json({
        error: 'Unauthorized. Admin access required'
      }, { status: 403 });
    }

    // Get all NFC product pricing using raw query to avoid model name issues
    const pricing = await prisma.$queryRaw`
      SELECT * FROM "NFCProductPricing"
      ORDER BY "deviceType" ASC
    `;

    return NextResponse.json(pricing);

  } catch (error) {
    console.error('Error fetching NFC product pricing:', error);
    return NextResponse.json({
      error: 'Failed to fetch NFC product pricing'
    }, { status: 500 });
  }
}

/**
 * POST /api/admin/nfc-pricing
 * Create a new NFC product pricing
 */
export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user from the session
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({
        error: 'Not authenticated'
      }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json({
        error: 'Unauthorized. Admin access required'
      }, { status: 403 });
    }

    // Parse the request body
    const body = await request.json();
    const { deviceType, name, description, price, currency, isActive, imageUrl } = body;

    // Validate required fields
    if (!deviceType || !name || price === undefined) {
      return NextResponse.json({
        error: 'Missing required fields: deviceType, name, and price are required'
      }, { status: 400 });
    }

    // Check if pricing for this device type and currency already exists using raw query
    const existingPricing = await prisma.$queryRaw`
      SELECT * FROM "NFCProductPricing"
      WHERE "deviceType" = ${deviceType} AND "currency" = ${currency || 'USD'}
      LIMIT 1
    `;

    // Convert result to expected format
    const existingPricingRecord = Array.isArray(existingPricing) && existingPricing.length > 0
      ? existingPricing[0]
      : null;

    if (existingPricingRecord) {
      return NextResponse.json({
        error: 'Pricing for this device type and currency already exists'
      }, { status: 400 });
    }

    // Create the pricing using raw query
    const now = new Date().toISOString();
    const newPricing = await prisma.$executeRaw`
      INSERT INTO "NFCProductPricing" (
        "id", "deviceType", "name", "description", "price", "currency", "isActive", "imageUrl", "createdAt", "updatedAt"
      ) VALUES (
        gen_random_uuid(), ${deviceType}, ${name}, ${description}, ${price}, ${currency || 'USD'},
        ${isActive !== undefined ? isActive : true}, ${imageUrl}, ${now}, ${now}
      )
      RETURNING *
    `;

    // Get the newly created record
    const newPricingRecord = await prisma.$queryRaw`
      SELECT * FROM "NFCProductPricing"
      WHERE "deviceType" = ${deviceType} AND "currency" = ${currency || 'USD'}
      ORDER BY "createdAt" DESC
      LIMIT 1
    `;

    return NextResponse.json(Array.isArray(newPricingRecord) && newPricingRecord.length > 0
      ? newPricingRecord[0]
      : { success: true }, { status: 201 });

  } catch (error) {
    console.error('Error creating NFC product pricing:', error);
    return NextResponse.json({
      error: 'Failed to create NFC product pricing'
    }, { status: 500 });
  }
}
