import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/nfc/analytics
 * Get NFC analytics data for admin dashboard
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const timeRange = searchParams.get('timeRange') || 'week';
    const vendorId = searchParams.get('vendorId');
    const eventId = searchParams.get('eventId');

    // Calculate date range based on time range
    const endDate = new Date();
    let startDate = new Date();
    
    switch (timeRange) {
      case 'day':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case 'year':
        startDate.setDate(startDate.getDate() - 365);
        break;
      default:
        startDate.setDate(startDate.getDate() - 7);
    }

    // Build where clause for transactions
    const whereClause: any = {
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    };

    // Add vendor filter if specified
    if (vendorId) {
      whereClause.vendorId = vendorId;
    }

    // Add event filter if specified
    if (eventId) {
      whereClause.eventId = eventId;
    }

    // Get transactions within date range
    const transactions = await db.vendorNFCTransaction.findMany({
      where: whereClause,
      include: {
        event: {
          select: {
            id: true,
            title: true
          }
        },
        vendor: {
          select: {
            id: true,
            businessName: true
          }
        },
        user: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    // Calculate total revenue and transaction count
    const totalTransactions = transactions.length;
    const totalRevenue = transactions.reduce((sum, tx) => sum + tx.amount, 0);
    
    // Get active cards count
    const activeCards = await db.nFCCard.count({
      where: { isActive: true }
    });
    
    // Get active vendors count
    const activeVendors = await db.vendorProfile.count({
      where: {
        verificationStatus: 'APPROVED'
      }
    });

    // Group transactions by day
    const transactionsByDay = groupTransactionsByDay(transactions);
    
    // Group transactions by status
    const transactionsByStatus = groupTransactionsByStatus(transactions);
    
    // Group transactions by vendor
    const transactionsByVendor = groupTransactionsByVendor(transactions);
    
    // Group transactions by event
    const transactionsByEvent = groupTransactionsByEvent(transactions);

    return NextResponse.json({
      totalRevenue,
      totalTransactions,
      activeCards,
      activeVendors,
      transactionsByDay,
      transactionsByStatus,
      transactionsByVendor,
      transactionsByEvent
    });
  } catch (error) {
    console.error('Error fetching NFC analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch NFC analytics data' },
      { status: 500 }
    );
  }
}

// Helper function to group transactions by day
function groupTransactionsByDay(transactions: any[]) {
  const groupedByDay = new Map();
  
  transactions.forEach(tx => {
    const date = tx.createdAt.toISOString().split('T')[0];
    
    if (!groupedByDay.has(date)) {
      groupedByDay.set(date, {
        date,
        completed: 0,
        pending: 0,
        failed: 0
      });
    }
    
    const dayData = groupedByDay.get(date);
    
    if (tx.status === 'COMPLETED') {
      dayData.completed += 1;
    } else if (tx.status === 'PENDING') {
      dayData.pending += 1;
    } else if (tx.status === 'FAILED' || tx.status === 'CANCELLED') {
      dayData.failed += 1;
    }
    
    groupedByDay.set(date, dayData);
  });
  
  return Array.from(groupedByDay.values()).sort((a, b) => {
    return new Date(a.date).getTime() - new Date(b.date).getTime();
  });
}

// Helper function to group transactions by status
function groupTransactionsByStatus(transactions: any[]) {
  const statusColors = {
    'COMPLETED': '#10b981',
    'PENDING': '#f59e0b',
    'FAILED': '#ef4444',
    'CANCELLED': '#6b7280'
  };
  
  const groupedByStatus = new Map();
  
  transactions.forEach(tx => {
    if (!groupedByStatus.has(tx.status)) {
      groupedByStatus.set(tx.status, {
        status: tx.status,
        count: 0,
        color: statusColors[tx.status as keyof typeof statusColors] || '#6b7280'
      });
    }
    
    const statusData = groupedByStatus.get(tx.status);
    statusData.count += 1;
    groupedByStatus.set(tx.status, statusData);
  });
  
  return Array.from(groupedByStatus.values());
}

// Helper function to group transactions by vendor
function groupTransactionsByVendor(transactions: any[]) {
  const groupedByVendor = new Map();
  
  transactions.forEach(tx => {
    if (!tx.vendor) return;
    
    const vendorId = tx.vendor.id;
    const vendorName = tx.vendor.businessName;
    
    if (!groupedByVendor.has(vendorId)) {
      groupedByVendor.set(vendorId, {
        id: vendorId,
        name: vendorName,
        transactions: 0,
        revenue: 0
      });
    }
    
    const vendorData = groupedByVendor.get(vendorId);
    vendorData.transactions += 1;
    
    if (tx.status === 'COMPLETED') {
      vendorData.revenue += tx.amount;
    }
    
    groupedByVendor.set(vendorId, vendorData);
  });
  
  return Array.from(groupedByVendor.values())
    .sort((a, b) => b.revenue - a.revenue);
}

// Helper function to group transactions by event
function groupTransactionsByEvent(transactions: any[]) {
  const groupedByEvent = new Map();
  
  transactions.forEach(tx => {
    if (!tx.event) return;
    
    const eventId = tx.event.id;
    const eventName = tx.event.title;
    
    if (!groupedByEvent.has(eventId)) {
      groupedByEvent.set(eventId, {
        id: eventId,
        name: eventName,
        transactions: 0,
        revenue: 0
      });
    }
    
    const eventData = groupedByEvent.get(eventId);
    eventData.transactions += 1;
    
    if (tx.status === 'COMPLETED') {
      eventData.revenue += tx.amount;
    }
    
    groupedByEvent.set(eventId, eventData);
  });
  
  return Array.from(groupedByEvent.values())
    .sort((a, b) => b.revenue - a.revenue);
}
