import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/nfc/cards/{id}
 * Get details of a specific NFC card
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const cardId = resolvedParams.id;

    // Get card with related data (no event relation)
    const card = await db.nFCCard.findUnique({
      where: { id: cardId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        nfcTransactions: {
          take: 10,
          orderBy: {
            createdAt: 'desc'
          },
          include: {
            vendor: {
              select: {
                id: true,
                businessName: true
              }
            }
          }
        },
        _count: {
          select: {
            nfcTransactions: true
          }
        }
      }
    });

    // Fetch event details using eventId
    let eventData = null;
    if (card && card.eventId) {
      const event = await db.event.findUnique({
        where: { id: card.eventId },
        select: {
          id: true,
          title: true,
          location: true,
          startDate: true,
          endDate: true
        }
      });
      if (event) {
        eventData = {
          id: event.id,
          title: event.title,
          location: event.location,
          startDate: event.startDate ? event.startDate.toISOString() : null,
          endDate: event.endDate ? event.endDate.toISOString() : null
        };
      }
    }
    if (!card) {
      return NextResponse.json(
        { error: 'Card not found' },
        { status: 404 }
      );
    }

    // Format card for the frontend
    const formattedCard = {
      id: card.id,
      uid: card.uid,
      isActive: card.isActive,
      status: card.status,
      balance: card.balance,
      assignedTo: card.assignedTo,
      lastUsed: card.lastUsed ? card.lastUsed.toISOString() : null,
      createdAt: card.createdAt.toISOString(),
      updatedAt: card.updatedAt.toISOString(),
      event: eventData,
      user: card.user ? {
        id: card.user.id,
        name: card.user.name,
        email: card.user.email,
        image: card.user.image
      } : null,
      recentTransactions: card.nfcTransactions.map(tx => ({
        id: tx.id,
        amount: tx.amount,
        status: tx.status,
        createdAt: tx.createdAt.toISOString(),
        vendor: tx.vendor ? {
          id: tx.vendor.id,
          name: tx.vendor.businessName
        } : null
      })),
      totalTransactions: card._count.nfcTransactions
    };

    return NextResponse.json(formattedCard);
  } catch (error) {
    console.error('Error fetching NFC card:', error);
    return NextResponse.json(
      { error: 'Failed to fetch NFC card details' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/nfc/cards/{id}
 * Update a specific NFC card
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const cardId = resolvedParams.id;
    const { isActive, status, balance, assignedTo, userId } = await request.json();

    // Get the card
    const card = await db.nFCCard.findUnique({
      where: { id: cardId }
    });

    if (!card) {
      return NextResponse.json(
        { error: 'Card not found' },
        { status: 404 }
      );
    }

    // Check if user exists (if provided)
    if (userId) {
      const userExists = await db.user.findUnique({
        where: { id: userId }
      });

      if (!userExists) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }
    }

    // Update the card
    const updatedCard = await db.nFCCard.update({
      where: { id: cardId },
      data: {
        ...(isActive !== undefined && { isActive }),
        ...(status && { status }),
        ...(balance !== undefined && { balance }),
        ...(assignedTo !== undefined && { assignedTo }),
        ...(userId !== undefined && { userId })
      }
    });

    return NextResponse.json({
      message: 'Card updated successfully',
      card: {
        id: updatedCard.id,
        uid: updatedCard.uid,
        isActive: updatedCard.isActive,
        status: updatedCard.status,
        balance: updatedCard.balance,
        assignedTo: updatedCard.assignedTo,
        userId: updatedCard.userId,
        updatedAt: updatedCard.updatedAt.toISOString()
      }
    });
  } catch (error) {
    console.error('Error updating NFC card:', error);
    return NextResponse.json(
      { error: 'Failed to update NFC card' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/nfc/cards/{id}
 * Delete a specific NFC card
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const cardId = resolvedParams.id;

    // Check if card exists
    const card = await db.nFCCard.findUnique({
      where: { id: cardId },
      include: {
        _count: {
          select: {
            nfcTransactions: true
          }
        }
      }
    });

    if (!card) {
      return NextResponse.json(
        { error: 'Card not found' },
        { status: 404 }
      );
    }

    // Check if card has transactions
    if (card._count.nfcTransactions > 0) {
      return NextResponse.json(
        { error: 'Cannot delete card with transactions. Deactivate it instead.' },
        { status: 400 }
      );
    }

    // Delete the card
    await db.nFCCard.delete({
      where: { id: cardId }
    });

    return NextResponse.json({
      message: 'Card deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting NFC card:', error);
    return NextResponse.json(
      { error: 'Failed to delete NFC card' },
      { status: 500 }
    );
  }
}
