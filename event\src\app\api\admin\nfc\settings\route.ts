import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { z } from 'zod';

// Schema for validating settings
const nfcGlobalSettingsSchema = z.object({
  systemName: z.string().min(1, 'System name is required'),
  defaultCurrencySymbol: z.string().min(1, 'Currency symbol is required'),
  defaultLanguage: z.string().min(2, 'Language code is required'),
  systemEnabled: z.boolean(),

  // Security settings
  maxTransactionAmount: z.number().positive('Maximum transaction amount must be positive'),
  requirePinForHighValue: z.boolean(),
  highValueThreshold: z.number().positive('High value threshold must be positive').optional(),
  cardLockoutThreshold: z.number().int().positive('Card lockout threshold must be a positive integer'),
  enforceCardLimits: z.boolean(),
  securityLevel: z.string(),

  // Offline mode settings
  allowOfflineMode: z.boolean(),
  maxOfflineTransactions: z.number().int().positive('Maximum offline transactions must be a positive integer').optional(),
  offlineTransactionLimit: z.number().positive('Offline transaction limit must be positive').optional(),
  syncInterval: z.number().int().positive('Sync interval must be a positive integer').optional(),
  requireSyncAfter: z.number().int().positive('Require sync after must be a positive integer').optional(),

  // Performance settings
  cacheTimeout: z.number().int().positive('Cache timeout must be a positive integer'),
  batchSize: z.number().int().positive('Batch size must be a positive integer'),
  logLevel: z.string(),
  analyticsEnabled: z.boolean(),
  receiptEnabled: z.boolean()
});

/**
 * GET /api/admin/nfc/settings
 * Get global NFC system settings
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Return default settings since GlobalSettings model might not exist yet
    return NextResponse.json({
      systemName: 'Global NFC Payment System',
      defaultCurrencySymbol: '$',
      defaultLanguage: 'en',
      systemEnabled: true,

      // Security settings
      maxTransactionAmount: 1000,
      requirePinForHighValue: true,
      highValueThreshold: 200,
      cardLockoutThreshold: 5,
      enforceCardLimits: true,
      securityLevel: 'standard',

      // Offline mode settings
      allowOfflineMode: true,
      maxOfflineTransactions: 100,
      offlineTransactionLimit: 50,
      syncInterval: 15,
      requireSyncAfter: 24,

      // Performance settings
      cacheTimeout: 30,
      batchSize: 100,
      logLevel: 'info',
      analyticsEnabled: true,
      receiptEnabled: true
    });

  } catch (error) {
    console.error('Error fetching NFC settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch NFC system settings' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/nfc/settings
 * Update global NFC system settings
 */
export async function PUT(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();

    try {
      nfcGlobalSettingsSchema.parse(body);
    } catch (validationError) {
      return NextResponse.json(
        { error: 'Invalid settings data', details: validationError },
        { status: 400 }
      );
    }

    // Since we can't save to the database yet (GlobalSettings model might not exist),
    // just return success with the provided settings
    return NextResponse.json({
      message: 'Settings updated successfully (Note: Changes are not persisted until database migration is complete)',
      settings: body
    });

  } catch (error) {
    console.error('Error saving NFC settings:', error);
    return NextResponse.json(
      { error: 'Failed to save NFC system settings' },
      { status: 500 }
    );
  }
}
