import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/nfc/transactions/{id}
 * Get details of a specific NFC transaction
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const transactionId = resolvedParams.id;

    // Get transaction with related data
    const transaction = await db.vendorNFCTransaction.findUnique({
      where: { id: transactionId },
      include: {
        event: true,
        vendor: true,
        user: true,
        products: true
      }
    });

    if (!transaction) {
      return NextResponse.json(
        { error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Format transaction for the frontend
    const formattedTransaction = {
      id: transaction.id,
      cardId: transaction.cardId,
      amount: transaction.amount,
      status: transaction.status,
      createdAt: transaction.createdAt.toISOString(),
      processedAt: transaction.processedAt ? transaction.processedAt.toISOString() : null,
      event: {
        id: transaction.event.id,
        title: transaction.event.title,
        location: transaction.event.location,
        startDate: transaction.event.startDate.toISOString(),
        endDate: transaction.event.endDate.toISOString()
      },
      vendor: {
        id: transaction.vendor.id,
        businessName: transaction.vendor.businessName,
        email: transaction.vendor.email,
        phoneNumber: transaction.vendor.phoneNumber
      },
      user: {
        id: transaction.user.id,
        name: transaction.user.name,
        email: transaction.user.email,
        image: transaction.user.image
      },
      products: transaction.products.map(product => ({
        id: product.id,
        quantity: product.quantity,
        unitPrice: product.unitPrice,
        totalPrice: product.totalPrice,
        productId: product.productId
      })),
      notes: transaction.notes
    };

    return NextResponse.json(formattedTransaction);
  } catch (error) {
    console.error('Error fetching NFC transaction:', error);
    return NextResponse.json(
      { error: 'Failed to fetch NFC transaction details' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/nfc/transactions/{id}
 * Update the status of a specific NFC transaction
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const transactionId = resolvedParams.id;
    const { status, notes } = await request.json();

    // Validate status
    const validStatuses = ['COMPLETED', 'FAILED', 'PENDING', 'REFUNDED', 'CANCELLED'];
    if (status && !validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status value. Must be one of: ' + validStatuses.join(', ') },
        { status: 400 }
      );
    }

    // Get the transaction
    const transaction = await db.vendorNFCTransaction.findUnique({
      where: { id: transactionId }
    });

    if (!transaction) {
      return NextResponse.json(
        { error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Update the transaction
    const updatedTransaction = await db.vendorNFCTransaction.update({
      where: { id: transactionId },
      data: {
        ...(status && { status }),
        ...(notes && { notes }),
        ...(status === 'COMPLETED' && { processedAt: new Date() }),
        ...(status === 'REFUNDED' && { refundedAt: new Date() })
      }
    });

    // If status is changed to refunded, update the card balance
    if (status === 'REFUNDED' && transaction.status !== 'REFUNDED') {
      // Get the card
      const card = await db.nFCCard.findFirst({
        where: { uid: transaction.cardId }
      });

      if (card) {
        // Refund the amount to the card
        await db.nFCCard.update({
          where: { id: card.id },
          data: {
            balance: {
              increment: transaction.amount
            }
          }
        });
      }
    }

    return NextResponse.json({
      message: 'Transaction updated successfully',
      transaction: {
        id: updatedTransaction.id,
        status: updatedTransaction.status,
        notes: updatedTransaction.notes,
        processedAt: updatedTransaction.processedAt ? updatedTransaction.processedAt.toISOString() : null,
        // refundedAt removed, not present in model
      }
    });
  } catch (error) {
    console.error('Error updating NFC transaction:', error);
    return NextResponse.json(
      { error: 'Failed to update NFC transaction' },
      { status: 500 }
    );
  }
}
