import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/nfc/transactions
 * Get all NFC transactions with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
    const status = url.searchParams.get('status') || 'all';
    const eventId = url.searchParams.get('event') || 'all';
    const vendorId = url.searchParams.get('vendor') || 'all';
    const search = url.searchParams.get('search') || '';
    const sortField = url.searchParams.get('sortField') || 'createdAt';
    const sortDirection = url.searchParams.get('sortDirection') || 'desc';
    const fromDate = url.searchParams.get('fromDate');
    const toDate = url.searchParams.get('toDate');

    // Build filter conditions
    const where: any = {};

    // Status filter
    if (status !== 'all') {
      where.status = status;
    }

    // Event filter
    if (eventId !== 'all') {
      where.eventId = eventId;
    }

    // Vendor filter
    if (vendorId !== 'all') {
      where.vendorId = vendorId;
    }

    // Date range filter
    if (fromDate || toDate) {
      where.createdAt = {};

      if (fromDate) {
        where.createdAt.gte = new Date(fromDate);
      }

      if (toDate) {
        // Add one day to include the end date
        const endDate = new Date(toDate);
        endDate.setDate(endDate.getDate() + 1);
        where.createdAt.lt = endDate;
      }
    }

    // Search filter
    if (search) {
      where.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { cardId: { contains: search, mode: 'insensitive' } },
        { user: { name: { contains: search, mode: 'insensitive' } } },
        { event: { title: { contains: search, mode: 'insensitive' } } },
        { vendor: { businessName: { contains: search, mode: 'insensitive' } } }
      ];
    }

    // Build sort options
    const orderBy: any = {};

    // Map frontend sort fields to database fields
    const sortFieldMap: Record<string, string> = {
      'timestamp': 'createdAt',
      'amount': 'amount',
      'status': 'status',
      'cardId': 'cardId',
      'id': 'id',
      'event': 'event.title',
      'vendor': 'vendor.businessName'
    };

    // Handle special cases for related fields
    if (sortField === 'event') {
      orderBy.event = { title: sortDirection };
    } else if (sortField === 'vendor') {
      orderBy.vendor = { businessName: sortDirection };
    } else {
      // Use mapped field or default to the provided field
      const dbField = sortFieldMap[sortField] || sortField;
      orderBy[dbField] = sortDirection;
    }

    // Get total count for pagination
    const totalCount = await db.vendorNFCTransaction.count({ where });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSize);

    // Get transactions with pagination
    const transactions = await db.vendorNFCTransaction.findMany({
      where,
      orderBy,
      skip: (page - 1) * pageSize,
      take: pageSize,
      include: {
        event: {
          select: {
            id: true,
            title: true
          }
        },
        vendor: {
          select: {
            id: true,
            businessName: true
          }
        },
        user: {
          select: {
            id: true,
            name: true
          }
        },
        products: true
      }
    });

    // Calculate total amount
    const totalAmountResult = await db.vendorNFCTransaction.aggregate({
      where,
      _sum: {
        amount: true
      }
    });

    const totalAmount = totalAmountResult._sum.amount || 0;

    // Format transactions for the frontend
    const formattedTransactions = transactions.map(tx => ({
      id: tx.id,
      cardId: tx.cardId,
      amount: tx.amount,
      status: tx.status,
      timestamp: tx.createdAt.toISOString(),
      event: {
        id: tx.event.id,
        name: tx.event.title
      },
      vendor: {
        id: tx.vendor.id,
        name: tx.vendor.businessName
      },
      user: {
        id: tx.user.id,
        name: tx.user.name || 'Unknown User'
      },
      products: tx.products.map(product => ({
        id: product.id,
        quantity: product.quantity,
        price: product.unitPrice
      }))
    }));

    // Get all events for filters
    // First get all events with NFC system settings
    const nfcSettings = await db.nFCSystemSettings.findMany({
      select: {
        eventId: true
      }
    });

    const events = await db.event.findMany({
      where: {
        id: {
          in: nfcSettings.map(setting => setting.eventId)
        }
      },
      select: {
        id: true,
        title: true
      },
      orderBy: {
        title: 'asc'
      }
    });

    // Get all vendors for filters
    const vendors = await db.vendorProfile.findMany({
      where: {
        verificationStatus: 'APPROVED'
      },
      select: {
        id: true,
        businessName: true
      },
      orderBy: {
        businessName: 'asc'
      }
    });

    return NextResponse.json({
      transactions: formattedTransactions,
      totalCount,
      totalPages,
      totalAmount,
      currentPage: page,
      events: events.map(event => ({ id: event.id, name: event.title })),
      vendors: vendors.map(vendor => ({ id: vendor.id, name: vendor.businessName }))
    });
  } catch (error) {
    console.error('Error fetching NFC transactions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch NFC transactions' },
      { status: 500 }
    );
  }
}
