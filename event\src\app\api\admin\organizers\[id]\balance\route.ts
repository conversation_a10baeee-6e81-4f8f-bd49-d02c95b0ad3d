import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function PATCH(request: NextRequest) {
  const id = request.nextUrl.pathname.split('/')[4]; // Extract ID from URL
  try {
    const user = await currentUser();

    // Check if user is admin or superadmin
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const organizerId = id;

    // Get the request body
    const body = await request.json();
    const { amount, action, reason } = body;

    // Validate required fields
    if (amount === undefined || !action) {
      return NextResponse.json(
        { error: 'Amount and action are required' },
        { status: 400 }
      );
    }

    // Parse amount to number
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      return NextResponse.json(
        { error: 'Amount must be a positive number' },
        { status: 400 }
      );
    }

    // Verify action is valid
    if (action !== 'add' && action !== 'subtract') {
      return NextResponse.json(
        { error: 'Action must be either "add" or "subtract"' },
        { status: 400 }
      );
    }

    // Get current organizer
    const organizer = await db.user.findUnique({
      where: { id: organizerId },
      select: {
        id: true,
        name: true,
        email: true,
        accountBalance: true,
        role: true
      }
    });

    if (!organizer) {
      return NextResponse.json(
        { error: 'Organizer not found' },
        { status: 404 }
      );
    }

    // Verify the user is an organizer
    if (organizer.role !== 'ORGANIZER') {
      return NextResponse.json(
        { error: 'User is not an organizer' },
        { status: 400 }
      );
    }

    // Calculate new balance
    const currentBalance = organizer.accountBalance || 0;
    const newBalance = action === 'add'
      ? currentBalance + amountNum
      : Math.max(0, currentBalance - amountNum); // Prevent negative balance

    // Update the organizer's balance
    const updatedOrganizer = await db.user.update({
      where: { id: organizerId },
      data: { accountBalance: newBalance },
      select: {
        id: true,
        name: true,
        email: true,
        accountBalance: true
      }
    });

    // Determine transaction type
    const transactionType = action === 'add'
      ? 'DEPOSIT'
      : 'WITHDRAWAL';

    // Create a description
    const description = reason
      ? `Admin ${action === 'add' ? 'added' : 'subtracted'} ${amountNum} to account balance: ${reason}`
      : `Admin ${action === 'add' ? 'added' : 'subtracted'} ${amountNum} to account balance`;

    // Create a financial transaction record
    await db.financialTransaction.create({
      data: {
        userId: organizerId,
        amount: action === 'add' ? amountNum : -amountNum,
        type: transactionType,
        description: description,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    // Log the admin action
    console.log(`Admin ${user.id} (${user.email}) ${action === 'add' ? 'added' : 'subtracted'} ${amountNum} ${action === 'add' ? 'to' : 'from'} organizer ${organizerId} (${organizer.email}) balance`);

    return NextResponse.json({
      success: true,
      organizer: {
        id: updatedOrganizer.id,
        name: updatedOrganizer.name,
        email: updatedOrganizer.email,
        accountBalance: updatedOrganizer.accountBalance
      },
      transaction: {
        type: transactionType,
        amount: action === 'add' ? amountNum : -amountNum,
        description: description,
        date: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error updating organizer balance:', error);
    return NextResponse.json(
      { error: 'Failed to update organizer balance' },
      { status: 500 }
    );
  }
}
