import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { db } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();
    
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin or superadmin
    if (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const resolvedParams = await params;
    const organizerId = resolvedParams.id;

    // Find the organizer
    const organizer = await db.user.findUnique({
      where: { 
        id: organizerId,
        role: 'ORGANIZER'
      },
      include: {
        _count: {
          select: {
            events: true
          }
        }
      }
    });

    if (!organizer) {
      return NextResponse.json({ error: 'Organizer not found' }, { status: 404 });
    }

    // Get events for this organizer
    const events = await db.event.findMany({
      where: { userId: organizer.id },
      select: {
        id: true,
        title: true,
        startDate: true,
        status: true,
        imagePath: true,
        _count: {
          select: {
            orders: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10 // Get more events for the details page
    });

    // Get financial transactions for this organizer
    const transactions = await db.financialTransaction.findMany({
      where: {
        userId: organizer.id
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20 // Get more transactions for the details page
    });

    // Calculate total sales and tickets sold
    const salesData = await db.order.aggregate({
      where: {
        event: {
          userId: organizer.id
        },
        status: 'Completed'
      },
      _sum: {
        totalPrice: true
      }
    });

    // Get all completed order IDs for this organizer
    const completedOrders = await db.order.findMany({
      where: {
        event: {
          userId: organizer.id
        },
        status: 'Completed'
      },
      select: { id: true }
    });

    const completedOrderIds = completedOrders.map(o => o.id);

    // Aggregate tickets for those orders
    const ticketData = await db.ticket.aggregate({
      where: {
        orderId: { in: completedOrderIds }
      },
      _sum: {
        quantity: true
      }
    });

    // Format events data
    const eventDetails = events.map(event => ({
      id: event.id,
      title: event.title,
      startDate: event.startDate.toISOString(),
      status: event.status,
      imagePath: event.imagePath,
      totalOrders: event._count.orders
    }));

    // Format organizer data
    const organizerDetails = {
      id: organizer.id,
      name: organizer.name || 'Unknown',
      email: organizer.email,
      // phone: organizer.phone, // Remove if not present in model
      isVerified: organizer.emailVerified !== null,
      accountBalance: organizer.accountBalance || 0,
      totalEvents: organizer._count.events,
      totalSales: salesData._sum?.totalPrice || 0,
      totalTicketsSold: ticketData._sum?.quantity || 0,
      joinedDate: organizer.createdAt.toISOString(),
      events: eventDetails,
      recentTransactions: transactions.map((tx: any) => ({
        id: tx.id,
        amount: tx.amount,
        type: tx.type,
        description: tx.description,
        date: tx.createdAt.toISOString()
      }))
    };

    return NextResponse.json(organizerDetails);
  } catch (error) {
    console.error('Error fetching organizer details:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
