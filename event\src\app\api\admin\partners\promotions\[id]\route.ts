import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/partners/promotions/:id
 * Get a specific partner promotion
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const promotionId = resolvedParams.id;

    // Get the promotion
    const promotion = await db.partnerPromotion.findUnique({
      where: { id: promotionId },
      include: {
        partner: {
          select: {
            id: true,
            businessName: true,
            partnerType: true,
            logo: true,
          },
        },
        event: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    });

    if (!promotion) {
      return NextResponse.json({ error: 'Promotion not found' }, { status: 404 });
    }

    return NextResponse.json(promotion);
  } catch (error) {
    console.error('Error fetching promotion:', error);
    return NextResponse.json(
      { error: 'Failed to fetch promotion' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/partners/promotions/:id
 * Update a partner promotion
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const promotionId = resolvedParams.id;
    const body = await request.json();

    // Check if the promotion exists
    const promotion = await db.partnerPromotion.findUnique({
      where: { id: promotionId },
    });

    if (!promotion) {
      return NextResponse.json({ error: 'Promotion not found' }, { status: 404 });
    }

    // Update the promotion
    const updatedPromotion = await db.partnerPromotion.update({
      where: { id: promotionId },
      data: {
        title: body.title,
        description: body.description,
        startDate: body.startDate ? new Date(body.startDate) : undefined,
        endDate: body.endDate ? new Date(body.endDate) : undefined,
        discountValue: body.discountValue ? parseFloat(body.discountValue.toString()) : undefined,
        discountType: body.discountType,
        promoCode: body.promoCode,
        isActive: body.isActive,
        maxUses: body.maxUses ? parseInt(body.maxUses.toString()) : undefined,
        imageUrl: body.imageUrl,
      },
      include: {
        partner: {
          select: {
            id: true,
            businessName: true,
            partnerType: true,
            logo: true,
          },
        },
        event: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    });

    return NextResponse.json(updatedPromotion);
  } catch (error) {
    console.error('Error updating promotion:', error);
    return NextResponse.json(
      { error: 'Failed to update promotion' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/partners/promotions/:id
 * Delete a partner promotion
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const promotionId = resolvedParams.id;

    // Check if the promotion exists
    const promotion = await db.partnerPromotion.findUnique({
      where: { id: promotionId },
    });

    if (!promotion) {
      return NextResponse.json({ error: 'Promotion not found' }, { status: 404 });
    }

    // Delete the promotion
    await db.partnerPromotion.delete({
      where: { id: promotionId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting promotion:', error);
    return NextResponse.json(
      { error: 'Failed to delete promotion' },
      { status: 500 }
    );
  }
}
