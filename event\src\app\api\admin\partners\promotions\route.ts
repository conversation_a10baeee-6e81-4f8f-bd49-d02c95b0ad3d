import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/partners/promotions
 * Get all partner promotions
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const partnerId = searchParams.get('partnerId');
    const eventId = searchParams.get('eventId');
    const isActive = searchParams.get('isActive');
    const limit = parseInt(searchParams.get('limit') || '100');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;

    // Build the where clause
    const where: any = {};

    if (partnerId) {
      where.partnerId = partnerId;
    }

    if (eventId) {
      where.eventId = eventId;
    }

    if (isActive !== null) {
      where.isActive = isActive === 'true';
    }

    // Get promotions with pagination
    const promotions = await db.partnerPromotion.findMany({
      where,
      include: {
        partner: {
          select: {
            id: true,
            businessName: true,
            partnerType: true,
            logo: true,
          },
        },
        event: eventId ? {
          select: {
            id: true,
            title: true,
          },
        } : undefined,
      },
      orderBy: [
        { startDate: 'desc' },
        { createdAt: 'desc' },
      ],
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await db.partnerPromotion.count({ where });

    return NextResponse.json({
      promotions,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching partner promotions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch partner promotions' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/partners/promotions
 * Create a new partner promotion
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      partnerId,
      eventId,
      title,
      description,
      startDate,
      endDate,
      discountValue,
      discountType,
      promoCode,
      isActive,
      maxUses,
      imageUrl,
    } = body;

    // Validate required fields
    if (!partnerId || !title || !description || !startDate || !endDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if the partner exists
    const partner = await db.partner.findUnique({
      where: { id: partnerId },
    });

    if (!partner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 });
    }

    // Check if the event exists if eventId is provided
    if (eventId) {
      const event = await db.event.findUnique({
        where: { id: eventId },
      });

      if (!event) {
        return NextResponse.json({ error: 'Event not found' }, { status: 404 });
      }
    }

    // Create the promotion
    const promotion = await db.partnerPromotion.create({
      data: {
        partnerId,
        eventId: eventId || undefined,
        title,
        description,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        discountValue: discountValue ? parseFloat(discountValue.toString()) : undefined,
        discountType,
        promoCode,
        isActive: isActive !== undefined ? isActive : true,
        maxUses: maxUses ? parseInt(maxUses.toString()) : undefined,
        imageUrl,
      },
      include: {
        partner: {
          select: {
            id: true,
            businessName: true,
            partnerType: true,
            logo: true,
          },
        },
        event: eventId ? {
          select: {
            id: true,
            title: true,
          },
        } : undefined,
      },
    });

    return NextResponse.json(promotion);
  } catch (error) {
    console.error('Error creating partner promotion:', error);
    return NextResponse.json(
      { error: 'Failed to create partner promotion' },
      { status: 500 }
    );
  }
}
