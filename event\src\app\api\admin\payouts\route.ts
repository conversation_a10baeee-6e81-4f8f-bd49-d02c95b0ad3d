import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { getSession } from '@/auth';
import { UserSession } from '@/types/session';

// GET: Fetch all event payouts (admin only)
export async function GET(req: NextRequest) {
  try {
    const session = await getSession() as UserSession;

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can access all payouts
    if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const status = searchParams.get('status');
    const userId = searchParams.get('userId');
    const eventId = searchParams.get('eventId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build the where clause
    const where: any = {};
    if (status) {
      where.status = status;
    }
    if (userId) {
      where.userId = userId;
    }
    if (eventId) {
      where.eventId = eventId;
    }

    // Get total count for pagination
    const totalCount = await db.eventPayout.count({ where });

    // Get payouts with pagination
    const payouts = await db.eventPayout.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        event: {
          select: {
            id: true,
            title: true,
            endDate: true,
            status: true,
          },
        },
        withdrawals: {
          select: {
            id: true,
            amount: true,
            status: true,
            requestDate: true,
            processedDate: true,
          },
        },
      },
      orderBy: { requestDate: 'desc' },
      skip,
      take: limit,
    });

    return NextResponse.json({
      payouts,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching payouts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payouts' },
      { status: 500 }
    );
  }
}
