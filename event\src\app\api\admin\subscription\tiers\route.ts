import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { db } from '@/lib/prisma';

/**
 * GET /api/admin/subscription/tiers
 * Get all subscription tier prices
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    // Only allow admins to access this endpoint
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all subscription tier prices using Prisma model
    const tierPrices = await db.subscriptionTierPrice.findMany({
      select: {
        id: true,
        tier: true,
        monthlyPrice: true,
        yearlyPrice: true,
        commissionRate: true,
        maxEvents: true,
        maxTeamMembers: true,
        maxEmailCampaigns: true,
        maxAnalyticsReports: true,
        maxVendorManagement: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        createdBy: true
      },
      orderBy: {
        tier: 'asc'
      }
    });

    // Sort tiers in the correct order
    const sortedTierPrices = [...tierPrices].sort((a, b) => {
      const tierOrder = { 'NONE': 1, 'BASIC': 2, 'PREMIUM': 3, 'ELITE': 4 };
      return (tierOrder[a.tier as keyof typeof tierOrder] || 5) -
             (tierOrder[b.tier as keyof typeof tierOrder] || 5);
    });

    return NextResponse.json(sortedTierPrices);

  } catch (error) {
    console.error('Error fetching subscription tier prices:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription tier prices' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/subscription/tiers
 * Create or update a subscription tier price
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    // Only allow admins to access this endpoint
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();

    // Validate required fields
    if (!data.tier || !data.monthlyPrice || !data.yearlyPrice || !data.commissionRate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if tier already exists
    const existingTier = await db.subscriptionTierPrice.findUnique({
      where: {
        tier: data.tier
      }
    });

    let result;

    if (existingTier) {
      // Update existing tier
      result = await db.subscriptionTierPrice.update({
        where: {
          tier: data.tier
        },
        data: {
          monthlyPrice: data.monthlyPrice,
          yearlyPrice: data.yearlyPrice,
          commissionRate: data.commissionRate,
          maxEvents: data.maxEvents || null,
          maxTeamMembers: data.maxTeamMembers || null,
          maxEmailCampaigns: data.maxEmailCampaigns || null,
          maxAnalyticsReports: data.maxAnalyticsReports || null,
          maxVendorManagement: data.maxVendorManagement || null,
          isActive: data.isActive !== undefined ? data.isActive : true,
          updatedAt: new Date(),
          createdBy: user.id
        }
      });
    } else {
      // Create new tier
      result = await db.subscriptionTierPrice.create({
        data: {
          tier: data.tier,
          monthlyPrice: data.monthlyPrice,
          yearlyPrice: data.yearlyPrice,
          commissionRate: data.commissionRate,
          maxEvents: data.maxEvents || null,
          maxTeamMembers: data.maxTeamMembers || null,
          maxEmailCampaigns: data.maxEmailCampaigns || null,
          maxAnalyticsReports: data.maxAnalyticsReports || null,
          maxVendorManagement: data.maxVendorManagement || null,
          isActive: data.isActive !== undefined ? data.isActive : true,
          createdBy: user.id
        }
      });
    }

    return NextResponse.json({
      message: existingTier ? 'Subscription tier updated' : 'Subscription tier created',
      data: result
    });

  } catch (error) {
    console.error('Error creating/updating subscription tier price:', error);
    return NextResponse.json(
      { error: 'Failed to create/update subscription tier price' },
      { status: 500 }
    );
  }
}
