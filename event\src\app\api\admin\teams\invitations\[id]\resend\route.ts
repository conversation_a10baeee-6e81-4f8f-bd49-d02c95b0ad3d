import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { sendTeamInvitationEmail } from '@/lib/email';

// POST /api/admin/teams/invitations/[id]/resend - Resend an invitation (admin only)
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
    const resolvedParams = await context.params;

  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const token = resolvedParams.id;

    // Get the invitation
    const invitation = await db.teamInvitation.findUnique({
      where: { token },
      include: {
        team: true,
        invitedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Check if invitation exists
    if (!invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      );
    }

    // Check if invitation is still pending
    if (invitation.status !== 'PENDING') {
      return NextResponse.json(
        { error: 'Invitation is no longer pending' },
        { status: 400 }
      );
    }

    // Update the invitation expiration date
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Expire in 7 days

    const updatedInvitation = await db.teamInvitation.update({
      where: { id: invitation.id },
      data: {
        expiresAt,
      },
    });

    // Send invitation email
    try {
      await sendTeamInvitationEmail(
        invitation.email,
        invitation.team.name,
        user.name || user.email || 'Administrator',
        invitation.role,
        invitation.token
      );
    } catch (emailError) {
      console.error('Failed to resend invitation email:', emailError);
      // Continue with the process even if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Invitation resent successfully',
      invitation: {
        id: updatedInvitation.id,
        email: updatedInvitation.email,
        role: updatedInvitation.role,
        expiresAt: updatedInvitation.expiresAt,
      },
    });
  } catch (error) {
    console.error('Error resending invitation:', error);
    return NextResponse.json(
      { error: 'Failed to resend invitation' },
      { status: 500 }
    );
  }
}
