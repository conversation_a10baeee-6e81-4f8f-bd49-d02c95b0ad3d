import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { UserRole } from '@prisma/client';
import { z } from 'zod';
import { hash } from 'bcryptjs';

// Validation schemas
const createUserSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Valid email is required'),
  role: z.enum(['ADMIN', 'USER', 'ORGANIZER', 'VENDOR', 'SUPERADMIN', 'DEVELOPER', 'PARTNER']),
});

const updateUserSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  role: z.enum(['ADMIN', 'USER', 'ORGANIZER', 'VENDOR', 'SUPERADMIN', 'DEVELOPER', 'PARTNER']).optional(),
  emailVerified: z.boolean().optional(),
});

// Type definitions
interface ApiError {
  error: string;
  details?: string;
}

// Helper function to check admin permissions
async function checkAdminPermissions(currentUserData: any): Promise<boolean> {
  return currentUserData?.role === 'ADMIN' ||
         currentUserData?.role === 'SUPERADMIN' ||
         currentUserData?.role === 'DEVELOPER';
}

// Helper function to log activity
async function logActivity(
  adminId: string,
  action: string,
  resource: string,
  resourceId?: string,
  details?: any,
  success: boolean = true,
  errorMessage?: string,
  ipAddress?: string,
  userAgent?: string
) {
  try {
    await db.activityLog.create({
      data: {
        adminId,
        action,
        resource,
        resourceId,
        details,
        success,
        errorMessage,
        ipAddress,
        userAgent,
      },
    });
  } catch (error) {
    console.error('Failed to log activity:', error);
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' } as ApiError,
        { status: 401 }
      );
    }

    const hasPermission = await checkAdminPermissions(user);
    if (!hasPermission) {
      await logActivity(
        user.id,
        'UNAUTHORIZED_ACCESS_ATTEMPT',
        'users',
        undefined,
        { endpoint: '/api/admin/users' },
        false,
        'Insufficient permissions',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'Insufficient permissions' } as ApiError,
        { status: 403 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const role = searchParams.get('role') || '';
    const verification = searchParams.get('verification') || '';
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortDirection = searchParams.get('sortDirection') || 'desc';

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build where clause
    const whereClause: any = {};

    // Add search filter
    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Add role filter
    if (role && role !== 'all') {
      whereClause.role = role;
    }

    // Add verification filter
    if (verification === 'verified') {
      whereClause.emailVerified = { not: null };
    } else if (verification === 'unverified') {
      whereClause.emailVerified = null;
    }

    // Fetch users with pagination
    const users = await db.user.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
        image: true,
        accountBalance: true,
        // Include related data
        events: {
          select: {
            id: true,
          },
        },
        accounts: {
          select: {
            id: true,
          },
        },
        // Include verification status if available
        organizerVerification: {
          select: {
            status: true,
            verifiedAt: true,
            createdAt: true,
          },
        },
      },
      orderBy: {
        [sortField]: sortDirection,
      },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalUsers = await db.user.count({
      where: whereClause,
    });

    // Transform data to match the expected format
    const transformedUsers = users.map(user => ({
      id: user.id,
      name: user.name || 'Anonymous',
      email: user.email || '',
      role: user.role,
      emailVerified: user.emailVerified ? user.emailVerified.toISOString() : null,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
      image: user.image,
      verificationStatus: user.organizerVerification?.status || null,
      isVerified: user.organizerVerification?.status === 'APPROVED',
      verificationDate: user.organizerVerification?.verifiedAt ? user.organizerVerification.verifiedAt.toISOString() : null,
      verificationSubmittedAt: user.organizerVerification?.createdAt ? user.organizerVerification.createdAt.toISOString() : null,
      accountBalance: user.accountBalance || 0,
      eventsCount: user.events.length,
      accountsCount: user.accounts.length,
    }));

    await logActivity(
      user.id,
      'VIEW_USERS',
      'users',
      undefined,
      { page, limit, search, role, verification, totalUsers },
      true,
      undefined,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      users: transformedUsers,
      pagination: {
        total: totalUsers,
        page,
        limit,
        totalPages: Math.ceil(totalUsers / limit),
        hasNext: page < Math.ceil(totalUsers / limit),
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      } as ApiError,
      { status: 500 }
    );
  }
}

// POST: Create a new user
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' } as ApiError,
        { status: 401 }
      );
    }

    const hasPermission = await checkAdminPermissions(user);
    if (!hasPermission) {
      await logActivity(
        user.id,
        'UNAUTHORIZED_CREATE_USER_ATTEMPT',
        'users',
        undefined,
        { endpoint: '/api/admin/users' },
        false,
        'Insufficient permissions',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'Insufficient permissions' } as ApiError,
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = createUserSchema.parse(body);

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      await logActivity(
        user.id,
        'CREATE_USER_FAILED',
        'users',
        undefined,
        { email: validatedData.email },
        false,
        'User already exists',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'User with this email already exists' } as ApiError,
        { status: 409 }
      );
    }

    // Generate a random password
    const password = Math.random().toString(36).slice(-12) + 'A1!';
    const hashedPassword = await hash(password, 10);

    // Create the user
    const newUser = await db.user.create({
      data: {
        name: validatedData.name,
        email: validatedData.email,
        role: validatedData.role,
        password: hashedPassword,
        accessToken: Math.random().toString(36).slice(-32),
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        emailVerified: true,
        createdAt: true,
      },
    });

    await logActivity(
      user.id,
      'CREATE_USER',
      'users',
      newUser.id,
      {
        name: validatedData.name,
        email: validatedData.email,
        role: validatedData.role
      },
      true,
      undefined,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      user: newUser,
      temporaryPassword: password,
      message: 'User created successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating user:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          details: error.errors.map(e => e.message).join(', ')
        } as ApiError,
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      } as ApiError,
      { status: 500 }
    );
  }
}

// PATCH: Update user details
export async function PATCH(request: NextRequest) {
  try {
    const user = await currentUser();
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' } as ApiError,
        { status: 401 }
      );
    }

    const hasPermission = await checkAdminPermissions(user);
    if (!hasPermission) {
      await logActivity(
        user.id,
        'UNAUTHORIZED_UPDATE_USER_ATTEMPT',
        'users',
        undefined,
        { endpoint: '/api/admin/users' },
        false,
        'Insufficient permissions',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'Insufficient permissions' } as ApiError,
        { status: 403 }
      );
    }

    const body = await request.json();
    const { userId, ...updateData } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' } as ApiError,
        { status: 400 }
      );
    }

    // Validate update data
    const validatedData = updateUserSchema.parse(updateData);

    // Check if user exists
    const existingUser = await db.user.findUnique({
      where: { id: userId },
      select: { id: true, name: true, email: true, role: true },
    });

    if (!existingUser) {
      await logActivity(
        user.id,
        'UPDATE_USER_FAILED',
        'users',
        userId,
        { reason: 'User not found' },
        false,
        'User not found',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'User not found' } as ApiError,
        { status: 404 }
      );
    }

    // Prepare update data
    const updatePayload: any = {};
    if (validatedData.name !== undefined) updatePayload.name = validatedData.name;
    if (validatedData.role !== undefined) updatePayload.role = validatedData.role;
    if (validatedData.emailVerified !== undefined) {
      updatePayload.emailVerified = validatedData.emailVerified ? new Date() : null;
    }

    // Update user
    const updatedUser = await db.user.update({
      where: { id: userId },
      data: updatePayload,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        emailVerified: true,
        updatedAt: true,
      },
    });

    await logActivity(
      user.id,
      'UPDATE_USER',
      'users',
      userId,
      {
        changes: updatePayload,
        previousData: existingUser
      },
      true,
      undefined,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      user: updatedUser,
      message: 'User updated successfully',
    });

  } catch (error) {
    console.error('Error updating user:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          details: error.errors.map(e => e.message).join(', ')
        } as ApiError,
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      } as ApiError,
      { status: 500 }
    );
  }
}

// DELETE: Delete a user
export async function DELETE(request: NextRequest) {
  try {
    const user = await currentUser();
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    if (!user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' } as ApiError,
        { status: 401 }
      );
    }

    const hasPermission = await checkAdminPermissions(user);
    if (!hasPermission) {
      await logActivity(
        user.id,
        'UNAUTHORIZED_DELETE_USER_ATTEMPT',
        'users',
        undefined,
        { endpoint: '/api/admin/users' },
        false,
        'Insufficient permissions',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'Insufficient permissions' } as ApiError,
        { status: 403 }
      );
    }

    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' } as ApiError,
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await db.user.findUnique({
      where: { id: userId },
      select: { id: true, name: true, email: true, role: true },
    });

    if (!existingUser) {
      await logActivity(
        user.id,
        'DELETE_USER_FAILED',
        'users',
        userId,
        { reason: 'User not found' },
        false,
        'User not found',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'User not found' } as ApiError,
        { status: 404 }
      );
    }

    // Prevent deleting yourself
    if (userId === user.id) {
      await logActivity(
        user.id,
        'DELETE_USER_FAILED',
        'users',
        userId,
        { reason: 'Attempted to delete own account' },
        false,
        'Cannot delete own account',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'You cannot delete your own account' } as ApiError,
        { status: 400 }
      );
    }

    // Prevent deleting SUPERADMIN users unless you are SUPERADMIN
    if (existingUser.role === 'SUPERADMIN' && user.role !== 'SUPERADMIN') {
      await logActivity(
        user.id,
        'DELETE_USER_FAILED',
        'users',
        userId,
        { reason: 'Attempted to delete SUPERADMIN without permission' },
        false,
        'Cannot delete SUPERADMIN user',
        ipAddress,
        userAgent
      );
      return NextResponse.json(
        { error: 'You cannot delete SUPERADMIN users' } as ApiError,
        { status: 403 }
      );
    }

    // Delete user
    await db.user.delete({
      where: { id: userId },
    });

    await logActivity(
      user.id,
      'DELETE_USER',
      'users',
      userId,
      {
        deletedUser: existingUser
      },
      true,
      undefined,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      message: 'User deleted successfully',
    });

  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      } as ApiError,
      { status: 500 }
    );
  }
}
