import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { getSession } from '@/auth';
import { sendVerificationApprovedEmail } from '@/lib/mail';

export async function POST(
  request: NextRequest
) {
  try {
    // Extract the ID from the URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.indexOf('verifications') + 1];

    const session = await getSession();

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can approve verifications
    if (!session.user?.role || (session.user.role !== 'ADMIN' && session.user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get verification with user info
    const existingVerification = await db.organizerVerification.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!existingVerification) {
      return NextResponse.json({ error: 'Verification not found' }, { status: 404 });
    }

    // Update verification status to APPROVED
    const verification = await db.organizerVerification.update({
      where: { id },
      data: {
        status: 'APPROVED',
        verifiedAt: new Date()
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Send email notification
    if (verification.user.email) {
      try {
        await sendVerificationApprovedEmail(
          verification.user.email,
          verification.businessName
        );
      } catch (emailError) {
        console.error('Error sending approval email:', emailError);
        // Continue even if email fails
      }
    }

    // Update user role to ORGANIZER if it's not already
    if (existingVerification.user.id) {
      await db.user.update({
        where: { id: existingVerification.user.id },
        data: { role: 'ORGANIZER' },
      });
    }

    return NextResponse.json({ success: true, verification });
  } catch (error) {
    console.error('Error approving verification:', error);
    return NextResponse.json(
      { error: 'Failed to approve verification' },
      { status: 500 }
    );
  }
}
