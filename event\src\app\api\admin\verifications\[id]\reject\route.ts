import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { getSession } from '@/auth';
import { sendVerificationRejectedEmail } from '@/lib/mail';

export async function POST(
  request: NextRequest
) {
  try {
    // Extract the ID from the URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.indexOf('verifications') + 1];

    const session = await getSession();

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can reject verifications
    if (!session.user?.role || (session.user.role !== 'ADMIN' && session.user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get rejection reason from request body
    const { reason } = await request.json();

    // Get verification with user info
    const existingVerification = await db.organizerVerification.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!existingVerification) {
      return NextResponse.json({ error: 'Verification not found' }, { status: 404 });
    }

    // Update verification status to REJECTED
    const verification = await db.organizerVerification.update({
      where: { id },
      data: {
        status: 'REJECTED',
        rejectionReason: reason || 'Rejected by admin'
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Send email notification
    if (verification.user.email) {
      try {
        await sendVerificationRejectedEmail(
          verification.user.email,
          verification.businessName,
          reason || 'Rejected by admin'
        );
      } catch (emailError) {
        console.error('Error sending rejection email:', emailError);
        // Continue even if email fails
      }
    }

    return NextResponse.json({ success: true, verification });
  } catch (error) {
    console.error('Error rejecting verification:', error);
    return NextResponse.json(
      { error: 'Failed to reject verification' },
      { status: 500 }
    );
  }
}
