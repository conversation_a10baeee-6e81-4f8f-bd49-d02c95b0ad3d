import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { Prisma } from '@prisma/client';

// Define the valid verification status values
type VerificationStatus = 'PENDING' | 'APPROVED' | 'REJECTED';

export async function GET(request: NextRequest) {
  try {
    // Get current user and check if admin
    const user = await currentUser();
    if (!user?.id || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const statusParam = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build query
    let where: Prisma.OrganizerVerificationWhereInput = {};

    // Only add status filter if it's a valid status and not 'ALL'
    if (statusParam && statusParam !== 'ALL') {
      // Validate that the status is one of the allowed values
      if (['PENDING', 'APPROVED', 'REJECTED'].includes(statusParam)) {
        where.status = statusParam as VerificationStatus;
      }
    }

    // Get verifications
    const verifications = await db.organizerVerification.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const total = await db.organizerVerification.count({ where });

    return NextResponse.json({
      verifications,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching verifications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
