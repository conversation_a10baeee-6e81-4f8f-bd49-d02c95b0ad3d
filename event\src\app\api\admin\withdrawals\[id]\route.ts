import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { createPayout } from '@/lib/payment-providers/stripe';

/**
 * PATCH /api/admin/withdrawals/:id
 * Approve or reject a withdrawal request
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      return NextResponse.json(
        { error: 'You do not have permission to perform this action' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const withdrawalId = resolvedParams.id;
    const { action, notes } = await req.json();

    // Validate the action
    if (!action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "approve" or "reject".' },
        { status: 400 }
      );
    }

    // Get the withdrawal request
    const withdrawal = await db.withdrawal.findUnique({
      where: { id: withdrawalId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            accountBalance: true,
          },
        },
        bankAccount: true,
      },
    });

    if (!withdrawal) {
      return NextResponse.json(
        { error: 'Withdrawal request not found' },
        { status: 404 }
      );
    }

    // Check if the withdrawal is already processed
    if (withdrawal.status !== 'Pending') {
      return NextResponse.json(
        { error: `Withdrawal is already ${withdrawal.status.toLowerCase()}` },
        { status: 400 }
      );
    }

    if (action === 'approve') {
      // Check if user still has sufficient balance
      if (withdrawal.user && withdrawal.user.accountBalance < withdrawal.amount) {
        return NextResponse.json(
          { error: 'User has insufficient balance for this withdrawal' },
          { status: 400 }
        );
      }

      // Process the withdrawal
      let payoutResult = null;

      // If user has a Stripe connected account, use Stripe for payout
      // Stripe payout logic removed: stripeConnectedAccountId not present in user model

      // Update the withdrawal status
      await db.withdrawal.update({
        where: { id: withdrawalId },
        data: {
          status: 'Approved',
          processedDate: new Date(),
          adminNotes: notes,
        },
      });

      // Update user's account balance
      await db.user.update({
        where: { id: withdrawal.userId },
        data: {
          accountBalance: { decrement: withdrawal.amount },
        },
      });

      // Record the transaction
      await db.financialTransaction.create({
        data: {
          userId: withdrawal.userId,
          amount: -withdrawal.amount, // Negative amount for outgoing funds
          type: 'WITHDRAWAL',
          status: 'COMPLETED',
        },
      });

      // Create a notification for the user
      await db.notification.create({
        data: {
          userId: withdrawal.userId,
          type: 'WITHDRAWAL_APPROVED',
          message: `Your withdrawal request of ${withdrawal.amount.toFixed(2)} has been approved`,
        },
      });

      return NextResponse.json({
        message: 'Withdrawal request approved successfully',
        withdrawal: {
          ...withdrawal,
          status: 'Approved',
          processedDate: new Date(),
        },
      });
    } else {
      // Reject the withdrawal
      await db.withdrawal.update({
        where: { id: withdrawalId },
        data: {
          status: 'Rejected',
          processedDate: new Date(),
          adminNotes: notes,
        },
      });

      // Create a notification for the user
      await db.notification.create({
        data: {
          userId: withdrawal.userId,
          type: 'WITHDRAWAL_REJECTED',
          message: `Your withdrawal request of ${withdrawal.amount.toFixed(2)} has been rejected`,
        },
      });

      return NextResponse.json({
        message: 'Withdrawal request rejected successfully',
        withdrawal: {
          ...withdrawal,
          status: 'Rejected',
          processedDate: new Date(),
        },
      });
    }
  } catch (error) {
    console.error('Error processing withdrawal action:', error);
    return NextResponse.json(
      { error: 'Failed to process withdrawal action' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/withdrawals/:id
 * Get details of a specific withdrawal request
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is an admin or the withdrawal owner
    if (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      // For non-admins, we'll check ownership in the query
    }

    const resolvedParams = await params;
    const withdrawalId = resolvedParams.id;

    // Build the where clause
    const where: any = { id: withdrawalId };

    // If not admin, only allow access to own withdrawals
    if (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      where.userId = user.id;
    }

    // Get the withdrawal request
    const withdrawal = await db.withdrawal.findUnique({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            image: true,
          },
        },
        bankAccount: {
          select: {
            bankName: true,
            accountNumber: true,
            accountName: true,
            routingNumber: true,
            branchCode: true,
            swiftCode: true,
          },
        },
      },
    });

    if (!withdrawal) {
      return NextResponse.json(
        { error: 'Withdrawal request not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(withdrawal);
  } catch (error) {
    console.error('Error fetching withdrawal details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch withdrawal details' },
      { status: 500 }
    );
  }
}
