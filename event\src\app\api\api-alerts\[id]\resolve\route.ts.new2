import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function POST(
  req: NextRequest,
  context: any
) {
  try {
    const { params } = context;
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const alertId = params.id;
    
    // Check if alert exists
    const alert = await db.apiAlert.findUnique({
      where: {
        id: alertId,
      },
    });
    
    if (!alert) {
      return NextResponse.json(
        { error: 'Alert not found' },
        { status: 404 }
      );
    }
    
    // Check if alert is already resolved
    if (alert.resolved) {
      return NextResponse.json(
        { error: 'Alert is already resolved' },
        { status: 400 }
      );
    }
    
    // Get request body
    const body = await req.json();
    
    // Update alert
    const updatedAlert = await db.apiAlert.update({
      where: {
        id: alertId,
      },
      data: {
        resolved: true,
        resolvedAt: new Date(),
        resolvedBy: user.id,
        resolution: body.resolution || null,
      },
    });

    return NextResponse.json(updatedAlert);
  } catch (error) {
    console.error('Error in POST /api/api-alerts/[id]/resolve:', error);
    return NextResponse.json(
      { error: 'Failed to resolve API alert' },
      { status: 500 }
    );
  }
}
