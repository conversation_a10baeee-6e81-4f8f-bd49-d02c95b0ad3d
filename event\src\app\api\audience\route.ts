// app/api/audience/route.ts
import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

export async function GET() {
  try {
    const data = await db.audienceData.findMany({
      orderBy: {
        age: 'asc',
      },
    });

    return NextResponse.json({
      data,
      message: 'Audience data retrieved successfully',
      status: 200,
    });
  } catch (error) {
    console.error('Error fetching audience data:', error);
    return NextResponse.json(
      { message: 'Internal server error', status: 500 },
      { status: 500 }
    );
  }
}