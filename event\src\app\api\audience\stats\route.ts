import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";
import { currentUser } from "@/lib/auth";

// GET: Retrieve audience statistics
export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get total subscribers
    const totalSubscribers = await db.newsletterSubscriber.count({
      where: {
        organizerId: user.id,
      },
    });

    // Get active subscribers (those with ACTIVE status)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const activeSubscribers = await db.newsletterSubscriber.count({
      where: {
        organizerId: user.id,
        status: 'ACTIVE',
      },
    });

    // Note: In the future, we can track lastOpenedAt to get more accurate active subscriber counts

    // Get new subscribers in the last 30 days
    const newSubscribers = await db.newsletterSubscriber.count({
      where: {
        organizerId: user.id,
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
    });

    // Calculate growth rate
    const previousPeriodStart = new Date();
    previousPeriodStart.setDate(previousPeriodStart.getDate() - 60);

    const previousPeriodEnd = new Date();
    previousPeriodEnd.setDate(previousPeriodEnd.getDate() - 30);

    const previousPeriodSubscribers = await db.newsletterSubscriber.count({
      where: {
        organizerId: user.id,
        createdAt: {
          gte: previousPeriodStart,
          lt: previousPeriodEnd,
        },
      },
    });

    // Calculate growth percentage
    const growthRate = previousPeriodSubscribers > 0
      ? ((newSubscribers - previousPeriodSubscribers) / previousPeriodSubscribers) * 100
      : newSubscribers > 0 ? 100 : 0;

    // Get email open and click rates from recent campaigns
    const recentCampaigns = await db.marketingCampaign.findMany({
      where: {
        userId: user.id!,
        status: 'SENT',
        sentAt: {
          gte: thirtyDaysAgo,
        },
      },
      include: {
        recipients: {
          select: {
            openedAt: true,
            clickedAt: true,
          },
        },
      },
    });

    // Calculate open and click rates
    let totalRecipients = 0;
    let totalOpens = 0;
    let totalClicks = 0;

    recentCampaigns.forEach(campaign => {
      const recipients = campaign.recipients.length;
      const opens = campaign.recipients.filter(r => r.openedAt).length;
      const clicks = campaign.recipients.filter(r => r.clickedAt).length;

      totalRecipients += recipients;
      totalOpens += opens;
      totalClicks += clicks;
    });

    const openRate = totalRecipients > 0 ? (totalOpens / totalRecipients) * 100 : 0;
    const clickRate = totalRecipients > 0 ? (totalClicks / totalRecipients) * 100 : 0;

    return NextResponse.json({
      total: totalSubscribers,
      active: activeSubscribers,
      growth: parseFloat(growthRate.toFixed(1)),
      openRate: parseFloat(openRate.toFixed(1)),
      clickRate: parseFloat(clickRate.toFixed(1)),
    });
  } catch (error) {
    console.error('Error fetching audience stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch audience stats' },
      { status: 500 }
    );
  }
}
