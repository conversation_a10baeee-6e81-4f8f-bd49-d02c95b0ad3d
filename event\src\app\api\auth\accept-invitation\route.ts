import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { hash } from 'bcryptjs';
import { z } from 'zod';

// Validation schema
const acceptInvitationSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  newPassword: z.string().min(8, 'Password must be at least 8 characters'),
});

// Type definitions
interface ApiError {
  error: string;
  details?: string;
}

// Helper function to log activity
async function logActivity(
  userId: string,
  action: string,
  resource: string,
  resourceId?: string,
  details?: any,
  success: boolean = true,
  errorMessage?: string,
  ipAddress?: string,
  userAgent?: string
) {
  try {
    await db.activityLog.create({
      data: {
        userId,
        action,
        resource,
        resourceId,
        details,
        success,
        errorMessage,
        ipAddress,
        userAgent,
      },
    });
  } catch (error) {
    console.error('Failed to log activity:', error);
  }
}

// POST: Accept invitation and create user account
export async function POST(request: NextRequest) {
  try {
    const ipAddress = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    const body = await request.json();
    const validatedData = acceptInvitationSchema.parse(body);

    // Find the invitation
    const invitation = await db.userInvitation.findUnique({
      where: { token: validatedData.token },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: 'Invalid or expired invitation token' } as ApiError,
        { status: 404 }
      );
    }

    // Check if invitation is still valid
    if (invitation.status !== 'PENDING') {
      return NextResponse.json(
        { error: 'This invitation has already been used or expired' } as ApiError,
        { status: 400 }
      );
    }

    if (invitation.expiresAt < new Date()) {
      // Mark invitation as expired
      await db.userInvitation.update({
        where: { id: invitation.id },
        data: { status: 'EXPIRED' },
      });

      return NextResponse.json(
        { error: 'This invitation has expired' } as ApiError,
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email: invitation.email },
    });

    if (existingUser) {
      // Mark invitation as used
      await db.userInvitation.update({
        where: { id: invitation.id },
        data: { 
          status: 'ACCEPTED',
          acceptedAt: new Date(),
        },
      });

      return NextResponse.json(
        { error: 'A user with this email already exists. Please try logging in instead.' } as ApiError,
        { status: 409 }
      );
    }

    // Hash the new password
    const hashedPassword = await hash(validatedData.newPassword, 10);

    // Create the user account
    const newUser = await db.user.create({
      data: {
        name: invitation.email.split('@')[0], // Default name from email
        email: invitation.email,
        role: invitation.role,
        password: hashedPassword,
        emailVerified: new Date(), // Auto-verify since they accepted invitation
        accessToken: Math.random().toString(36).slice(-32),
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        emailVerified: true,
        createdAt: true,
      },
    });

    // Mark invitation as accepted
    await db.userInvitation.update({
      where: { id: invitation.id },
      data: { 
        status: 'ACCEPTED',
        acceptedAt: new Date(),
      },
    });

    // Log the activity
    await logActivity(
      newUser.id,
      'ACCEPT_INVITATION',
      'users',
      newUser.id,
      { 
        invitationId: invitation.id,
        role: invitation.role,
        invitedBy: invitation.invitedBy
      },
      true,
      undefined,
      ipAddress,
      userAgent
    );

    return NextResponse.json({
      message: 'Account created successfully! You can now log in with your new password.',
      user: {
        id: newUser.id,
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
      },
    }, { status: 201 });

  } catch (error) {
    console.error('Error accepting invitation:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation error',
          details: error.errors.map(e => e.message).join(', ')
        } as ApiError,
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      } as ApiError,
      { status: 500 }
    );
  }
}

// GET: Validate invitation token
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' } as ApiError,
        { status: 400 }
      );
    }

    // Find the invitation
    const invitation = await db.userInvitation.findUnique({
      where: { token },
      select: {
        id: true,
        email: true,
        role: true,
        status: true,
        expiresAt: true,
        createdAt: true,
      },
    });

    if (!invitation) {
      return NextResponse.json(
        { error: 'Invalid invitation token' } as ApiError,
        { status: 404 }
      );
    }

    // Check if invitation is still valid
    if (invitation.status !== 'PENDING') {
      return NextResponse.json(
        { 
          error: 'This invitation has already been used or expired',
          invitation: {
            status: invitation.status,
            email: invitation.email,
          }
        } as ApiError,
        { status: 400 }
      );
    }

    if (invitation.expiresAt < new Date()) {
      // Mark invitation as expired
      await db.userInvitation.update({
        where: { id: invitation.id },
        data: { status: 'EXPIRED' },
      });

      return NextResponse.json(
        { 
          error: 'This invitation has expired',
          invitation: {
            status: 'EXPIRED',
            email: invitation.email,
            expiresAt: invitation.expiresAt,
          }
        } as ApiError,
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email: invitation.email },
      select: { id: true, email: true },
    });

    if (existingUser) {
      return NextResponse.json(
        { 
          error: 'A user with this email already exists',
          suggestion: 'Please try logging in instead'
        } as ApiError,
        { status: 409 }
      );
    }

    return NextResponse.json({
      valid: true,
      invitation: {
        email: invitation.email,
        role: invitation.role,
        expiresAt: invitation.expiresAt,
        createdAt: invitation.createdAt,
      },
    });

  } catch (error) {
    console.error('Error validating invitation:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      } as ApiError,
      { status: 500 }
    );
  }
}
