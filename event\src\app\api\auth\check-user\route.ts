import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { db } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    console.log('Check User - Starting request');
    
    // Get the authenticated user from the session
    const user = await currentUser();
    console.log('Check User - Current user from session:', user ? { id: user.id, role: user.role } : 'Not authenticated');
    
    if (!user?.id) {
      console.log('Check User - No user in session');
      return NextResponse.json({ 
        authenticated: false,
        message: 'Not authenticated',
        sessionUser: null,
        dbUser: null
      });
    }
    
    // Check if the user exists in the database
    try {
      const dbUser = await db.user.findUnique({
        where: { id: user.id },
        select: { 
          id: true, 
          email: true,
          name: true,
          role: true,
          emailVerified: true,
          createdAt: true
        }
      });
      
      console.log('Check User - Database user:', dbUser ? 'Found' : 'Not found');
      
      return NextResponse.json({
        authenticated: true,
        message: dbUser ? 'User found in database' : 'User not found in database',
        sessionUser: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        },
        dbUser
      });
    } catch (dbError) {
      console.error('Check User - Database error:', dbError);
      return NextResponse.json({
        authenticated: true,
        message: 'Error checking user in database',
        sessionUser: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        },
        dbUser: null,
        error: dbError instanceof Error ? dbError.message : 'Unknown database error'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Check User - Unexpected error:', error);
    return NextResponse.json({
      authenticated: false,
      message: 'Error checking authentication',
      sessionUser: null,
      dbUser: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
