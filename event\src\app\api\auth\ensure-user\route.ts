import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { db } from '@/lib/prisma';

/**
 * API endpoint to ensure the current user exists in the database
 * This is a more direct approach than the sync-user endpoint
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current user from the session
    const user = await currentUser();
    console.log('Ensure User - Current user:', user ? { id: user.id, email: user.email, role: user.role } : 'Not authenticated');

    // If there's no authenticated user, return an error
    if (!user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Not authenticated',
      }, { status: 401 });
    }

    // Check if the user exists in the database
    let dbUser = await db.user.findUnique({
      where: { id: user.id },
    });
    console.log('Ensure User - Database user:', dbUser ? 'Found' : 'Not found');

    // If the user doesn't exist in the database by ID, check if they exist by email
    if (!dbUser && user.email) {
      // Check if a user with the same email already exists
      const existingUserByEmail = await db.user.findUnique({
        where: { email: user.email },
      });

      if (existingUserByEmail) {
        console.log(`Ensure User - User with email ${user.email} already exists with ID ${existingUserByEmail.id}, but session has ID ${user.id}`);

        try {
          // Update the existing user's accounts to link them to the session user
          await db.account.updateMany({
            where: { userId: existingUserByEmail.id },
            data: { userId: user.id },
          });

          // Update any related records (like NFCTerminalSettings) to the new user ID
          try {
            await db.nFCTerminalSettings.updateMany({
              where: { vendorId: existingUserByEmail.id },
              data: { vendorId: user.id },
            });
            console.log(`Ensure User - Updated NFCTerminalSettings from user ${existingUserByEmail.id} to ${user.id}`);
          } catch (settingsError) {
            console.error('Ensure User - Error updating NFCTerminalSettings:', settingsError);
            // Continue even if this fails
          }

          // Delete the existing user
          await db.user.delete({
            where: { id: existingUserByEmail.id },
          });

          console.log(`Ensure User - Deleted user with ID ${existingUserByEmail.id}`);

          // Now create the new user with the session ID
          dbUser = await db.user.create({
            data: {
              id: user.id,
              name: user.name || existingUserByEmail.name || 'User',
              email: user.email,
              role: user.role || existingUserByEmail.role || 'VENDOR',
              emailVerified: existingUserByEmail.emailVerified || new Date(),
              image: user.image || existingUserByEmail.image,
            },
          });

          console.log(`Ensure User - Created new user with ID ${dbUser.id} to replace ${existingUserByEmail.id}`);

          return NextResponse.json({
            success: true,
            message: 'User account migrated successfully',
            user: {
              id: dbUser.id,
              email: dbUser.email,
              name: dbUser.name,
              role: dbUser.role,
            },
            migrated: true,
          });
        } catch (migrationError) {
          console.error('Ensure User - Error migrating user:', migrationError);

          // If migration fails, try a simpler approach - just create a new user with a modified email
          try {
            const modifiedEmail = `${user.email.split('@')[0]}+${Date.now()}@${user.email.split('@')[1]}`;

            dbUser = await db.user.create({
              data: {
                id: user.id,
                name: user.name || 'User',
                email: modifiedEmail,
                role: user.role || 'VENDOR',
                emailVerified: new Date(),
              },
            });

            console.log(`Ensure User - Created user with modified email: ${modifiedEmail}`);

            return NextResponse.json({
              success: true,
              message: 'User created with modified email due to conflict',
              user: {
                id: dbUser.id,
                email: dbUser.email,
                name: dbUser.name,
                role: dbUser.role,
              },
              created: true,
              emailModified: true,
            });
          } catch (createError) {
            console.error('Ensure User - Error creating user with modified email:', createError);

            return NextResponse.json({
              success: false,
              message: 'Failed to resolve email conflict',
              error: migrationError instanceof Error ? migrationError.message : 'Unknown error',
            }, { status: 409 });
          }
        }
      } else {
        // No user with this email exists, so create a new user
        try {
          dbUser = await db.user.create({
            data: {
              id: user.id,
              name: user.name || 'User',
              email: user.email,
              role: user.role || 'VENDOR', // Set role to VENDOR for NFC settings
              emailVerified: new Date(),
            },
          });

          console.log('Ensure User - User created successfully:', dbUser.id);

          return NextResponse.json({
            success: true,
            message: 'User created successfully',
            user: {
              id: dbUser.id,
              email: dbUser.email,
              name: dbUser.name,
              role: dbUser.role,
            },
            created: true,
          });
        } catch (error) {
          console.error('Ensure User - Error creating user:', error);

          return NextResponse.json({
            success: false,
            message: 'Failed to create user',
            error: error instanceof Error ? error.message : 'Unknown error',
          }, { status: 500 });
        }
      }
    } else if (!dbUser) {
      // No email provided, create user with generated email
      try {
        const generatedEmail = `user-${Date.now()}@example.com`;

        dbUser = await db.user.create({
          data: {
            id: user.id,
            name: user.name || 'User',
            email: generatedEmail,
            role: user.role || 'VENDOR',
            emailVerified: new Date(),
          },
        });

        console.log(`Ensure User - User created with generated email: ${generatedEmail}`);

        return NextResponse.json({
          success: true,
          message: 'User created with generated email',
          user: {
            id: dbUser.id,
            email: dbUser.email,
            name: dbUser.name,
            role: dbUser.role,
          },
          created: true,
          emailGenerated: true,
        });
      } catch (error) {
        console.error('Ensure User - Error creating user with generated email:', error);

        return NextResponse.json({
          success: false,
          message: 'Failed to create user',
          error: error instanceof Error ? error.message : 'Unknown error',
        }, { status: 500 });
      }
    }

    // User exists in the database
    return NextResponse.json({
      success: true,
      message: 'User already exists in database',
      user: {
        id: dbUser.id,
        email: dbUser.email,
        name: dbUser.name,
        role: dbUser.role,
      },
      created: false,
    });
  } catch (error) {
    console.error('Ensure User - Unexpected error:', error);

    return NextResponse.json({
      success: false,
      message: 'Unexpected error',
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
