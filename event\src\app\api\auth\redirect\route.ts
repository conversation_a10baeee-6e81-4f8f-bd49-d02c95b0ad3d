import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/auth';

export async function GET(request: NextRequest) {
  try {
    // Get the user's session
    const session = await getSession();

    console.log("Auth redirect route called, user role:", session?.user?.role);

    // Default redirect path for users without roles
    let redirectPath = '/auth/select-role';

    // Check if the user has a role and redirect accordingly
    if (session?.user?.role) {
      switch (session.user.role) {
        case 'ADMIN':
          redirectPath = '/admin/dashboard';
          break;
        case 'USER':
          redirectPath = '/dashboard/user';
          break;
        case 'VENDOR':
          redirectPath = '/dashboard/vendor';
          break;
        case 'ORGANIZER':
          redirectPath = '/dashboard/organizer';
          break;
        case 'SUPERADMIN':
          redirectPath = '/admin/dashboard';
          break;
        case 'DEVELOPER':
          redirectPath = '/dashboard/developer';
          break;
        case 'PARTNER':
          console.log("Redirecting PARTNER to /dashboard/partner");
          redirectPath = '/dashboard/partner';
          break;
        default:
          // If user has unrecognized role, redirect to role selection
          redirectPath = '/auth/select-role';
          break;
      }
    } else if (!session?.user) {
      // If no user session, redirect to login
      redirectPath = '/auth/login';
    }

    // Log the redirect path
    console.log("Redirecting to:", redirectPath);

    // Redirect to the appropriate dashboard
    return NextResponse.redirect(new URL(redirectPath, request.url));
  } catch (error) {
    console.error('Error in redirect route:', error);
    // If there's an error, redirect to login
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }
}
