/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow strict
 */

import type {
  Platform as PlatformType,
  PlatformSelectSpec,
} from './PlatformTypes';

// This is a fallback Platform.js file that Metro bundler can resolve
// The actual platform-specific implementations are in Platform.ios.js and Platform.android.js

const Platform: PlatformType = {
  __constants: null,
  OS: 'web', // Default to web for fallback
  get Version(): string | number {
    return '1.0.0';
  },
  get constants(): any {
    if (this.__constants == null) {
      this.__constants = {
        isTesting: false,
        reactNativeVersion: {
          major: 0,
          minor: 79,
          patch: 5,
          prerelease: null,
        },
      };
    }
    return this.__constants;
  },
  get isTesting(): boolean {
    return false;
  },
  get isDisableAnimations(): boolean {
    return false;
  },
  get isTV(): boolean {
    return false;
  },
  get isVision(): boolean {
    return false;
  },
  select: <T>(spec: PlatformSelectSpec<T>): T =>
    'web' in spec
      ? spec.web
      : 'default' in spec
        ? spec.default
        : spec.native || spec.default,
};

export default Platform;
